"""
Model Repair Utility
Downloads and fixes missing model components for image generation models.
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, List, Optional
import subprocess
import sys

def install_huggingface_hub():
    """Install huggingface_hub if not available."""
    try:
        import huggingface_hub
        return True
    except ImportError:
        print("Installing huggingface_hub...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "huggingface_hub"])
            import huggingface_hub
            return True
        except Exception as e:
            print(f"Failed to install huggingface_hub: {e}")
            return False

def download_model_component(repo_id: str, component_path: str, local_dir: Path, subfolder: str = None):
    """Download a specific component from a HuggingFace model."""
    if not install_huggingface_hub():
        return False
        
    try:
        from huggingface_hub import snapshot_download
        
        print(f"Downloading {component_path} from {repo_id}...")
        
        # Create local directory if it doesn't exist
        local_dir.mkdir(parents=True, exist_ok=True)
        
        # Download the component
        if subfolder:
            snapshot_download(
                repo_id=repo_id,
                local_dir=str(local_dir),
                allow_patterns=[f"{subfolder}/*"],
                local_dir_use_symlinks=False
            )
        else:
            snapshot_download(
                repo_id=repo_id,
                local_dir=str(local_dir),
                local_dir_use_symlinks=False
            )
            
        print(f"Successfully downloaded {component_path}")
        return True
        
    except Exception as e:
        print(f"Failed to download {component_path}: {e}")
        return False

def repair_stable_diffusion_v1_5(models_path: Path):
    """Repair Stable Diffusion v1.5 model."""
    model_path = models_path / "stable-diffusion-v1-5"
    
    print("Repairing Stable Diffusion v1.5...")
    
    # Check for missing VAE
    vae_path = model_path / "vae"
    if not vae_path.exists():
        print("Missing VAE component, downloading...")
        if download_model_component(
            "runwayml/stable-diffusion-v1-5",
            "vae",
            vae_path,
            "vae"
        ):
            print("VAE component downloaded successfully")
        else:
            print("Failed to download VAE component")
            return False
    
    return True

def repair_stable_diffusion_2_1(models_path: Path):
    """Repair Stable Diffusion 2.1 model."""
    model_path = models_path / "stable-diffusion-2-1"
    
    print("Repairing Stable Diffusion 2.1...")
    
    # Check for missing VAE
    vae_path = model_path / "vae"
    if not vae_path.exists():
        print("Missing VAE component, downloading...")
        if download_model_component(
            "stabilityai/stable-diffusion-2-1",
            "vae",
            vae_path,
            "vae"
        ):
            print("VAE component downloaded successfully")
        else:
            print("Failed to download VAE component")
            return False
    
    return True

def validate_model_structure(model_path: Path, required_components: List[str]) -> List[str]:
    """Validate model structure and return list of missing components."""
    missing = []
    
    for component in required_components:
        component_path = model_path / component
        if not component_path.exists():
            missing.append(component)
        elif component_path.is_dir():
            # Check if directory has any files
            if not any(component_path.iterdir()):
                missing.append(component)
                
    return missing

def repair_all_models(models_path: Path):
    """Repair all models in the models directory."""
    models_path = Path(models_path)
    
    if not models_path.exists():
        print(f"Models directory not found: {models_path}")
        return False
        
    print(f"Starting model repair in: {models_path}")
    
    # Model repair configurations
    repair_configs = {
        "stable-diffusion-v1-5": {
            "required_components": ["scheduler", "text_encoder", "tokenizer", "unet", "vae", "safety_checker", "feature_extractor"],
            "repair_function": repair_stable_diffusion_v1_5
        },
        "stable-diffusion-2-1": {
            "required_components": ["scheduler", "text_encoder", "tokenizer", "unet", "vae", "feature_extractor"],
            "repair_function": repair_stable_diffusion_2_1
        }
    }
    
    success_count = 0
    total_count = 0
    
    for model_name, config in repair_configs.items():
        model_path = models_path / model_name
        
        if not model_path.exists():
            print(f"Model directory not found: {model_name}")
            continue
            
        total_count += 1
        print(f"\n--- Checking {model_name} ---")
        
        # Validate model structure
        missing_components = validate_model_structure(model_path, config["required_components"])
        
        if not missing_components:
            print(f"{model_name} is complete")
            success_count += 1
            continue
            
        print(f"Missing components in {model_name}: {missing_components}")
        
        # Attempt repair
        try:
            if config["repair_function"](models_path):
                print(f"Successfully repaired {model_name}")
                success_count += 1
            else:
                print(f"Failed to repair {model_name}")
        except Exception as e:
            print(f"Error repairing {model_name}: {e}")
    
    print(f"\n--- Repair Summary ---")
    print(f"Successfully repaired: {success_count}/{total_count} models")
    
    return success_count == total_count

def create_missing_vae_configs():
    """Create missing VAE config files for models that need them."""
    # This is a fallback if downloading fails
    vae_config = {
        "_class_name": "AutoencoderKL",
        "_diffusers_version": "0.21.0",
        "act_fn": "silu",
        "block_out_channels": [128, 256, 512, 512],
        "down_block_types": ["DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D"],
        "in_channels": 3,
        "latent_channels": 4,
        "layers_per_block": 2,
        "norm_num_groups": 32,
        "out_channels": 3,
        "sample_size": 512,
        "scaling_factor": 0.18215,
        "up_block_types": ["UpDecoderBlock2D", "UpDecoderBlock2D", "UpDecoderBlock2D", "UpDecoderBlock2D"]
    }
    
    return vae_config

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Repair image generation models")
    parser.add_argument("--models-path", type=str, required=True, help="Path to models directory")
    parser.add_argument("--model", type=str, help="Specific model to repair (optional)")
    
    args = parser.parse_args()
    
    models_path = Path(args.models_path)
    
    if args.model:
        # Repair specific model
        if args.model == "stable-diffusion-v1-5":
            success = repair_stable_diffusion_v1_5(models_path)
        elif args.model == "stable-diffusion-2-1":
            success = repair_stable_diffusion_2_1(models_path)
        else:
            print(f"Unknown model: {args.model}")
            success = False
    else:
        # Repair all models
        success = repair_all_models(models_path)
    
    if success:
        print("Model repair completed successfully")
        sys.exit(0)
    else:
        print("Model repair failed")
        sys.exit(1)
