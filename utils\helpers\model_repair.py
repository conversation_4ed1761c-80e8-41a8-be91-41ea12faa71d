"""
Model Repair and Download Utility
Downloads and fixes missing model components for image generation models.
Supports all models defined in the dependency manager.
"""

import json
from pathlib import Path
from typing import Dict, List, Optional
import subprocess
import sys
import time

def install_huggingface_hub():
    """Install huggingface_hub if not available."""
    try:
        import huggingface_hub
        return True
    except ImportError:
        print("Installing huggingface_hub...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "huggingface_hub"])
            import huggingface_hub
            return True
        except Exception as e:
            print(f"Failed to install huggingface_hub: {e}")
            return False

def download_model_component(repo_id: str, component_path: str, local_dir: Path, subfolder: str = None):
    """Download a specific component from a HuggingFace model."""
    if not install_huggingface_hub():
        return False

    try:
        from huggingface_hub import snapshot_download

        print(f"Downloading {component_path} from {repo_id}...")

        # Create local directory if it doesn't exist
        local_dir.mkdir(parents=True, exist_ok=True)

        # Download the component
        if subfolder:
            snapshot_download(
                repo_id=repo_id,
                local_dir=str(local_dir),
                allow_patterns=[f"{subfolder}/*"],
                local_dir_use_symlinks=False
            )
        else:
            snapshot_download(
                repo_id=repo_id,
                local_dir=str(local_dir),
                local_dir_use_symlinks=False
            )

        print(f"Successfully downloaded {component_path}")
        return True

    except Exception as e:
        print(f"Failed to download {component_path}: {e}")
        return False

def download_full_model(repo_id: str, local_dir: Path, model_name: str = None):
    """Download a complete model from HuggingFace."""
    if not install_huggingface_hub():
        return False

    try:
        from huggingface_hub import snapshot_download

        model_display_name = model_name or repo_id.split('/')[-1]
        print(f"Downloading complete model: {model_display_name} from {repo_id}...")

        # Create local directory if it doesn't exist
        local_dir.mkdir(parents=True, exist_ok=True)

        # Download the complete model
        snapshot_download(
            repo_id=repo_id,
            local_dir=str(local_dir),
            local_dir_use_symlinks=False
        )

        print(f"Successfully downloaded {model_display_name}")
        return True

    except Exception as e:
        print(f"Failed to download {model_display_name}: {e}")
        return False

def repair_stable_diffusion_v1_5(models_path: Path):
    """Repair Stable Diffusion v1.5 model."""
    model_path = models_path / "stable-diffusion-v1-5"
    
    print("Repairing Stable Diffusion v1.5...")
    
    # Check for missing VAE
    vae_path = model_path / "vae"
    if not vae_path.exists():
        print("Missing VAE component, downloading...")
        if download_model_component(
            "runwayml/stable-diffusion-v1-5",
            "vae",
            vae_path,
            "vae"
        ):
            print("VAE component downloaded successfully")
        else:
            print("Failed to download VAE component")
            return False
    
    return True

def repair_stable_diffusion_2_1(models_path: Path):
    """Repair Stable Diffusion 2.1 model."""
    model_path = models_path / "stable-diffusion-2-1"
    
    print("Repairing Stable Diffusion 2.1...")
    
    # Check for missing VAE
    vae_path = model_path / "vae"
    if not vae_path.exists():
        print("Missing VAE component, downloading...")
        if download_model_component(
            "stabilityai/stable-diffusion-2-1",
            "vae",
            vae_path,
            "vae"
        ):
            print("VAE component downloaded successfully")
        else:
            print("Failed to download VAE component")
            return False
    
    return True

def validate_model_structure(model_path: Path, required_components: List[str]) -> List[str]:
    """Validate model structure and return list of missing components."""
    missing = []
    
    for component in required_components:
        component_path = model_path / component
        if not component_path.exists():
            missing.append(component)
        elif component_path.is_dir():
            # Check if directory has any files
            if not any(component_path.iterdir()):
                missing.append(component)
                
    return missing

def get_all_models_config():
    """Get configuration for all supported models."""
    return {
        "sdxl-turbo": {
            "repo_id": "stabilityai/sdxl-turbo",
            "local_path": "sdxl-turbo",
            "required": True,
            "description": "SDXL Turbo - Ultra-fast 1-step generation",
            "required_components": ["scheduler", "text_encoder", "text_encoder_2", "tokenizer", "tokenizer_2", "unet", "vae"],
            "repair_function": None  # Uses full download
        },
        "stable-diffusion-xl-base-1.0": {
            "repo_id": "stabilityai/stable-diffusion-xl-base-1.0",
            "local_path": "stable-diffusion-xl-base-1.0",
            "required": False,
            "description": "SDXL Base 1.0 - High quality generation",
            "required_components": ["scheduler", "text_encoder", "text_encoder_2", "tokenizer", "tokenizer_2", "unet", "vae"],
            "repair_function": None  # Uses full download
        },
        "stable-diffusion-xl-refiner-1.0": {
            "repo_id": "stabilityai/stable-diffusion-xl-refiner-1.0",
            "local_path": "stable-diffusion-xl-refiner-1.0",
            "required": False,
            "description": "SDXL Refiner 1.0 - Quality enhancement",
            "required_components": ["scheduler", "text_encoder_2", "tokenizer_2", "unet", "vae"],
            "repair_function": None  # Uses full download
        },
        "fluxDev": {
            "repo_id": "black-forest-labs/FLUX.1-dev",
            "local_path": "fluxDev",
            "required": False,
            "description": "FLUX Dev - High quality advanced generation with guidance scale support",
            "required_components": ["scheduler", "text_encoder", "text_encoder_2", "tokenizer", "tokenizer_2", "transformer", "vae"],
            "repair_function": None  # Uses full download
        },
        "stable-diffusion-v1-5": {
            "repo_id": "runwayml/stable-diffusion-v1-5",
            "local_path": "stable-diffusion-v1-5",
            "required": False,
            "description": "Stable Diffusion v1.5 - Classic model",
            "required_components": ["scheduler", "text_encoder", "tokenizer", "unet", "vae", "safety_checker", "feature_extractor"],
            "repair_function": repair_stable_diffusion_v1_5
        },
        "stable-diffusion-2-1": {
            "repo_id": "stabilityai/stable-diffusion-2-1",
            "local_path": "stable-diffusion-2-1",
            "required": False,
            "description": "Stable Diffusion v2.1 - Improved model",
            "required_components": ["scheduler", "text_encoder", "tokenizer", "unet", "vae", "feature_extractor"],
            "repair_function": repair_stable_diffusion_2_1
        }
    }

def download_all_models(models_path: Path, required_only: bool = False):
    """Download all models defined in the configuration."""
    models_path = Path(models_path)
    models_path.mkdir(parents=True, exist_ok=True)

    print(f"Starting model download in: {models_path}")
    if required_only:
        print("Downloading required models only")
    else:
        print("Downloading all models")
    print()  # Add blank line for better readability

    models_config = get_all_models_config()
    success_count = 0
    total_count = 0

    for model_name, config in models_config.items():
        # Skip non-required models if required_only is True
        if required_only and not config.get("required", False):
            print(f"Skipping optional model: {model_name}")
            continue

        model_path = models_path / config["local_path"]
        total_count += 1

        print(f"\n--- Processing {model_name} ---")
        print(f"Description: {config['description']}")

        # Check if model already exists and is complete
        if model_path.exists():
            missing_components = validate_model_structure(model_path, config["required_components"])
            if not missing_components:
                print(f"{model_name} already exists and is complete")
                success_count += 1
                continue
            else:
                print(f"Model exists but missing components: {missing_components}")

        # Download the model
        print(f"Downloading {model_name}...")
        if download_full_model(config["repo_id"], model_path, model_name):
            print(f"Successfully downloaded {model_name}")
            success_count += 1
        else:
            print(f"Failed to download {model_name}")

    print(f"\n--- Download Summary ---")
    print(f"Successfully downloaded: {success_count}/{total_count} models")

    return success_count == total_count

def repair_all_models(models_path: Path):
    """Repair all models in the models directory."""
    models_path = Path(models_path)

    if not models_path.exists():
        print(f"Models directory not found: {models_path}")
        return False

    print(f"Starting model repair in: {models_path}")

    models_config = get_all_models_config()
    success_count = 0
    total_count = 0

    for model_name, config in models_config.items():
        model_path = models_path / config["local_path"]

        if not model_path.exists():
            print(f"Model directory not found: {model_name}")
            continue

        total_count += 1
        print(f"\n--- Checking {model_name} ---")

        # Validate model structure
        missing_components = validate_model_structure(model_path, config["required_components"])

        if not missing_components:
            print(f"{model_name} is complete")
            success_count += 1
            continue

        print(f"Missing components in {model_name}: {missing_components}")

        # Attempt repair
        try:
            if config["repair_function"]:
                # Use specific repair function
                if config["repair_function"](models_path):
                    print(f"Successfully repaired {model_name}")
                    success_count += 1
                else:
                    print(f"Failed to repair {model_name}")
            else:
                # Use full download as repair
                print(f"Repairing {model_name} by re-downloading...")
                if download_full_model(config["repo_id"], model_path, model_name):
                    print(f"Successfully repaired {model_name}")
                    success_count += 1
                else:
                    print(f"Failed to repair {model_name}")
        except Exception as e:
            print(f"Error repairing {model_name}: {e}")

    print(f"\n--- Repair Summary ---")
    print(f"Successfully repaired: {success_count}/{total_count} models")

    return success_count == total_count

def create_missing_configs(models_path: Path):
    """Create missing config.json files for all models."""
    models_path = Path(models_path)

    print("Creating missing configuration files...")

    # Main model configs
    model_configs = {
        "stable-diffusion-v1-5": {
            "_class_name": "StableDiffusionPipeline",
            "_diffusers_version": "0.21.0",
            "_name_or_path": "runwayml/stable-diffusion-v1-5",
            "requires_safety_checker": True,
            "feature_extractor": ["transformers", "CLIPImageProcessor"],
            "safety_checker": ["stable_diffusion_safety_checker", "StableDiffusionSafetyChecker"],
            "scheduler": ["diffusers", "PNDMScheduler"],
            "text_encoder": ["transformers", "CLIPTextModel"],
            "tokenizer": ["transformers", "CLIPTokenizer"],
            "unet": ["diffusers", "UNet2DConditionModel"],
            "vae": ["diffusers", "AutoencoderKL"]
        },
        "stable-diffusion-2-1": {
            "_class_name": "StableDiffusionPipeline",
            "_diffusers_version": "0.21.0",
            "_name_or_path": "stabilityai/stable-diffusion-2-1",
            "requires_safety_checker": False,
            "feature_extractor": ["transformers", "CLIPImageProcessor"],
            "scheduler": ["diffusers", "DDIMScheduler"],
            "text_encoder": ["transformers", "CLIPTextModel"],
            "tokenizer": ["transformers", "CLIPTokenizer"],
            "unet": ["diffusers", "UNet2DConditionModel"],
            "vae": ["diffusers", "AutoencoderKL"]
        },
        "stable-diffusion-xl-base-1.0": {
            "_class_name": "StableDiffusionXLPipeline",
            "_diffusers_version": "0.21.0",
            "_name_or_path": "stabilityai/stable-diffusion-xl-base-1.0",
            "requires_safety_checker": False,
            "scheduler": ["diffusers", "EulerDiscreteScheduler"],
            "text_encoder": ["transformers", "CLIPTextModel"],
            "text_encoder_2": ["transformers", "CLIPTextModelWithProjection"],
            "tokenizer": ["transformers", "CLIPTokenizer"],
            "tokenizer_2": ["transformers", "CLIPTokenizer"],
            "unet": ["diffusers", "UNet2DConditionModel"],
            "vae": ["diffusers", "AutoencoderKL"]
        },
        "fluxDev": {
            "_class_name": "FluxPipeline",
            "_diffusers_version": "0.30.0",
            "_name_or_path": "black-forest-labs/FLUX.1-dev",
            "requires_safety_checker": False,
            "scheduler": ["diffusers", "FlowMatchEulerDiscreteScheduler"],
            "text_encoder": ["transformers", "CLIPTextModel"],
            "text_encoder_2": ["transformers", "T5EncoderModel"],
            "tokenizer": ["transformers", "CLIPTokenizer"],
            "tokenizer_2": ["transformers", "T5TokenizerFast"],
            "transformer": ["diffusers", "FluxTransformer2DModel"],
            "vae": ["diffusers", "AutoencoderKL"]
        }
    }

    # VAE config template
    vae_config = {
        "_class_name": "AutoencoderKL",
        "_diffusers_version": "0.21.0",
        "act_fn": "silu",
        "block_out_channels": [128, 256, 512, 512],
        "down_block_types": ["DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D"],
        "in_channels": 3,
        "latent_channels": 4,
        "layers_per_block": 2,
        "norm_num_groups": 32,
        "out_channels": 3,
        "sample_size": 512,
        "scaling_factor": 0.18215,
        "up_block_types": ["UpDecoderBlock2D", "UpDecoderBlock2D", "UpDecoderBlock2D", "UpDecoderBlock2D"]
    }

    # Create main model configs
    for model_name, config in model_configs.items():
        model_path = models_path / model_name
        if model_path.exists():
            config_file = model_path / "config.json"
            if not config_file.exists():
                print(f"Creating config.json for {model_name}")
                with open(config_file, 'w') as f:
                    json.dump(config, f, indent=2)

            # Create VAE config if VAE directory exists
            vae_path = model_path / "vae"
            if vae_path.exists():
                vae_config_file = vae_path / "config.json"
                if not vae_config_file.exists():
                    print(f"Creating VAE config.json for {model_name}")
                    # Adjust sample_size for SD 2.1
                    if model_name == "stable-diffusion-2-1":
                        vae_config_copy = vae_config.copy()
                        vae_config_copy["sample_size"] = 768
                        vae_config_copy["_name_or_path"] = "stabilityai/stable-diffusion-2-1"
                    else:
                        vae_config_copy = vae_config.copy()
                        vae_config_copy["_name_or_path"] = config["_name_or_path"]

                    with open(vae_config_file, 'w') as f:
                        json.dump(vae_config_copy, f, indent=2)

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Download and repair image generation models")
    parser.add_argument("--models-path", type=str, required=True, help="Path to models directory")
    parser.add_argument("--model", type=str, help="Specific model to repair (optional)")
    parser.add_argument("--download-all", action="store_true", help="Download all models")
    parser.add_argument("--download-required", action="store_true", help="Download only required models")
    parser.add_argument("--repair-only", action="store_true", help="Only repair existing models, don't download new ones")

    args = parser.parse_args()

    models_path = Path(args.models_path)
    success = False

    if args.download_all:
        # Download all models
        print("=== DOWNLOADING ALL MODELS ===")
        success = download_all_models(models_path, required_only=False)
    elif args.download_required:
        # Download only required models and create configs
        print("=== DOWNLOADING REQUIRED MODELS ===")
        download_success = download_all_models(models_path, required_only=True)

        print("\n=== CREATING MISSING CONFIG FILES ===")
        create_missing_configs(models_path)

        success = download_success
    elif args.model:
        # Repair specific model
        models_config = get_all_models_config()
        if args.model in models_config:
            config = models_config[args.model]
            model_path = models_path / config["local_path"]

            if config["repair_function"]:
                success = config["repair_function"](models_path)
            else:
                success = download_full_model(config["repo_id"], model_path, args.model)
        else:
            print(f"Unknown model: {args.model}")
            print(f"Available models: {', '.join(models_config.keys())}")
            success = False
    elif args.repair_only:
        # Repair existing models only
        print("=== REPAIRING EXISTING MODELS ===")
        success = repair_all_models(models_path)
    else:
        # Default: Download all models and repair existing ones
        print("=== DOWNLOADING ALL MODELS AND REPAIRING EXISTING ===")
        download_success = download_all_models(models_path, required_only=False)
        repair_success = repair_all_models(models_path)

        # Create missing config files
        print("\n=== CREATING MISSING CONFIG FILES ===")
        create_missing_configs(models_path)

        success = download_success and repair_success

    if success:
        print("\nModel operation completed successfully!")
        sys.exit(0)
    else:
        print("\nModel operation failed!")
        sys.exit(1)
