"""
Quick Model Status Check
Checks the current status of all image generation models without downloading.
"""

import json
from pathlib import Path
import sys

def check_model_status(models_path):
    """Check the status of all models without downloading."""
    models_path = Path(models_path)
    
    if not models_path.exists():
        print(f"Models directory not found: {models_path}")
        return
    
    print("=== CURRENT MODEL STATUS ===")
    print(f"Models directory: {models_path}")
    print()
    
    # Models to check
    models = {
        "sdxl-turbo": {
            "path": "sdxl-turbo",
            "required_files": ["config.json", "model_index.json", "scheduler", "text_encoder", "text_encoder_2", "tokenizer", "tokenizer_2", "unet", "vae"]
        },
        "stable-diffusion-xl-base-1.0": {
            "path": "stable-diffusion-xl-base-1.0", 
            "required_files": ["config.json", "model_index.json", "scheduler", "text_encoder", "text_encoder_2", "tokenizer", "tokenizer_2", "unet", "vae"]
        },
        "stable-diffusion-xl-refiner-1.0": {
            "path": "stable-diffusion-xl-refiner-1.0",
            "required_files": ["config.json", "model_index.json", "scheduler", "text_encoder_2", "tokenizer_2", "unet", "vae"]
        },
        "fluxDev": {
            "path": "fluxDev",
            "required_files": ["config.json", "model_index.json", "scheduler", "text_encoder", "text_encoder_2", "tokenizer", "tokenizer_2", "transformer", "vae"]
        },
        "stable-diffusion-v1-5": {
            "path": "stable-diffusion-v1-5",
            "required_files": ["config.json", "model_index.json", "scheduler", "text_encoder", "tokenizer", "unet", "vae", "safety_checker", "feature_extractor"]
        },
        "stable-diffusion-2-1": {
            "path": "stable-diffusion-2-1",
            "required_files": ["config.json", "model_index.json", "scheduler", "text_encoder", "tokenizer", "unet", "vae", "feature_extractor"]
        }
    }
    
    complete_models = []
    incomplete_models = []
    missing_models = []
    
    for model_name, config in models.items():
        model_path = models_path / config["path"]
        
        print(f"--- Checking {model_name} ---")
        
        if not model_path.exists():
            print(f"  Status: MISSING (directory not found)")
            missing_models.append(model_name)
            print()
            continue
            
        missing_files = []
        present_files = []
        
        for required_file in config["required_files"]:
            file_path = model_path / required_file
            if file_path.exists():
                present_files.append(required_file)
            else:
                missing_files.append(required_file)
        
        if not missing_files:
            print(f"  Status: COMPLETE")
            complete_models.append(model_name)
        else:
            print(f"  Status: INCOMPLETE")
            print(f"  Present: {', '.join(present_files)}")
            print(f"  Missing: {', '.join(missing_files)}")
            incomplete_models.append(model_name)
        
        print()
    
    print("=== SUMMARY ===")
    print(f"Complete models: {len(complete_models)}")
    if complete_models:
        for model in complete_models:
            print(f"  ✓ {model}")
    
    print(f"Incomplete models: {len(incomplete_models)}")
    if incomplete_models:
        for model in incomplete_models:
            print(f"  ⚠ {model}")
    
    print(f"Missing models: {len(missing_models)}")
    if missing_models:
        for model in missing_models:
            print(f"  ✗ {model}")
    
    print()
    
    # Check for any running downloads
    print("=== CHECKING FOR ACTIVE DOWNLOADS ===")
    temp_files = []
    for model_path in models_path.glob("*"):
        if model_path.is_dir():
            # Look for .tmp files or incomplete downloads
            for temp_file in model_path.rglob("*.tmp"):
                temp_files.append(str(temp_file))
            for temp_file in model_path.rglob("*.part"):
                temp_files.append(str(temp_file))
    
    if temp_files:
        print("Found potential active/stalled downloads:")
        for temp_file in temp_files:
            print(f"  {temp_file}")
    else:
        print("No active downloads detected")
    
    return {
        "complete": complete_models,
        "incomplete": incomplete_models, 
        "missing": missing_models,
        "temp_files": temp_files
    }

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python quick_model_check.py <models_path>")
        sys.exit(1)
    
    models_path = sys.argv[1]
    result = check_model_status(models_path)
    
    if result["complete"]:
        print("Models ready for use!")
    else:
        print("Some models need attention.")
