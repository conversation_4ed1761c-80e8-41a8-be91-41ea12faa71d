{"_class_name": "FluxPipeline", "_diffusers_version": "0.30.0", "_name_or_path": "black-forest-labs/FLUX.1-dev", "requires_safety_checker": false, "scheduler": ["diffusers", "FlowMatchEulerDiscreteScheduler"], "text_encoder": ["transformers", "CLIPTextModel"], "text_encoder_2": ["transformers", "T5EncoderModel"], "tokenizer": ["transformers", "CLIPTokenizer"], "tokenizer_2": ["transformers", "T5TokenizerFast"], "transformer": ["diffusers", "FluxTransformer2DModel"], "vae": ["diffusers", "AutoencoderKL"]}