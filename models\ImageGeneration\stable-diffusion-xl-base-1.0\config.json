{"_class_name": "StableDiffusionXLPipeline", "_diffusers_version": "0.21.0", "_name_or_path": "stabilityai/stable-diffusion-xl-base-1.0", "requires_safety_checker": false, "scheduler": ["diffusers", "EulerDiscreteScheduler"], "text_encoder": ["transformers", "CLIPTextModel"], "text_encoder_2": ["transformers", "CLIPTextModelWithProjection"], "tokenizer": ["transformers", "CLIPTokenizer"], "tokenizer_2": ["transformers", "CLIPTokenizer"], "unet": ["diffusers", "UNet2DConditionModel"], "vae": ["diffusers", "AutoencoderKL"]}