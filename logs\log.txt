========================================
         3D AI Studio Launcher
========================================

[1/3] Checking Node.js installation...
Γ£ô Node.js found
[2/3] Checking for node_modules...
Γ£ô Dependencies ready
[3/3] Building frontend for production...
The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
vite v5.4.19 building for production...

(!) outDir N:\3D AI Studio\dist\renderer is not inside project root and will not be emptied.
Use --emptyOutDir to override.

✓ 2114 modules transformed.
../../dist/renderer/index.html                     0.21 kB │ gzip:   0.16 kB
../../dist/renderer/assets/index-CN-azzHY.css     44.66 kB │ gzip:   7.35 kB
../../dist/renderer/assets/index-E2ArCN4p.js      11.52 kB │ gzip:   4.64 kB
../../dist/renderer/assets/index-Bcj0YhQR.js   1,354.15 kB │ gzip: 368.83 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 14.97s
Γ£ô Frontend build complete

========================================
    Starting 3D AI Studio (Production)...
========================================


> 3d-ai-studio@1.0.0 start
> electron .


info: Logger initialized. {"service":"user-service","timestamp":"2025-07-17 23:57:44"}
info: App is ready. {"service":"user-service","timestamp":"2025-07-17 23:57:44"}
info: Console window hidden at startup {"service":"user-service","timestamp":"2025-07-17 23:57:44"}
[Trellis Server] Checking for lingering Python processes...
[Trellis Server] No Python processes found
info: Python process cleanup completed on startup {"service":"user-service","timestamp":"2025-07-17 23:57:45"}
info: Production mode - Loading file: N:\3D AI Studio\dist\renderer\index.html {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Main window web contents started loading {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Splash screen initialization complete {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Splash Status: Loading pipeline configurations... {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Splash Status: Scanning pipelines... {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Loading pipelines from embedded configurations... {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Loaded pipeline: Core {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Loaded pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Loaded pipeline: ImageUpscaling {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Loaded pipeline: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Loaded pipeline: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Ensured pipeline directory exists: Core {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Ensured pipeline directory exists: ImageGeneration {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Ensured pipeline directory exists: ImageUpscaling {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Skipping directory creation for system package pipeline: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Skipping directory creation for system package pipeline: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Registering pipelines... {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Main window web contents finished loading {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Main window finished loading {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: Splash screen displayed {"service":"user-service","timestamp":"2025-07-17 23:57:46"}
info: IPC: get-console-visibility called {"service":"user-service","timestamp":"2025-07-17 23:57:47"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-17 23:57:47"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-17 23:57:47"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-17 23:57:48"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-17 23:57:48"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-17 23:57:48"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-17 23:57:48"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-17 23:57:48"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-17 23:57:48"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-17 23:57:48"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-17 23:57:49"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-17 23:57:49"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-17 23:57:49"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-17 23:57:49"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-17 23:57:50"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-17 23:57:50"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-17 23:57:50"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-17 23:57:50"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-17 23:57:50"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-17 23:57:50"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-17 23:57:50"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-17 23:57:50"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-17 23:57:50"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-17 23:57:50"}
info: IPC: get-config called for key: huggingface-token {"service":"user-service","timestamp":"2025-07-17 23:57:51"}
info: [DEBUG] Found pipeline config.json files: {"0":"N:\\3D AI Studio\\pipelines\\3DPipelines\\gen3d\\hunyuan2-spz-101\\config.json","1":"N:\\3D AI Studio\\pipelines\\3DPipelines\\gen3d\\trellis-stable-projectorz-101\\config.json","2":"N:\\3D AI Studio\\pipelines\\Core\\config.json","3":"N:\\3D AI Studio\\pipelines\\ImageGeneration\\config.json","4":"N:\\3D AI Studio\\pipelines\\ImageUpscaling\\config.json","service":"user-service","timestamp":"2025-07-17 23:58:03"}
info: Registered pipeline: hunyuan2-spz-101 (from N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\config.json) {"service":"user-service","timestamp":"2025-07-17 23:58:03"}
info: Registered pipeline: trellis-stable-projectorz-101 (from N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\config.json) {"service":"user-service","timestamp":"2025-07-17 23:58:03"}
info: Registered pipeline: Core (from N:\3D AI Studio\pipelines\Core\config.json) {"service":"user-service","timestamp":"2025-07-17 23:58:03"}
info: Registered pipeline: ImageGeneration (from N:\3D AI Studio\pipelines\ImageGeneration\config.json) {"service":"user-service","timestamp":"2025-07-17 23:58:03"}
info: Registered pipeline: ImageUpscaling (from N:\3D AI Studio\pipelines\ImageUpscaling\config.json) {"service":"user-service","timestamp":"2025-07-17 23:58:03"}
info: [DEBUG] Final registered pipelines: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-17 23:58:03"}
info: PipelineLoader: Final pipelines after merge: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-17 23:58:03"}
info: Splash Status: Checking core dependencies... {"service":"user-service","timestamp":"2025-07-17 23:58:03"}
info: Checking dependencies for Core {"service":"user-service","timestamp":"2025-07-17 23:58:03"}
info: Final dependency check for Core: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-17 23:58:06"}
info: Splash Status: Core dependencies verified {"service":"user-service","timestamp":"2025-07-17 23:58:06"}
info: Splash Status: Finalizing startup... {"service":"user-service","timestamp":"2025-07-17 23:58:06"}
info: Startup complete - showing main window {"service":"user-service","timestamp":"2025-07-17 23:58:09"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-17 23:58:09"}
info: Initialization complete signal sent to renderer {"service":"user-service","timestamp":"2025-07-17 23:58:09"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-17 23:58:09"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-07-17 23:58:09"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-17 23:58:09"}
info: IPC: get-image-collections called {"service":"user-service","timestamp":"2025-07-17 23:58:21"}
info: Loaded 2 collections {"service":"user-service","timestamp":"2025-07-17 23:58:21"}
info: IPC: save-image-collections called {"service":"user-service","timestamp":"2025-07-17 23:58:21"}
info: Image collections saved successfully {"service":"user-service","timestamp":"2025-07-17 23:58:21"}
info: Starting streaming image generation with model: sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 00:03:13"}
info: Starting image generation with command: N:\3D AI Studio\pipelines\ImageGeneration\env\Scripts\python.exe utils\helpers\generate_image.py --prompt a comic frog --output N:\3D AI Studio\output\generated_1752814993087_d4ad6220-f6c3-4542-a628-879fc051c997.png --model sdxl-turbo --width 512 --height 512 --guidance_scale 0 --seed 1319590139 --refiner_steps 10 --preview_quality high --result_file pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-18 00:03:13"}
info: ImageGeneration stderr: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton' {"service":"user-service","timestamp":"2025-07-18 00:03:31"}
info: ImageGeneration stdout: ImageGenerator initialized: {"service":"user-service","timestamp":"2025-07-18 00:03:32"}
info: ImageGeneration stdout: Device: cuda {"service":"user-service","timestamp":"2025-07-18 00:03:32"}
info: ImageGeneration stdout: Torch dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 00:03:32"}
info: ImageGeneration stdout: App root: N:\3D AI Studio {"service":"user-service","timestamp":"2025-07-18 00:03:32"}
info: ImageGeneration stdout: Models path: N:\3D AI Studio\models\ImageGeneration {"service":"user-service","timestamp":"2025-07-18 00:03:32"}
info: ImageGeneration stdout: Preview quality: high {"service":"user-service","timestamp":"2025-07-18 00:03:32"}
info: ImageGeneration stdout: Initializing sdxl-turbo model... {"service":"user-service","timestamp":"2025-07-18 00:03:32"}
info: ImageGeneration stdout: Loading model: sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 00:03:32"}
info: ImageGeneration stderr: Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-18 00:03:32"}
info: ImageGeneration stderr: Loading pipeline components...:  14%|#4        | 1/7 [00:00<00:00,  9.26it/s] {"service":"user-service","timestamp":"2025-07-18 00:03:32"}
info: ImageGeneration stderr: Loading pipeline components...:  29%|##8       | 2/7 [00:01<00:03,  1.61it/s] {"service":"user-service","timestamp":"2025-07-18 00:03:33"}
info: ImageGeneration stderr: Loading pipeline components...:  43%|####2     | 3/7 [00:01<00:01,  2.53it/s] {"service":"user-service","timestamp":"2025-07-18 00:03:33"}
info: ImageGeneration stderr: Loading pipeline components...:  57%|#####7    | 4/7 [00:04<00:04,  1.36s/it] {"service":"user-service","timestamp":"2025-07-18 00:03:36"}
info: ImageGeneration stderr: An error occurred while trying to fetch N:\3D AI Studio\models\ImageGeneration\sdxl-turbo: Error no file named diffusion_pytorch_model.safetensors found in directory N:\3D AI Studio\models\ImageGeneration\sdxl-turbo. {"service":"user-service","timestamp":"2025-07-18 00:03:36"}
info: ImageGeneration stdout: Error loading model sdxl-turbo: Error no file named diffusion_pytorch_model.bin found in directory N:\3D AI Studio\models\ImageGeneration\sdxl-turbo. {"service":"user-service","timestamp":"2025-07-18 00:03:36"}
info: ImageGeneration stderr: Loading pipeline components...:  57%|#####7    | 4/7 [00:04<00:03,  1.01s/it] {"service":"user-service","timestamp":"2025-07-18 00:03:36"}
info: ImageGeneration stderr: Traceback (most recent call last): {"service":"user-service","timestamp":"2025-07-18 00:03:36"}
info: ImageGeneration stderr: File "N:\3D AI Studio\utils\helpers\generate_image.py", line 492, in load_model
    self.pipeline = StableDiffusionXLPipeline.from_pretrained(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 1022, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 833, in load_sub_model
    loaded_sub_model = load_method(cached_folder, **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\modeling_utils.py", line 1177, in from_pretrained
    resolved_model_file = _get_model_file(
                          ^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\utils\hub_utils.py", line 254, in _get_model_file
    raise EnvironmentError(
OSError: Error no file named diffusion_pytorch_model.bin found in directory N:\3D AI Studio\models\ImageGeneration\sdxl-turbo. {"service":"user-service","timestamp":"2025-07-18 00:03:36"}
info: ImageGeneration stdout: {"success": false, "error": "Failed to load model sdxl-turbo", "execution_time": 4.11733341217041} {"service":"user-service","timestamp":"2025-07-18 00:03:36"}
info: Image generation process exited with code: 1 {"service":"user-service","timestamp":"2025-07-18 00:03:37"}
error: Image generation failed with code 1: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton'
Loading pipeline components...:  57%|#####7    | 4/7 [00:04<00:04,  1.36s/it]An error occurred while trying to fetch N:\3D AI Studio\models\ImageGeneration\sdxl-turbo: Error no file named diffusion_pytorch_model.safetensors found in directory N:\3D AI Studio\models\ImageGeneration\sdxl-turbo.
Loading pipeline components...:  57%|#####7    | 4/7 [00:04<00:03,  1.01s/it]
Traceback (most recent call last):
  File "N:\3D AI Studio\utils\helpers\generate_image.py", line 492, in load_model
    self.pipeline = StableDiffusionXLPipeline.from_pretrained(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 1022, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 833, in load_sub_model
    loaded_sub_model = load_method(cached_folder, **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\modeling_utils.py", line 1177, in from_pretrained
    resolved_model_file = _get_model_file(
                          ^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\utils\hub_utils.py", line 254, in _get_model_file
    raise EnvironmentError(
OSError: Error no file named diffusion_pytorch_model.bin found in directory N:\3D AI Studio\models\ImageGeneration\sdxl-turbo.
 {"service":"user-service","timestamp":"2025-07-18 00:03:37"}
error: Failed to generate image (streaming): Image generation failed with code 1: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton'
Loading pipeline components...:  57%|#####7    | 4/7 [00:04<00:04,  1.36s/it]An error occurred while trying to fetch N:\3D AI Studio\models\ImageGeneration\sdxl-turbo: Error no file named diffusion_pytorch_model.safetensors found in directory N:\3D AI Studio\models\ImageGeneration\sdxl-turbo.
Loading pipeline components...:  57%|#####7    | 4/7 [00:04<00:03,  1.01s/it]
Traceback (most recent call last):
  File "N:\3D AI Studio\utils\helpers\generate_image.py", line 492, in load_model
    self.pipeline = StableDiffusionXLPipeline.from_pretrained(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 1022, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 833, in load_sub_model
    loaded_sub_model = load_method(cached_folder, **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\modeling_utils.py", line 1177, in from_pretrained
    resolved_model_file = _get_model_file(
                          ^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\utils\hub_utils.py", line 254, in _get_model_file
    raise EnvironmentError(
OSError: Error no file named diffusion_pytorch_model.bin found in directory N:\3D AI Studio\models\ImageGeneration\sdxl-turbo.
 {"service":"user-service","stack":"Error: Image generation failed with code 1: A matching Triton is not available, some optimizations will not be enabled\r\nTraceback (most recent call last):\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\xformers\\__init__.py\", line 57, in _is_triton_available\r\n    import triton  # noqa\r\n    ^^^^^^^^^^^^^\r\nModuleNotFoundError: No module named 'triton'\r\n\rLoading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s]\rLoading pipeline components...:  14%|#4        | 1/7 [00:00<00:00,  9.26it/s]\rLoading pipeline components...:  29%|##8       | 2/7 [00:01<00:03,  1.61it/s]\rLoading pipeline components...:  43%|####2     | 3/7 [00:01<00:01,  2.53it/s]\rLoading pipeline components...:  57%|#####7    | 4/7 [00:04<00:04,  1.36s/it]An error occurred while trying to fetch N:\\3D AI Studio\\models\\ImageGeneration\\sdxl-turbo: Error no file named diffusion_pytorch_model.safetensors found in directory N:\\3D AI Studio\\models\\ImageGeneration\\sdxl-turbo.\r\n\rLoading pipeline components...:  57%|#####7    | 4/7 [00:04<00:03,  1.01s/it]\r\nTraceback (most recent call last):\r\n  File \"N:\\3D AI Studio\\utils\\helpers\\generate_image.py\", line 492, in load_model\r\n    self.pipeline = StableDiffusionXLPipeline.from_pretrained(\r\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\huggingface_hub\\utils\\_validators.py\", line 114, in _inner_fn\r\n    return fn(*args, **kwargs)\r\n           ^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\pipelines\\pipeline_utils.py\", line 1022, in from_pretrained\r\n    loaded_sub_model = load_sub_model(\r\n                       ^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\pipelines\\pipeline_loading_utils.py\", line 833, in load_sub_model\r\n    loaded_sub_model = load_method(cached_folder, **loading_kwargs)\r\n                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\huggingface_hub\\utils\\_validators.py\", line 114, in _inner_fn\r\n    return fn(*args, **kwargs)\r\n           ^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\models\\modeling_utils.py\", line 1177, in from_pretrained\r\n    resolved_model_file = _get_model_file(\r\n                          ^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\huggingface_hub\\utils\\_validators.py\", line 114, in _inner_fn\r\n    return fn(*args, **kwargs)\r\n           ^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\utils\\hub_utils.py\", line 254, in _get_model_file\r\n    raise EnvironmentError(\r\nOSError: Error no file named diffusion_pytorch_model.bin found in directory N:\\3D AI Studio\\models\\ImageGeneration\\sdxl-turbo.\r\n\n    at ChildProcess.<anonymous> (N:\\3D AI Studio\\src\\main\\pipelineManager.js:349:36)\n    at ChildProcess.emit (node:events:519:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","timestamp":"2025-07-18 00:03:37"}
error: Error stack: {"0":"E","1":"r","10":"g","100":"i","1000":"o","1001":"n","1002":"e","1003":"n","1004":"t","1005":"s","1006":".","1007":".","1008":".","1009":":","101":"l","1010":" ","1011":" ","1012":"5","1013":"7","1014":"%","1015":"|","1016":"#","1017":"#","1018":"#","1019":"#","102":"l","1020":"#","1021":"7","1022":" ","1023":" ","1024":" ","1025":" ","1026":"|","1027":" ","1028":"4","1029":"/","103":" ","1030":"7","1031":" ","1032":"[","1033":"0","1034":"0","1035":":","1036":"0","1037":"4","1038":"<","1039":"0","104":"n","1040":"0","1041":":","1042":"0","1043":"3","1044":",","1045":" ","1046":" ","1047":"1","1048":".","1049":"0","105":"o","1050":"1","1051":"s","1052":"/","1053":"i","1054":"t","1055":"]","1056":"\r","1057":"\n","1058":"T","1059":"r","106":"t","1060":"a","1061":"c","1062":"e","1063":"b","1064":"a","1065":"c","1066":"k","1067":" ","1068":"(","1069":"m","107":" ","1070":"o","1071":"s","1072":"t","1073":" ","1074":"r","1075":"e","1076":"c","1077":"e","1078":"n","1079":"t","108":"b","1080":" ","1081":"c","1082":"a","1083":"l","1084":"l","1085":" ","1086":"l","1087":"a","1088":"s","1089":"t","109":"e","1090":")","1091":":","1092":"\r","1093":"\n","1094":" ","1095":" ","1096":"F","1097":"i","1098":"l","1099":"e","11":"e","110":" ","1100":" ","1101":"\"","1102":"N","1103":":","1104":"\\","1105":"3","1106":"D","1107":" ","1108":"A","1109":"I","111":"e","1110":" ","1111":"S","1112":"t","1113":"u","1114":"d","1115":"i","1116":"o","1117":"\\","1118":"u","1119":"t","112":"n","1120":"i","1121":"l","1122":"s","1123":"\\","1124":"h","1125":"e","1126":"l","1127":"p","1128":"e","1129":"r","113":"a","1130":"s","1131":"\\","1132":"g","1133":"e","1134":"n","1135":"e","1136":"r","1137":"a","1138":"t","1139":"e","114":"b","1140":"_","1141":"i","1142":"m","1143":"a","1144":"g","1145":"e","1146":".","1147":"p","1148":"y","1149":"\"","115":"l","1150":",","1151":" ","1152":"l","1153":"i","1154":"n","1155":"e","1156":" ","1157":"4","1158":"9","1159":"2","116":"e","1160":",","1161":" ","1162":"i","1163":"n","1164":" ","1165":"l","1166":"o","1167":"a","1168":"d","1169":"_","117":"d","1170":"m","1171":"o","1172":"d","1173":"e","1174":"l","1175":"\r","1176":"\n","1177":" ","1178":" ","1179":" ","118":"\r","1180":" ","1181":"s","1182":"e","1183":"l","1184":"f","1185":".","1186":"p","1187":"i","1188":"p","1189":"e","119":"\n","1190":"l","1191":"i","1192":"n","1193":"e","1194":" ","1195":"=","1196":" ","1197":"S","1198":"t","1199":"a","12":" ","120":"T","1200":"b","1201":"l","1202":"e","1203":"D","1204":"i","1205":"f","1206":"f","1207":"u","1208":"s","1209":"i","121":"r","1210":"o","1211":"n","1212":"X","1213":"L","1214":"P","1215":"i","1216":"p","1217":"e","1218":"l","1219":"i","122":"a","1220":"n","1221":"e","1222":".","1223":"f","1224":"r","1225":"o","1226":"m","1227":"_","1228":"p","1229":"r","123":"c","1230":"e","1231":"t","1232":"r","1233":"a","1234":"i","1235":"n","1236":"e","1237":"d","1238":"(","1239":"\r","124":"e","1240":"\n","1241":" ","1242":" ","1243":" ","1244":" ","1245":" ","1246":" ","1247":" ","1248":" ","1249":" ","125":"b","1250":" ","1251":" ","1252":" ","1253":" ","1254":" ","1255":" ","1256":" ","1257":" ","1258":" ","1259":" ","126":"a","1260":" ","1261":"^","1262":"^","1263":"^","1264":"^","1265":"^","1266":"^","1267":"^","1268":"^","1269":"^","127":"c","1270":"^","1271":"^","1272":"^","1273":"^","1274":"^","1275":"^","1276":"^","1277":"^","1278":"^","1279":"^","128":"k","1280":"^","1281":"^","1282":"^","1283":"^","1284":"^","1285":"^","1286":"^","1287":"^","1288":"^","1289":"^","129":" ","1290":"^","1291":"^","1292":"^","1293":"^","1294":"^","1295":"^","1296":"^","1297":"^","1298":"^","1299":"^","13":"g","130":"(","1300":"^","1301":"^","1302":"^","1303":"\r","1304":"\n","1305":" ","1306":" ","1307":"F","1308":"i","1309":"l","131":"m","1310":"e","1311":" ","1312":"\"","1313":"N","1314":":","1315":"\\","1316":"3","1317":"D","1318":" ","1319":"A","132":"o","1320":"I","1321":" ","1322":"S","1323":"t","1324":"u","1325":"d","1326":"i","1327":"o","1328":"\\","1329":"p","133":"s","1330":"i","1331":"p","1332":"e","1333":"l","1334":"i","1335":"n","1336":"e","1337":"s","1338":"\\","1339":"I","134":"t","1340":"m","1341":"a","1342":"g","1343":"e","1344":"G","1345":"e","1346":"n","1347":"e","1348":"r","1349":"a","135":" ","1350":"t","1351":"i","1352":"o","1353":"n","1354":"\\","1355":"e","1356":"n","1357":"v","1358":"\\","1359":"L","136":"r","1360":"i","1361":"b","1362":"\\","1363":"s","1364":"i","1365":"t","1366":"e","1367":"-","1368":"p","1369":"a","137":"e","1370":"c","1371":"k","1372":"a","1373":"g","1374":"e","1375":"s","1376":"\\","1377":"h","1378":"u","1379":"g","138":"c","1380":"g","1381":"i","1382":"n","1383":"g","1384":"f","1385":"a","1386":"c","1387":"e","1388":"_","1389":"h","139":"e","1390":"u","1391":"b","1392":"\\","1393":"u","1394":"t","1395":"i","1396":"l","1397":"s","1398":"\\","1399":"_","14":"e","140":"n","1400":"v","1401":"a","1402":"l","1403":"i","1404":"d","1405":"a","1406":"t","1407":"o","1408":"r","1409":"s","141":"t","1410":".","1411":"p","1412":"y","1413":"\"","1414":",","1415":" ","1416":"l","1417":"i","1418":"n","1419":"e","142":" ","1420":" ","1421":"1","1422":"1","1423":"4","1424":",","1425":" ","1426":"i","1427":"n","1428":" ","1429":"_","143":"c","1430":"i","1431":"n","1432":"n","1433":"e","1434":"r","1435":"_","1436":"f","1437":"n","1438":"\r","1439":"\n","144":"a","1440":" ","1441":" ","1442":" ","1443":" ","1444":"r","1445":"e","1446":"t","1447":"u","1448":"r","1449":"n","145":"l","1450":" ","1451":"f","1452":"n","1453":"(","1454":"*","1455":"a","1456":"r","1457":"g","1458":"s","1459":",","146":"l","1460":" ","1461":"*","1462":"*","1463":"k","1464":"w","1465":"a","1466":"r","1467":"g","1468":"s","1469":")","147":" ","1470":"\r","1471":"\n","1472":" ","1473":" ","1474":" ","1475":" ","1476":" ","1477":" ","1478":" ","1479":" ","148":"l","1480":" ","1481":" ","1482":" ","1483":"^","1484":"^","1485":"^","1486":"^","1487":"^","1488":"^","1489":"^","149":"a","1490":"^","1491":"^","1492":"^","1493":"^","1494":"^","1495":"^","1496":"^","1497":"^","1498":"^","1499":"^","15":"n","150":"s","1500":"^","1501":"^","1502":"\r","1503":"\n","1504":" ","1505":" ","1506":"F","1507":"i","1508":"l","1509":"e","151":"t","1510":" ","1511":"\"","1512":"N","1513":":","1514":"\\","1515":"3","1516":"D","1517":" ","1518":"A","1519":"I","152":")","1520":" ","1521":"S","1522":"t","1523":"u","1524":"d","1525":"i","1526":"o","1527":"\\","1528":"p","1529":"i","153":":","1530":"p","1531":"e","1532":"l","1533":"i","1534":"n","1535":"e","1536":"s","1537":"\\","1538":"I","1539":"m","154":"\r","1540":"a","1541":"g","1542":"e","1543":"G","1544":"e","1545":"n","1546":"e","1547":"r","1548":"a","1549":"t","155":"\n","1550":"i","1551":"o","1552":"n","1553":"\\","1554":"e","1555":"n","1556":"v","1557":"\\","1558":"L","1559":"i","156":" ","1560":"b","1561":"\\","1562":"s","1563":"i","1564":"t","1565":"e","1566":"-","1567":"p","1568":"a","1569":"c","157":" ","1570":"k","1571":"a","1572":"g","1573":"e","1574":"s","1575":"\\","1576":"d","1577":"i","1578":"f","1579":"f","158":"F","1580":"u","1581":"s","1582":"e","1583":"r","1584":"s","1585":"\\","1586":"p","1587":"i","1588":"p","1589":"e","159":"i","1590":"l","1591":"i","1592":"n","1593":"e","1594":"s","1595":"\\","1596":"p","1597":"i","1598":"p","1599":"e","16":"e","160":"l","1600":"l","1601":"i","1602":"n","1603":"e","1604":"_","1605":"u","1606":"t","1607":"i","1608":"l","1609":"s","161":"e","1610":".","1611":"p","1612":"y","1613":"\"","1614":",","1615":" ","1616":"l","1617":"i","1618":"n","1619":"e","162":" ","1620":" ","1621":"1","1622":"0","1623":"2","1624":"2","1625":",","1626":" ","1627":"i","1628":"n","1629":" ","163":"\"","1630":"f","1631":"r","1632":"o","1633":"m","1634":"_","1635":"p","1636":"r","1637":"e","1638":"t","1639":"r","164":"N","1640":"a","1641":"i","1642":"n","1643":"e","1644":"d","1645":"\r","1646":"\n","1647":" ","1648":" ","1649":" ","165":":","1650":" ","1651":"l","1652":"o","1653":"a","1654":"d","1655":"e","1656":"d","1657":"_","1658":"s","1659":"u","166":"\\","1660":"b","1661":"_","1662":"m","1663":"o","1664":"d","1665":"e","1666":"l","1667":" ","1668":"=","1669":" ","167":"3","1670":"l","1671":"o","1672":"a","1673":"d","1674":"_","1675":"s","1676":"u","1677":"b","1678":"_","1679":"m","168":"D","1680":"o","1681":"d","1682":"e","1683":"l","1684":"(","1685":"\r","1686":"\n","1687":" ","1688":" ","1689":" ","169":" ","1690":" ","1691":" ","1692":" ","1693":" ","1694":" ","1695":" ","1696":" ","1697":" ","1698":" ","1699":" ","17":"r","170":"A","1700":" ","1701":" ","1702":" ","1703":" ","1704":" ","1705":" ","1706":" ","1707":" ","1708":" ","1709":" ","171":"I","1710":"^","1711":"^","1712":"^","1713":"^","1714":"^","1715":"^","1716":"^","1717":"^","1718":"^","1719":"^","172":" ","1720":"^","1721":"^","1722":"^","1723":"^","1724":"^","1725":"\r","1726":"\n","1727":" ","1728":" ","1729":"F","173":"S","1730":"i","1731":"l","1732":"e","1733":" ","1734":"\"","1735":"N","1736":":","1737":"\\","1738":"3","1739":"D","174":"t","1740":" ","1741":"A","1742":"I","1743":" ","1744":"S","1745":"t","1746":"u","1747":"d","1748":"i","1749":"o","175":"u","1750":"\\","1751":"p","1752":"i","1753":"p","1754":"e","1755":"l","1756":"i","1757":"n","1758":"e","1759":"s","176":"d","1760":"\\","1761":"I","1762":"m","1763":"a","1764":"g","1765":"e","1766":"G","1767":"e","1768":"n","1769":"e","177":"i","1770":"r","1771":"a","1772":"t","1773":"i","1774":"o","1775":"n","1776":"\\","1777":"e","1778":"n","1779":"v","178":"o","1780":"\\","1781":"L","1782":"i","1783":"b","1784":"\\","1785":"s","1786":"i","1787":"t","1788":"e","1789":"-","179":"\\","1790":"p","1791":"a","1792":"c","1793":"k","1794":"a","1795":"g","1796":"e","1797":"s","1798":"\\","1799":"d","18":"a","180":"p","1800":"i","1801":"f","1802":"f","1803":"u","1804":"s","1805":"e","1806":"r","1807":"s","1808":"\\","1809":"p","181":"i","1810":"i","1811":"p","1812":"e","1813":"l","1814":"i","1815":"n","1816":"e","1817":"s","1818":"\\","1819":"p","182":"p","1820":"i","1821":"p","1822":"e","1823":"l","1824":"i","1825":"n","1826":"e","1827":"_","1828":"l","1829":"o","183":"e","1830":"a","1831":"d","1832":"i","1833":"n","1834":"g","1835":"_","1836":"u","1837":"t","1838":"i","1839":"l","184":"l","1840":"s","1841":".","1842":"p","1843":"y","1844":"\"","1845":",","1846":" ","1847":"l","1848":"i","1849":"n","185":"i","1850":"e","1851":" ","1852":"8","1853":"3","1854":"3","1855":",","1856":" ","1857":"i","1858":"n","1859":" ","186":"n","1860":"l","1861":"o","1862":"a","1863":"d","1864":"_","1865":"s","1866":"u","1867":"b","1868":"_","1869":"m","187":"e","1870":"o","1871":"d","1872":"e","1873":"l","1874":"\r","1875":"\n","1876":" ","1877":" ","1878":" ","1879":" ","188":"s","1880":"l","1881":"o","1882":"a","1883":"d","1884":"e","1885":"d","1886":"_","1887":"s","1888":"u","1889":"b","189":"\\","1890":"_","1891":"m","1892":"o","1893":"d","1894":"e","1895":"l","1896":" ","1897":"=","1898":" ","1899":"l","19":"t","190":"I","1900":"o","1901":"a","1902":"d","1903":"_","1904":"m","1905":"e","1906":"t","1907":"h","1908":"o","1909":"d","191":"m","1910":"(","1911":"c","1912":"a","1913":"c","1914":"h","1915":"e","1916":"d","1917":"_","1918":"f","1919":"o","192":"a","1920":"l","1921":"d","1922":"e","1923":"r","1924":",","1925":" ","1926":"*","1927":"*","1928":"l","1929":"o","193":"g","1930":"a","1931":"d","1932":"i","1933":"n","1934":"g","1935":"_","1936":"k","1937":"w","1938":"a","1939":"r","194":"e","1940":"g","1941":"s","1942":")","1943":"\r","1944":"\n","1945":" ","1946":" ","1947":" ","1948":" ","1949":" ","195":"G","1950":" ","1951":" ","1952":" ","1953":" ","1954":" ","1955":" ","1956":" ","1957":" ","1958":" ","1959":" ","196":"e","1960":" ","1961":" ","1962":" ","1963":" ","1964":" ","1965":" ","1966":" ","1967":" ","1968":"^","1969":"^","197":"n","1970":"^","1971":"^","1972":"^","1973":"^","1974":"^","1975":"^","1976":"^","1977":"^","1978":"^","1979":"^","198":"e","1980":"^","1981":"^","1982":"^","1983":"^","1984":"^","1985":"^","1986":"^","1987":"^","1988":"^","1989":"^","199":"r","1990":"^","1991":"^","1992":"^","1993":"^","1994":"^","1995":"^","1996":"^","1997":"^","1998":"^","1999":"^","2":"r","20":"i","200":"a","2000":"^","2001":"^","2002":"^","2003":"^","2004":"^","2005":"^","2006":"^","2007":"^","2008":"^","2009":"^","201":"t","2010":"^","2011":"^","2012":"\r","2013":"\n","2014":" ","2015":" ","2016":"F","2017":"i","2018":"l","2019":"e","202":"i","2020":" ","2021":"\"","2022":"N","2023":":","2024":"\\","2025":"3","2026":"D","2027":" ","2028":"A","2029":"I","203":"o","2030":" ","2031":"S","2032":"t","2033":"u","2034":"d","2035":"i","2036":"o","2037":"\\","2038":"p","2039":"i","204":"n","2040":"p","2041":"e","2042":"l","2043":"i","2044":"n","2045":"e","2046":"s","2047":"\\","2048":"I","2049":"m","205":"\\","2050":"a","2051":"g","2052":"e","2053":"G","2054":"e","2055":"n","2056":"e","2057":"r","2058":"a","2059":"t","206":"e","2060":"i","2061":"o","2062":"n","2063":"\\","2064":"e","2065":"n","2066":"v","2067":"\\","2068":"L","2069":"i","207":"n","2070":"b","2071":"\\","2072":"s","2073":"i","2074":"t","2075":"e","2076":"-","2077":"p","2078":"a","2079":"c","208":"v","2080":"k","2081":"a","2082":"g","2083":"e","2084":"s","2085":"\\","2086":"h","2087":"u","2088":"g","2089":"g","209":"\\","2090":"i","2091":"n","2092":"g","2093":"f","2094":"a","2095":"c","2096":"e","2097":"_","2098":"h","2099":"u","21":"o","210":"L","2100":"b","2101":"\\","2102":"u","2103":"t","2104":"i","2105":"l","2106":"s","2107":"\\","2108":"_","2109":"v","211":"i","2110":"a","2111":"l","2112":"i","2113":"d","2114":"a","2115":"t","2116":"o","2117":"r","2118":"s","2119":".","212":"b","2120":"p","2121":"y","2122":"\"","2123":",","2124":" ","2125":"l","2126":"i","2127":"n","2128":"e","2129":" ","213":"\\","2130":"1","2131":"1","2132":"4","2133":",","2134":" ","2135":"i","2136":"n","2137":" ","2138":"_","2139":"i","214":"s","2140":"n","2141":"n","2142":"e","2143":"r","2144":"_","2145":"f","2146":"n","2147":"\r","2148":"\n","2149":" ","215":"i","2150":" ","2151":" ","2152":" ","2153":"r","2154":"e","2155":"t","2156":"u","2157":"r","2158":"n","2159":" ","216":"t","2160":"f","2161":"n","2162":"(","2163":"*","2164":"a","2165":"r","2166":"g","2167":"s","2168":",","2169":" ","217":"e","2170":"*","2171":"*","2172":"k","2173":"w","2174":"a","2175":"r","2176":"g","2177":"s","2178":")","2179":"\r","218":"-","2180":"\n","2181":" ","2182":" ","2183":" ","2184":" ","2185":" ","2186":" ","2187":" ","2188":" ","2189":" ","219":"p","2190":" ","2191":" ","2192":"^","2193":"^","2194":"^","2195":"^","2196":"^","2197":"^","2198":"^","2199":"^","22":"n","220":"a","2200":"^","2201":"^","2202":"^","2203":"^","2204":"^","2205":"^","2206":"^","2207":"^","2208":"^","2209":"^","221":"c","2210":"^","2211":"\r","2212":"\n","2213":" ","2214":" ","2215":"F","2216":"i","2217":"l","2218":"e","2219":" ","222":"k","2220":"\"","2221":"N","2222":":","2223":"\\","2224":"3","2225":"D","2226":" ","2227":"A","2228":"I","2229":" ","223":"a","2230":"S","2231":"t","2232":"u","2233":"d","2234":"i","2235":"o","2236":"\\","2237":"p","2238":"i","2239":"p","224":"g","2240":"e","2241":"l","2242":"i","2243":"n","2244":"e","2245":"s","2246":"\\","2247":"I","2248":"m","2249":"a","225":"e","2250":"g","2251":"e","2252":"G","2253":"e","2254":"n","2255":"e","2256":"r","2257":"a","2258":"t","2259":"i","226":"s","2260":"o","2261":"n","2262":"\\","2263":"e","2264":"n","2265":"v","2266":"\\","2267":"L","2268":"i","2269":"b","227":"\\","2270":"\\","2271":"s","2272":"i","2273":"t","2274":"e","2275":"-","2276":"p","2277":"a","2278":"c","2279":"k","228":"x","2280":"a","2281":"g","2282":"e","2283":"s","2284":"\\","2285":"d","2286":"i","2287":"f","2288":"f","2289":"u","229":"f","2290":"s","2291":"e","2292":"r","2293":"s","2294":"\\","2295":"m","2296":"o","2297":"d","2298":"e","2299":"l","23":" ","230":"o","2300":"s","2301":"\\","2302":"m","2303":"o","2304":"d","2305":"e","2306":"l","2307":"i","2308":"n","2309":"g","231":"r","2310":"_","2311":"u","2312":"t","2313":"i","2314":"l","2315":"s","2316":".","2317":"p","2318":"y","2319":"\"","232":"m","2320":",","2321":" ","2322":"l","2323":"i","2324":"n","2325":"e","2326":" ","2327":"1","2328":"1","2329":"7","233":"e","2330":"7","2331":",","2332":" ","2333":"i","2334":"n","2335":" ","2336":"f","2337":"r","2338":"o","2339":"m","234":"r","2340":"_","2341":"p","2342":"r","2343":"e","2344":"t","2345":"r","2346":"a","2347":"i","2348":"n","2349":"e","235":"s","2350":"d","2351":"\r","2352":"\n","2353":" ","2354":" ","2355":" ","2356":" ","2357":"r","2358":"e","2359":"s","236":"\\","2360":"o","2361":"l","2362":"v","2363":"e","2364":"d","2365":"_","2366":"m","2367":"o","2368":"d","2369":"e","237":"_","2370":"l","2371":"_","2372":"f","2373":"i","2374":"l","2375":"e","2376":" ","2377":"=","2378":" ","2379":"_","238":"_","2380":"g","2381":"e","2382":"t","2383":"_","2384":"m","2385":"o","2386":"d","2387":"e","2388":"l","2389":"_","239":"i","2390":"f","2391":"i","2392":"l","2393":"e","2394":"(","2395":"\r","2396":"\n","2397":" ","2398":" ","2399":" ","24":"f","240":"n","2400":" ","2401":" ","2402":" ","2403":" ","2404":" ","2405":" ","2406":" ","2407":" ","2408":" ","2409":" ","241":"i","2410":" ","2411":" ","2412":" ","2413":" ","2414":" ","2415":" ","2416":" ","2417":" ","2418":" ","2419":" ","242":"t","2420":" ","2421":" ","2422":" ","2423":"^","2424":"^","2425":"^","2426":"^","2427":"^","2428":"^","2429":"^","243":"_","2430":"^","2431":"^","2432":"^","2433":"^","2434":"^","2435":"^","2436":"^","2437":"^","2438":"^","2439":"\r","244":"_","2440":"\n","2441":" ","2442":" ","2443":"F","2444":"i","2445":"l","2446":"e","2447":" ","2448":"\"","2449":"N","245":".","2450":":","2451":"\\","2452":"3","2453":"D","2454":" ","2455":"A","2456":"I","2457":" ","2458":"S","2459":"t","246":"p","2460":"u","2461":"d","2462":"i","2463":"o","2464":"\\","2465":"p","2466":"i","2467":"p","2468":"e","2469":"l","247":"y","2470":"i","2471":"n","2472":"e","2473":"s","2474":"\\","2475":"I","2476":"m","2477":"a","2478":"g","2479":"e","248":"\"","2480":"G","2481":"e","2482":"n","2483":"e","2484":"r","2485":"a","2486":"t","2487":"i","2488":"o","2489":"n","249":",","2490":"\\","2491":"e","2492":"n","2493":"v","2494":"\\","2495":"L","2496":"i","2497":"b","2498":"\\","2499":"s","25":"a","250":" ","2500":"i","2501":"t","2502":"e","2503":"-","2504":"p","2505":"a","2506":"c","2507":"k","2508":"a","2509":"g","251":"l","2510":"e","2511":"s","2512":"\\","2513":"h","2514":"u","2515":"g","2516":"g","2517":"i","2518":"n","2519":"g","252":"i","2520":"f","2521":"a","2522":"c","2523":"e","2524":"_","2525":"h","2526":"u","2527":"b","2528":"\\","2529":"u","253":"n","2530":"t","2531":"i","2532":"l","2533":"s","2534":"\\","2535":"_","2536":"v","2537":"a","2538":"l","2539":"i","254":"e","2540":"d","2541":"a","2542":"t","2543":"o","2544":"r","2545":"s","2546":".","2547":"p","2548":"y","2549":"\"","255":" ","2550":",","2551":" ","2552":"l","2553":"i","2554":"n","2555":"e","2556":" ","2557":"1","2558":"1","2559":"4","256":"5","2560":",","2561":" ","2562":"i","2563":"n","2564":" ","2565":"_","2566":"i","2567":"n","2568":"n","2569":"e","257":"7","2570":"r","2571":"_","2572":"f","2573":"n","2574":"\r","2575":"\n","2576":" ","2577":" ","2578":" ","2579":" ","258":",","2580":"r","2581":"e","2582":"t","2583":"u","2584":"r","2585":"n","2586":" ","2587":"f","2588":"n","2589":"(","259":" ","2590":"*","2591":"a","2592":"r","2593":"g","2594":"s","2595":",","2596":" ","2597":"*","2598":"*","2599":"k","26":"i","260":"i","2600":"w","2601":"a","2602":"r","2603":"g","2604":"s","2605":")","2606":"\r","2607":"\n","2608":" ","2609":" ","261":"n","2610":" ","2611":" ","2612":" ","2613":" ","2614":" ","2615":" ","2616":" ","2617":" ","2618":" ","2619":"^","262":" ","2620":"^","2621":"^","2622":"^","2623":"^","2624":"^","2625":"^","2626":"^","2627":"^","2628":"^","2629":"^","263":"_","2630":"^","2631":"^","2632":"^","2633":"^","2634":"^","2635":"^","2636":"^","2637":"^","2638":"\r","2639":"\n","264":"i","2640":" ","2641":" ","2642":"F","2643":"i","2644":"l","2645":"e","2646":" ","2647":"\"","2648":"N","2649":":","265":"s","2650":"\\","2651":"3","2652":"D","2653":" ","2654":"A","2655":"I","2656":" ","2657":"S","2658":"t","2659":"u","266":"_","2660":"d","2661":"i","2662":"o","2663":"\\","2664":"p","2665":"i","2666":"p","2667":"e","2668":"l","2669":"i","267":"t","2670":"n","2671":"e","2672":"s","2673":"\\","2674":"I","2675":"m","2676":"a","2677":"g","2678":"e","2679":"G","268":"r","2680":"e","2681":"n","2682":"e","2683":"r","2684":"a","2685":"t","2686":"i","2687":"o","2688":"n","2689":"\\","269":"i","2690":"e","2691":"n","2692":"v","2693":"\\","2694":"L","2695":"i","2696":"b","2697":"\\","2698":"s","2699":"i","27":"l","270":"t","2700":"t","2701":"e","2702":"-","2703":"p","2704":"a","2705":"c","2706":"k","2707":"a","2708":"g","2709":"e","271":"o","2710":"s","2711":"\\","2712":"d","2713":"i","2714":"f","2715":"f","2716":"u","2717":"s","2718":"e","2719":"r","272":"n","2720":"s","2721":"\\","2722":"u","2723":"t","2724":"i","2725":"l","2726":"s","2727":"\\","2728":"h","2729":"u","273":"_","2730":"b","2731":"_","2732":"u","2733":"t","2734":"i","2735":"l","2736":"s","2737":".","2738":"p","2739":"y","274":"a","2740":"\"","2741":",","2742":" ","2743":"l","2744":"i","2745":"n","2746":"e","2747":" ","2748":"2","2749":"5","275":"v","2750":"4","2751":",","2752":" ","2753":"i","2754":"n","2755":" ","2756":"_","2757":"g","2758":"e","2759":"t","276":"a","2760":"_","2761":"m","2762":"o","2763":"d","2764":"e","2765":"l","2766":"_","2767":"f","2768":"i","2769":"l","277":"i","2770":"e","2771":"\r","2772":"\n","2773":" ","2774":" ","2775":" ","2776":" ","2777":"r","2778":"a","2779":"i","278":"l","2780":"s","2781":"e","2782":" ","2783":"E","2784":"n","2785":"v","2786":"i","2787":"r","2788":"o","2789":"n","279":"a","2790":"m","2791":"e","2792":"n","2793":"t","2794":"E","2795":"r","2796":"r","2797":"o","2798":"r","2799":"(","28":"e","280":"b","2800":"\r","2801":"\n","2802":"O","2803":"S","2804":"E","2805":"r","2806":"r","2807":"o","2808":"r","2809":":","281":"l","2810":" ","2811":"E","2812":"r","2813":"r","2814":"o","2815":"r","2816":" ","2817":"n","2818":"o","2819":" ","282":"e","2820":"f","2821":"i","2822":"l","2823":"e","2824":" ","2825":"n","2826":"a","2827":"m","2828":"e","2829":"d","283":"\r","2830":" ","2831":"d","2832":"i","2833":"f","2834":"f","2835":"u","2836":"s","2837":"i","2838":"o","2839":"n","284":"\n","2840":"_","2841":"p","2842":"y","2843":"t","2844":"o","2845":"r","2846":"c","2847":"h","2848":"_","2849":"m","285":" ","2850":"o","2851":"d","2852":"e","2853":"l","2854":".","2855":"b","2856":"i","2857":"n","2858":" ","2859":"f","286":" ","2860":"o","2861":"u","2862":"n","2863":"d","2864":" ","2865":"i","2866":"n","2867":" ","2868":"d","2869":"i","287":" ","2870":"r","2871":"e","2872":"c","2873":"t","2874":"o","2875":"r","2876":"y","2877":" ","2878":"N","2879":":","288":" ","2880":"\\","2881":"3","2882":"D","2883":" ","2884":"A","2885":"I","2886":" ","2887":"S","2888":"t","2889":"u","289":"i","2890":"d","2891":"i","2892":"o","2893":"\\","2894":"m","2895":"o","2896":"d","2897":"e","2898":"l","2899":"s","29":"d","290":"m","2900":"\\","2901":"I","2902":"m","2903":"a","2904":"g","2905":"e","2906":"G","2907":"e","2908":"n","2909":"e","291":"p","2910":"r","2911":"a","2912":"t","2913":"i","2914":"o","2915":"n","2916":"\\","2917":"s","2918":"d","2919":"x","292":"o","2920":"l","2921":"-","2922":"t","2923":"u","2924":"r","2925":"b","2926":"o","2927":".","2928":"\r","2929":"\n","293":"r","2930":"\n","2931":" ","2932":" ","2933":" ","2934":" ","2935":"a","2936":"t","2937":" ","2938":"C","2939":"h","294":"t","2940":"i","2941":"l","2942":"d","2943":"P","2944":"r","2945":"o","2946":"c","2947":"e","2948":"s","2949":"s","295":" ","2950":".","2951":"<","2952":"a","2953":"n","2954":"o","2955":"n","2956":"y","2957":"m","2958":"o","2959":"u","296":"t","2960":"s","2961":">","2962":" ","2963":"(","2964":"N","2965":":","2966":"\\","2967":"3","2968":"D","2969":" ","297":"r","2970":"A","2971":"I","2972":" ","2973":"S","2974":"t","2975":"u","2976":"d","2977":"i","2978":"o","2979":"\\","298":"i","2980":"s","2981":"r","2982":"c","2983":"\\","2984":"m","2985":"a","2986":"i","2987":"n","2988":"\\","2989":"p","299":"t","2990":"i","2991":"p","2992":"e","2993":"l","2994":"i","2995":"n","2996":"e","2997":"M","2998":"a","2999":"n","3":"o","30":" ","300":"o","3000":"a","3001":"g","3002":"e","3003":"r","3004":".","3005":"j","3006":"s","3007":":","3008":"3","3009":"4","301":"n","3010":"9","3011":":","3012":"3","3013":"6","3014":")","3015":"\n","3016":" ","3017":" ","3018":" ","3019":" ","302":" ","3020":"a","3021":"t","3022":" ","3023":"C","3024":"h","3025":"i","3026":"l","3027":"d","3028":"P","3029":"r","303":" ","3030":"o","3031":"c","3032":"e","3033":"s","3034":"s","3035":".","3036":"e","3037":"m","3038":"i","3039":"t","304":"#","3040":" ","3041":"(","3042":"n","3043":"o","3044":"d","3045":"e","3046":":","3047":"e","3048":"v","3049":"e","305":" ","3050":"n","3051":"t","3052":"s","3053":":","3054":"5","3055":"1","3056":"9","3057":":","3058":"2","3059":"8","306":"n","3060":")","3061":"\n","3062":" ","3063":" ","3064":" ","3065":" ","3066":"a","3067":"t","3068":" ","3069":"m","307":"o","3070":"a","3071":"y","3072":"b","3073":"e","3074":"C","3075":"l","3076":"o","3077":"s","3078":"e","3079":" ","308":"q","3080":"(","3081":"n","3082":"o","3083":"d","3084":"e","3085":":","3086":"i","3087":"n","3088":"t","3089":"e","309":"a","3090":"r","3091":"n","3092":"a","3093":"l","3094":"/","3095":"c","3096":"h","3097":"i","3098":"l","3099":"d","31":"w","310":"\r","3100":"_","3101":"p","3102":"r","3103":"o","3104":"c","3105":"e","3106":"s","3107":"s","3108":":","3109":"1","311":"\n","3110":"1","3111":"0","3112":"5","3113":":","3114":"1","3115":"6","3116":")","3117":"\n","3118":" ","3119":" ","312":" ","3120":" ","3121":" ","3122":"a","3123":"t","3124":" ","3125":"C","3126":"h","3127":"i","3128":"l","3129":"d","313":" ","3130":"P","3131":"r","3132":"o","3133":"c","3134":"e","3135":"s","3136":"s","3137":".","3138":"_","3139":"h","314":" ","3140":"a","3141":"n","3142":"d","3143":"l","3144":"e","3145":".","3146":"o","3147":"n","3148":"e","3149":"x","315":" ","3150":"i","3151":"t","3152":" ","3153":"(","3154":"n","3155":"o","3156":"d","3157":"e","3158":":","3159":"i","316":"^","3160":"n","3161":"t","3162":"e","3163":"r","3164":"n","3165":"a","3166":"l","3167":"/","3168":"c","3169":"h","317":"^","3170":"i","3171":"l","3172":"d","3173":"_","3174":"p","3175":"r","3176":"o","3177":"c","3178":"e","3179":"s","318":"^","3180":"s","3181":":","3182":"3","3183":"0","3184":"5","3185":":","3186":"5","3187":")","319":"^","32":"i","320":"^","321":"^","322":"^","323":"^","324":"^","325":"^","326":"^","327":"^","328":"^","329":"\r","33":"t","330":"\n","331":"M","332":"o","333":"d","334":"u","335":"l","336":"e","337":"N","338":"o","339":"t","34":"h","340":"F","341":"o","342":"u","343":"n","344":"d","345":"E","346":"r","347":"r","348":"o","349":"r","35":" ","350":":","351":" ","352":"N","353":"o","354":" ","355":"m","356":"o","357":"d","358":"u","359":"l","36":"c","360":"e","361":" ","362":"n","363":"a","364":"m","365":"e","366":"d","367":" ","368":"'","369":"t","37":"o","370":"r","371":"i","372":"t","373":"o","374":"n","375":"'","376":"\r","377":"\n","378":"\r","379":"L","38":"d","380":"o","381":"a","382":"d","383":"i","384":"n","385":"g","386":" ","387":"p","388":"i","389":"p","39":"e","390":"e","391":"l","392":"i","393":"n","394":"e","395":" ","396":"c","397":"o","398":"m","399":"p","4":"r","40":" ","400":"o","401":"n","402":"e","403":"n","404":"t","405":"s","406":".","407":".","408":".","409":":","41":"1","410":" ","411":" ","412":" ","413":"0","414":"%","415":"|","416":" ","417":" ","418":" ","419":" ","42":":","420":" ","421":" ","422":" ","423":" ","424":" ","425":" ","426":"|","427":" ","428":"0","429":"/","43":" ","430":"7","431":" ","432":"[","433":"0","434":"0","435":":","436":"0","437":"0","438":"<","439":"?","44":"A","440":",","441":" ","442":"?","443":"i","444":"t","445":"/","446":"s","447":"]","448":"\r","449":"L","45":" ","450":"o","451":"a","452":"d","453":"i","454":"n","455":"g","456":" ","457":"p","458":"i","459":"p","46":"m","460":"e","461":"l","462":"i","463":"n","464":"e","465":" ","466":"c","467":"o","468":"m","469":"p","47":"a","470":"o","471":"n","472":"e","473":"n","474":"t","475":"s","476":".","477":".","478":".","479":":","48":"t","480":" ","481":" ","482":"1","483":"4","484":"%","485":"|","486":"#","487":"4","488":" ","489":" ","49":"c","490":" ","491":" ","492":" ","493":" ","494":" ","495":" ","496":"|","497":" ","498":"1","499":"/","5":":","50":"h","500":"7","501":" ","502":"[","503":"0","504":"0","505":":","506":"0","507":"0","508":"<","509":"0","51":"i","510":"0","511":":","512":"0","513":"0","514":",","515":" ","516":" ","517":"9","518":".","519":"2","52":"n","520":"6","521":"i","522":"t","523":"/","524":"s","525":"]","526":"\r","527":"L","528":"o","529":"a","53":"g","530":"d","531":"i","532":"n","533":"g","534":" ","535":"p","536":"i","537":"p","538":"e","539":"l","54":" ","540":"i","541":"n","542":"e","543":" ","544":"c","545":"o","546":"m","547":"p","548":"o","549":"n","55":"T","550":"e","551":"n","552":"t","553":"s","554":".","555":".","556":".","557":":","558":" ","559":" ","56":"r","560":"2","561":"9","562":"%","563":"|","564":"#","565":"#","566":"8","567":" ","568":" ","569":" ","57":"i","570":" ","571":" ","572":" ","573":" ","574":"|","575":" ","576":"2","577":"/","578":"7","579":" ","58":"t","580":"[","581":"0","582":"0","583":":","584":"0","585":"1","586":"<","587":"0","588":"0","589":":","59":"o","590":"0","591":"3","592":",","593":" ","594":" ","595":"1","596":".","597":"6","598":"1","599":"i","6":" ","60":"n","600":"t","601":"/","602":"s","603":"]","604":"\r","605":"L","606":"o","607":"a","608":"d","609":"i","61":" ","610":"n","611":"g","612":" ","613":"p","614":"i","615":"p","616":"e","617":"l","618":"i","619":"n","62":"i","620":"e","621":" ","622":"c","623":"o","624":"m","625":"p","626":"o","627":"n","628":"e","629":"n","63":"s","630":"t","631":"s","632":".","633":".","634":".","635":":","636":" ","637":" ","638":"4","639":"3","64":" ","640":"%","641":"|","642":"#","643":"#","644":"#","645":"#","646":"2","647":" ","648":" ","649":" ","65":"n","650":" ","651":" ","652":"|","653":" ","654":"3","655":"/","656":"7","657":" ","658":"[","659":"0","66":"o","660":"0","661":":","662":"0","663":"1","664":"<","665":"0","666":"0","667":":","668":"0","669":"1","67":"t","670":",","671":" ","672":" ","673":"2","674":".","675":"5","676":"3","677":"i","678":"t","679":"/","68":" ","680":"s","681":"]","682":"\r","683":"L","684":"o","685":"a","686":"d","687":"i","688":"n","689":"g","69":"a","690":" ","691":"p","692":"i","693":"p","694":"e","695":"l","696":"i","697":"n","698":"e","699":" ","7":"I","70":"v","700":"c","701":"o","702":"m","703":"p","704":"o","705":"n","706":"e","707":"n","708":"t","709":"s","71":"a","710":".","711":".","712":".","713":":","714":" ","715":" ","716":"5","717":"7","718":"%","719":"|","72":"i","720":"#","721":"#","722":"#","723":"#","724":"#","725":"7","726":" ","727":" ","728":" ","729":" ","73":"l","730":"|","731":" ","732":"4","733":"/","734":"7","735":" ","736":"[","737":"0","738":"0","739":":","74":"a","740":"0","741":"4","742":"<","743":"0","744":"0","745":":","746":"0","747":"4","748":",","749":" ","75":"b","750":" ","751":"1","752":".","753":"3","754":"6","755":"s","756":"/","757":"i","758":"t","759":"]","76":"l","760":"A","761":"n","762":" ","763":"e","764":"r","765":"r","766":"o","767":"r","768":" ","769":"o","77":"e","770":"c","771":"c","772":"u","773":"r","774":"r","775":"e","776":"d","777":" ","778":"w","779":"h","78":",","780":"i","781":"l","782":"e","783":" ","784":"t","785":"r","786":"y","787":"i","788":"n","789":"g","79":" ","790":" ","791":"t","792":"o","793":" ","794":"f","795":"e","796":"t","797":"c","798":"h","799":" ","8":"m","80":"s","800":"N","801":":","802":"\\","803":"3","804":"D","805":" ","806":"A","807":"I","808":" ","809":"S","81":"o","810":"t","811":"u","812":"d","813":"i","814":"o","815":"\\","816":"m","817":"o","818":"d","819":"e","82":"m","820":"l","821":"s","822":"\\","823":"I","824":"m","825":"a","826":"g","827":"e","828":"G","829":"e","83":"e","830":"n","831":"e","832":"r","833":"a","834":"t","835":"i","836":"o","837":"n","838":"\\","839":"s","84":" ","840":"d","841":"x","842":"l","843":"-","844":"t","845":"u","846":"r","847":"b","848":"o","849":":","85":"o","850":" ","851":"E","852":"r","853":"r","854":"o","855":"r","856":" ","857":"n","858":"o","859":" ","86":"p","860":"f","861":"i","862":"l","863":"e","864":" ","865":"n","866":"a","867":"m","868":"e","869":"d","87":"t","870":" ","871":"d","872":"i","873":"f","874":"f","875":"u","876":"s","877":"i","878":"o","879":"n","88":"i","880":"_","881":"p","882":"y","883":"t","884":"o","885":"r","886":"c","887":"h","888":"_","889":"m","89":"m","890":"o","891":"d","892":"e","893":"l","894":".","895":"s","896":"a","897":"f","898":"e","899":"t","9":"a","90":"i","900":"e","901":"n","902":"s","903":"o","904":"r","905":"s","906":" ","907":"f","908":"o","909":"u","91":"z","910":"n","911":"d","912":" ","913":"i","914":"n","915":" ","916":"d","917":"i","918":"r","919":"e","92":"a","920":"c","921":"t","922":"o","923":"r","924":"y","925":" ","926":"N","927":":","928":"\\","929":"3","93":"t","930":"D","931":" ","932":"A","933":"I","934":" ","935":"S","936":"t","937":"u","938":"d","939":"i","94":"i","940":"o","941":"\\","942":"m","943":"o","944":"d","945":"e","946":"l","947":"s","948":"\\","949":"I","95":"o","950":"m","951":"a","952":"g","953":"e","954":"G","955":"e","956":"n","957":"e","958":"r","959":"a","96":"n","960":"t","961":"i","962":"o","963":"n","964":"\\","965":"s","966":"d","967":"x","968":"l","969":"-","97":"s","970":"t","971":"u","972":"r","973":"b","974":"o","975":".","976":"\r","977":"\n","978":"\r","979":"L","98":" ","980":"o","981":"a","982":"d","983":"i","984":"n","985":"g","986":" ","987":"p","988":"i","989":"p","99":"w","990":"e","991":"l","992":"i","993":"n","994":"e","995":" ","996":"c","997":"o","998":"m","999":"p","service":"user-service","timestamp":"2025-07-18 00:03:37"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-18 00:04:09"}
info: [upload-file] Received ΓÇô filename: 20250613_Wood_Chair_With_Brown_Cussions.jpg, size: 989189 bytes (Uint8Array) {"service":"user-service","timestamp":"2025-07-18 00:04:17"}
info: Buffer received (bytes): 989189 {"service":"user-service","timestamp":"2025-07-18 00:04:17"}
info: Uploaded original file saved: N:\3D AI Studio\uploads\c1b726ee-22ea-4e15-b81d-73d518749df4\20250613_Wood_Chair_With_Brown_Cussions.jpg {"service":"user-service","timestamp":"2025-07-18 00:04:17"}
info: Starting background removal for N:\3D AI Studio\uploads\c1b726ee-22ea-4e15-b81d-73d518749df4\20250613_Wood_Chair_With_Brown_Cussions.jpg... {"service":"user-service","timestamp":"2025-07-18 00:04:17"}
info: Spawning: N:\3D AI Studio\pipelines\Core\env\Scripts\python.exe N:\3D AI Studio\src\main\python_helpers\remove_background.py N:\3D AI Studio\uploads\c1b726ee-22ea-4e15-b81d-73d518749df4\20250613_Wood_Chair_With_Brown_Cussions.jpg N:\3D AI Studio\uploads\c1b726ee-22ea-4e15-b81d-73d518749df4\20250613_Wood_Chair_With_Brown_Cussions_processed.png {"service":"user-service","timestamp":"2025-07-18 00:04:17"}
info: [load-file] Received request for: uploads/c1b726ee-22ea-4e15-b81d-73d518749df4/20250613_Wood_Chair_With_Brown_Cussions_processed.png {"service":"user-service","timestamp":"2025-07-18 00:04:56"}
info: [load-file] Reading absolute path: N:\3D AI Studio\uploads\c1b726ee-22ea-4e15-b81d-73d518749df4\20250613_Wood_Chair_With_Brown_Cussions_processed.png {"service":"user-service","timestamp":"2025-07-18 00:04:56"}
info: IPC: run-pipeline called for: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 00:06:04"}
info: runPipeline request: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 00:06:04"}
info: runPipeline: Registered pipelines at call time: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-18 00:06:04"}
info: Checking dependencies for hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 00:06:04"}
info: Final dependency check for hunyuan2-spz-101: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-18 00:06:04"}
[IPC Handler] Sending status: hunyaun - 25% - Launching Hunyaun server and generating 3D model...
[HunyaunServer] generate3DModel called with imagePath: uploads/c1b726ee-22ea-4e15-b81d-73d518749df4/20250613_Wood_Chair_With_Brown_Cussions_processed.png
[HunyaunServer] Settings received: {
  ss_steps: 12,
  ss_cfg_strength: 7.5,
  slat_steps: 12,
  slat_cfg_strength: 3,
  randomize_seed: true,
  seed: 419655,
  simplify: 0.95,
  texture_size: 1024,
  enable_lighting_optimizer: true,
  octree_resolution: 128,
  num_inference_steps: 5,
  guidance_scale: 5,
  enable_texture: true,
  face_count: 40000
}
[HunyaunServer] Checking for lingering Python processes...
[HunyaunServer] No Python processes found to clean up (or cleanup failed)
[HunyaunServer] isHunyaunRunning() returned: false
[HunyaunServer] [Hunyaun Progress] Stage tracking reset for new generation
[HunyaunServer] Server not running, starting server...
info: [3D Progress] hunyaun_preprocessing: 0% (Overall: 0%) - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-18 00:06:05"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] Starting server...
[HunyaunServer] RUN_BAT: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\run-projectorz_(faster)\run-stableprojectorz-full-multiview.bat
[HunyaunServer] Batch file exists: true
[HunyaunServer] Hunyaun server process started
[HunyaunServer] startHunyaunServer() called successfully
[HunyaunServer] Waiting for Hunyaun server to be ready...
[HunyaunServer] [STDOUT] N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe"
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\get-pip.py"
[HunyaunServer] [STDOUT] DEBUG: VENV_PATH is "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv"
[HunyaunServer] [STDOUT] DEBUG: Checking for "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\activate.bat"
[HunyaunServer] [STDOUT] DEBUG: Virtual environment already exists, skipping creation
[HunyaunServer] [STDOUT]         1 file(s) copied.
[HunyaunServer] [STDOUT] Portable Python located at: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe
[HunyaunServer] [STDOUT] Virtual environment Python set to: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[HunyaunServer] [STDOUT] Starting the server, please wait...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 00:06:13"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 00:06:43"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 00:07:13"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 00:07:43"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 00:08:13"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 00:08:44"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 00:09:14"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun_preprocessing: 0% (Overall: 0%) - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-18 00:09:40"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] [System Info] Python: 3.11.9   | PyTorch: 2.5.1+cu124 | CUDA: 12.4
[HunyaunServer] [STDOUT] Initializing Hunyuan3D models from tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv
[HunyaunServer] [STDERR] 2025-07-18 00:09:43,700 - hy3dgen.shapgen - INFO - Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv

info: [3D Progress] hunyaun_preprocessing: 0% (Overall: 0%) - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-18 00:09:43"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDERR] 2025-07-18 00:09:43,702 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface

[HunyaunServer] [STDOUT] 2025-07-18 00:09:43 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 00:09:54"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Fetching 3 files:   0%|          | 0/3 [00:00<?, ?it/s]
Fetching 3 files: 100%|##########| 3/3 [00:00<?, ?it/s]

[HunyaunServer] [STDOUT] 2025-07-18 00:09:55 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
[HunyaunServer] [STDERR] 2025-07-18 00:09:55,116 - hy3dgen.shapgen - INFO - Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors

[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 00:10:24"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|##########| 13/13 [00:00<00:00, 6394.51it/s]

Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
Fetching 17 files: 100%|##########| 17/17 [00:00<00:00, 8441.24it/s]

Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
Loading pipeline components...:  17%|#6        | 1/6 [00:00<00:01,  3.18it/s]
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 00:10:54"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Loading pipeline components...:  33%|###3      | 2/6 [00:10<00:24,  6.03s/it]
info: [3D Progress] hunyaun_preprocessing: 0% (Overall: 0%) - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-18 00:11:04"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
Loading pipeline components...:  50%|#####     | 3/6 [00:10<00:10,  3.47s/it]
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Loading pipeline components...:  83%|########3 | 5/6 [00:11<00:01,  1.61s/it]
Loading pipeline components...: 100%|##########| 6/6 [00:11<00:00,  1.91s/it]

Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
Loading pipeline components...:  17%|#6        | 1/6 [00:00<00:00,  7.75it/s]
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 00:11:14"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Loading pipeline components...:  33%|###3      | 2/6 [00:27<01:04, 16.01s/it]
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Loading pipeline components...:  50%|#####     | 3/6 [00:34<00:35, 11.94s/it]
Loading pipeline components...:  83%|########3 | 5/6 [00:34<00:05,  5.22s/it]
Loading pipeline components...: 100%|##########| 6/6 [00:34<00:00,  5.76s/it]

[HunyaunServer] [STDOUT] 2025-07-18 00:11:40 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
info: [3D Progress] hunyaun_preprocessing: 0% (Overall: 0%) - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-18 00:11:40"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] VRAM allocated at startup: 0.0MB
[HunyaunServer] [STDOUT] Server is active and listening on 127.0.0.1:7960
[HunyaunServer] Server is running - HTTP endpoint responding
info: [3D Progress] hunyaun_preprocessing: 0% (Overall: 0%) - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-18 00:11:41"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] Mapped API settings: {
  guidance_scale: 5,
  num_inference_steps: 20,
  octree_resolution: 256,
  texture_size: 1024,
  mesh_simplify_ratio: 0.15,
  num_chunks: 40,
  apply_texture: true
}
[HunyaunServer] Final request data: {
  single_multi_img_input: [ '[IMAGE_DATA_OMITTED]' ],
  seed: 993802,
  guidance_scale: 5,
  num_inference_steps: 20,
  octree_resolution: 256,
  num_chunks: 40,
  mesh_simplify_ratio: 0.15,
  texture_size: 1024,
  apply_texture: true,
  output_format: 'glb'
}
[HunyaunServer] Sending generation request to Hunyaun server (no timeout)...
[HunyaunServer] [STDOUT] ==================================================
[HunyaunServer] [STDOUT] 2025-07-18 00:11:42 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
info: [3D Progress] hunyaun_preprocessing: 0% (Overall: 0%) - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-18 00:11:42"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] 2025-07-18 00:11:42 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
[HunyaunServer] [STDOUT] 2025-07-18 00:11:45 | INFO | hunyuan3d_api | Shape generation model moved to cuda
[HunyaunServer] Socket hang up during POST request - server may still be processing. Starting status polling...
[HunyaunServer] POST error details: ECONNRESET socket hang up
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 00:11:46"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 00:11:52"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::   0%|          | 0/20 [00:00<?, ?it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::   5%|5         | 1/20 [00:02<00:40,  2.11s/it]
Diffusion Sampling::  10%|#         | 2/20 [00:02<00:19,  1.07s/it]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 00:11:58"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  15%|#5        | 3/20 [00:03<00:18,  1.06s/it]
Diffusion Sampling::  20%|##        | 4/20 [00:04<00:16,  1.05s/it]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  25%|##5       | 5/20 [00:05<00:15,  1.04s/it]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:02"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Diffusion Sampling::  30%|###       | 6/20 [00:06<00:14,  1.04s/it]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 00:12:02"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  35%|###5      | 7/20 [00:07<00:13,  1.04s/it]
Diffusion Sampling::  40%|####      | 8/20 [00:08<00:12,  1.04s/it]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  45%|####5     | 9/20 [00:09<00:11,  1.04s/it]
Diffusion Sampling::  50%|#####     | 10/20 [00:10<00:10,  1.04s/it]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  55%|#####5    | 11/20 [00:11<00:09,  1.04s/it]
Diffusion Sampling::  60%|######    | 12/20 [00:12<00:08,  1.03s/it]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 00:12:08"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  65%|######5   | 13/20 [00:13<00:07,  1.04s/it]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  70%|#######   | 14/20 [00:14<00:06,  1.03s/it]
Diffusion Sampling::  75%|#######5  | 15/20 [00:15<00:05,  1.04s/it]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:12"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Diffusion Sampling::  80%|########  | 16/20 [00:16<00:04,  1.04s/it]
Diffusion Sampling::  85%|########5 | 17/20 [00:17<00:03,  1.04s/it]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  90%|######### | 18/20 [00:19<00:02,  1.03s/it]
Diffusion Sampling::  95%|#########5| 19/20 [00:20<00:01,  1.03s/it]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling:: 100%|##########| 20/20 [00:21<00:00,  1.05s/it]

Volume Decoding:   0%|          | 0/425 [00:00<?, ?it/s]
Volume Decoding:   0%|          | 1/425 [00:00<01:29,  4.73it/s]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 00:12:18"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:   9%|8         | 38/425 [00:00<00:02, 151.15it/s]
Volume Decoding:  14%|#4        | 60/425 [00:01<00:09, 37.22it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:20"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  17%|#7        | 73/425 [00:02<00:13, 26.21it/s]
Volume Decoding:  19%|#9        | 82/425 [00:02<00:15, 22.40it/s]
Volume Decoding:  21%|##        | 88/425 [00:03<00:16, 20.55it/s]
Volume Decoding:  22%|##1       | 93/425 [00:03<00:17, 19.31it/s]
Volume Decoding:  23%|##2       | 97/425 [00:03<00:17, 18.37it/s]
Volume Decoding:  24%|##3       | 100/425 [00:04<00:18, 17.67it/s]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 00:12:22"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  24%|##4       | 103/425 [00:04<00:18, 17.02it/s]
Volume Decoding:  25%|##4       | 106/425 [00:04<00:19, 16.45it/s]
Volume Decoding:  25%|##5       | 108/425 [00:04<00:19, 16.33it/s]
Volume Decoding:  26%|##5       | 110/425 [00:04<00:19, 15.82it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:23"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  26%|##6       | 112/425 [00:04<00:19, 15.75it/s]
Volume Decoding:  27%|##6       | 114/425 [00:05<00:20, 15.36it/s]
Volume Decoding:  27%|##7       | 116/425 [00:05<00:20, 15.10it/s]
Volume Decoding:  28%|##7       | 118/425 [00:05<00:20, 15.31it/s]
Volume Decoding:  28%|##8       | 120/425 [00:05<00:20, 14.86it/s]
Volume Decoding:  29%|##8       | 122/425 [00:05<00:20, 15.07it/s]
Volume Decoding:  29%|##9       | 124/425 [00:05<00:20, 14.71it/s]
Volume Decoding:  30%|##9       | 126/425 [00:05<00:19, 15.03it/s]
Volume Decoding:  30%|###       | 128/425 [00:06<00:20, 14.74it/s]
Volume Decoding:  31%|###       | 130/425 [00:06<00:20, 14.51it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:24"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  31%|###1      | 132/425 [00:06<00:19, 14.86it/s]
Volume Decoding:  32%|###1      | 134/425 [00:06<00:20, 14.52it/s]
Volume Decoding:  32%|###2      | 136/425 [00:06<00:19, 14.87it/s]
Volume Decoding:  32%|###2      | 138/425 [00:06<00:19, 14.56it/s]
Volume Decoding:  33%|###2      | 140/425 [00:06<00:19, 14.83it/s]
Volume Decoding:  33%|###3      | 142/425 [00:07<00:19, 14.57it/s]
Volume Decoding:  34%|###3      | 144/425 [00:07<00:18, 14.87it/s]
Volume Decoding:  34%|###4      | 146/425 [00:07<00:19, 14.59it/s]
Volume Decoding:  35%|###4      | 148/425 [00:07<00:18, 14.85it/s]
Volume Decoding:  35%|###5      | 150/425 [00:07<00:18, 14.64it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:25"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  36%|###5      | 152/425 [00:07<00:18, 14.54it/s]
Volume Decoding:  36%|###6      | 154/425 [00:07<00:18, 14.88it/s]
Volume Decoding:  37%|###6      | 156/425 [00:07<00:18, 14.63it/s]
Volume Decoding:  37%|###7      | 158/425 [00:08<00:18, 14.37it/s]
Volume Decoding:  38%|###7      | 160/425 [00:08<00:17, 14.76it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  38%|###8      | 162/425 [00:08<00:18, 14.52it/s]
Volume Decoding:  39%|###8      | 164/425 [00:08<00:17, 14.83it/s]
Volume Decoding:  39%|###9      | 166/425 [00:08<00:17, 14.57it/s]
Volume Decoding:  40%|###9      | 168/425 [00:08<00:17, 14.84it/s]
Volume Decoding:  40%|####      | 170/425 [00:08<00:17, 14.57it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:27"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  40%|####      | 172/425 [00:09<00:17, 14.87it/s]
Volume Decoding:  41%|####      | 174/425 [00:09<00:17, 14.56it/s]
Volume Decoding:  41%|####1     | 176/425 [00:09<00:16, 14.83it/s]
Volume Decoding:  42%|####1     | 178/425 [00:09<00:16, 14.54it/s]
Volume Decoding:  42%|####2     | 180/425 [00:09<00:16, 14.88it/s]
Volume Decoding:  43%|####2     | 182/425 [00:09<00:16, 14.54it/s]
Volume Decoding:  43%|####3     | 184/425 [00:09<00:16, 14.88it/s]
Volume Decoding:  44%|####3     | 186/425 [00:10<00:16, 14.66it/s]
Volume Decoding:  44%|####4     | 188/425 [00:10<00:16, 14.51it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  45%|####4     | 190/425 [00:10<00:15, 14.83it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:28"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  45%|####5     | 192/425 [00:10<00:16, 14.53it/s]
Volume Decoding:  46%|####5     | 194/425 [00:10<00:15, 14.85it/s]
Volume Decoding:  46%|####6     | 196/425 [00:10<00:15, 14.54it/s]
Volume Decoding:  47%|####6     | 198/425 [00:10<00:15, 14.82it/s]
Volume Decoding:  47%|####7     | 200/425 [00:10<00:15, 14.56it/s]
Volume Decoding:  48%|####7     | 202/425 [00:11<00:15, 14.86it/s]
Volume Decoding:  48%|####8     | 204/425 [00:11<00:15, 14.56it/s]
Volume Decoding:  48%|####8     | 206/425 [00:11<00:14, 14.83it/s]
Volume Decoding:  49%|####8     | 208/425 [00:11<00:14, 14.53it/s]
Volume Decoding:  49%|####9     | 210/425 [00:11<00:14, 14.91it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:29"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  50%|####9     | 212/425 [00:11<00:14, 14.62it/s]
Volume Decoding:  50%|#####     | 214/425 [00:11<00:14, 14.43it/s]
Volume Decoding:  51%|#####     | 216/425 [00:12<00:14, 14.73it/s]
Volume Decoding:  51%|#####1    | 218/425 [00:12<00:14, 14.50it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  52%|#####1    | 220/425 [00:12<00:13, 14.85it/s]
Volume Decoding:  52%|#####2    | 222/425 [00:12<00:13, 14.67it/s]
Volume Decoding:  53%|#####2    | 224/425 [00:12<00:13, 14.46it/s]
Volume Decoding:  53%|#####3    | 226/425 [00:12<00:13, 14.86it/s]
Volume Decoding:  54%|#####3    | 228/425 [00:12<00:13, 14.58it/s]
Volume Decoding:  54%|#####4    | 230/425 [00:13<00:13, 14.49it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:31"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  55%|#####4    | 232/425 [00:13<00:13, 14.78it/s]
Volume Decoding:  55%|#####5    | 234/425 [00:13<00:13, 14.53it/s]
Volume Decoding:  56%|#####5    | 236/425 [00:13<00:12, 14.88it/s]
Volume Decoding:  56%|#####6    | 238/425 [00:13<00:12, 14.57it/s]
Volume Decoding:  56%|#####6    | 240/425 [00:13<00:12, 14.90it/s]
Volume Decoding:  57%|#####6    | 242/425 [00:13<00:12, 14.62it/s]
Volume Decoding:  57%|#####7    | 244/425 [00:13<00:12, 14.87it/s]
Volume Decoding:  58%|#####7    | 246/425 [00:14<00:12, 14.59it/s]
Volume Decoding:  58%|#####8    | 248/425 [00:14<00:12, 14.38it/s]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 00:12:32"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  59%|#####8    | 250/425 [00:14<00:11, 14.73it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:32"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  59%|#####9    | 252/425 [00:14<00:11, 14.47it/s]
Volume Decoding:  60%|#####9    | 254/425 [00:14<00:11, 14.76it/s]
Volume Decoding:  60%|######    | 256/425 [00:14<00:11, 14.51it/s]
Volume Decoding:  61%|######    | 258/425 [00:14<00:11, 14.99it/s]
Volume Decoding:  61%|######1   | 260/425 [00:15<00:11, 14.72it/s]
Volume Decoding:  62%|######1   | 262/425 [00:15<00:11, 14.53it/s]
Volume Decoding:  62%|######2   | 264/425 [00:15<00:10, 14.84it/s]
Volume Decoding:  63%|######2   | 266/425 [00:15<00:10, 14.54it/s]
Volume Decoding:  63%|######3   | 268/425 [00:15<00:10, 14.85it/s]
Volume Decoding:  64%|######3   | 270/425 [00:15<00:10, 14.55it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:34"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  64%|######4   | 272/425 [00:15<00:10, 14.85it/s]
Volume Decoding:  64%|######4   | 274/425 [00:16<00:10, 14.58it/s]
Volume Decoding:  65%|######4   | 276/425 [00:16<00:10, 14.40it/s]
Volume Decoding:  65%|######5   | 278/425 [00:16<00:09, 14.78it/s]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 00:12:34"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  66%|######5   | 280/425 [00:16<00:09, 14.53it/s]
Volume Decoding:  66%|######6   | 282/425 [00:16<00:09, 14.87it/s]
Volume Decoding:  67%|######6   | 284/425 [00:16<00:09, 14.56it/s]
Volume Decoding:  67%|######7   | 286/425 [00:16<00:09, 14.84it/s]
Volume Decoding:  68%|######7   | 288/425 [00:16<00:09, 14.57it/s]
Volume Decoding:  68%|######8   | 290/425 [00:17<00:09, 14.42it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:35"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  69%|######8   | 292/425 [00:17<00:09, 14.76it/s]
Volume Decoding:  69%|######9   | 294/425 [00:17<00:08, 14.63it/s]
Volume Decoding:  70%|######9   | 296/425 [00:17<00:08, 14.92it/s]
Volume Decoding:  70%|#######   | 298/425 [00:17<00:08, 14.60it/s]
Volume Decoding:  71%|#######   | 300/425 [00:17<00:08, 14.89it/s]
Volume Decoding:  71%|#######1  | 302/425 [00:17<00:08, 14.64it/s]
Volume Decoding:  72%|#######1  | 304/425 [00:18<00:08, 14.44it/s]
Volume Decoding:  72%|#######2  | 306/425 [00:18<00:08, 14.81it/s]
Volume Decoding:  72%|#######2  | 308/425 [00:18<00:08, 14.49it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  73%|#######2  | 310/425 [00:18<00:07, 14.91it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:36"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  73%|#######3  | 312/425 [00:18<00:07, 14.59it/s]
Volume Decoding:  74%|#######3  | 314/425 [00:18<00:07, 14.40it/s]
Volume Decoding:  74%|#######4  | 316/425 [00:18<00:07, 14.83it/s]
Volume Decoding:  75%|#######4  | 318/425 [00:19<00:07, 14.58it/s]
Volume Decoding:  75%|#######5  | 320/425 [00:19<00:07, 14.91it/s]
Volume Decoding:  76%|#######5  | 322/425 [00:19<00:07, 14.59it/s]
Volume Decoding:  76%|#######6  | 324/425 [00:19<00:06, 14.92it/s]
Volume Decoding:  77%|#######6  | 326/425 [00:19<00:06, 14.56it/s]
Volume Decoding:  77%|#######7  | 328/425 [00:19<00:06, 14.90it/s]
Volume Decoding:  78%|#######7  | 330/425 [00:19<00:06, 14.78it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:38"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  78%|#######8  | 332/425 [00:19<00:06, 14.52it/s]
Volume Decoding:  79%|#######8  | 334/425 [00:20<00:06, 14.39it/s]
Volume Decoding:  79%|#######9  | 336/425 [00:20<00:06, 14.74it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  80%|#######9  | 338/425 [00:20<00:06, 14.48it/s]
Volume Decoding:  80%|########  | 340/425 [00:20<00:05, 14.77it/s]
Volume Decoding:  80%|########  | 342/425 [00:20<00:05, 14.52it/s]
Volume Decoding:  81%|########  | 344/425 [00:20<00:05, 14.87it/s]
Volume Decoding:  81%|########1 | 346/425 [00:20<00:05, 14.56it/s]
Volume Decoding:  82%|########1 | 348/425 [00:21<00:05, 14.93it/s]
Volume Decoding:  82%|########2 | 350/425 [00:21<00:05, 14.63it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:39"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  83%|########2 | 352/425 [00:21<00:05, 14.43it/s]
Volume Decoding:  83%|########3 | 354/425 [00:21<00:04, 14.74it/s]
Volume Decoding:  84%|########3 | 356/425 [00:21<00:04, 14.47it/s]
Volume Decoding:  84%|########4 | 358/425 [00:21<00:04, 14.80it/s]
Volume Decoding:  85%|########4 | 360/425 [00:21<00:04, 14.55it/s]
Volume Decoding:  85%|########5 | 362/425 [00:22<00:04, 14.85it/s]
Volume Decoding:  86%|########5 | 364/425 [00:22<00:04, 14.58it/s]
Volume Decoding:  86%|########6 | 366/425 [00:22<00:04, 14.54it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  87%|########6 | 368/425 [00:22<00:03, 14.90it/s]
Volume Decoding:  87%|########7 | 370/425 [00:22<00:03, 14.71it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:40"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  88%|########7 | 372/425 [00:22<00:03, 14.49it/s]
Volume Decoding:  88%|########8 | 374/425 [00:22<00:03, 14.84it/s]
Volume Decoding:  88%|########8 | 376/425 [00:22<00:03, 14.54it/s]
Volume Decoding:  89%|########8 | 378/425 [00:23<00:03, 14.85it/s]
Volume Decoding:  89%|########9 | 380/425 [00:23<00:03, 14.61it/s]
Volume Decoding:  90%|########9 | 382/425 [00:23<00:02, 14.42it/s]
Volume Decoding:  90%|######### | 384/425 [00:23<00:02, 14.75it/s]
Volume Decoding:  91%|######### | 386/425 [00:23<00:02, 14.51it/s]
Volume Decoding:  91%|#########1| 388/425 [00:23<00:02, 14.83it/s]
Volume Decoding:  92%|#########1| 390/425 [00:23<00:02, 14.56it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:42"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  92%|#########2| 392/425 [00:24<00:02, 14.83it/s]
Volume Decoding:  93%|#########2| 394/425 [00:24<00:02, 14.54it/s]
Volume Decoding:  93%|#########3| 396/425 [00:24<00:01, 14.91it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  94%|#########3| 398/425 [00:24<00:01, 14.56it/s]
Volume Decoding:  94%|#########4| 400/425 [00:24<00:01, 14.43it/s]
Volume Decoding:  95%|#########4| 402/425 [00:24<00:01, 14.84it/s]
Volume Decoding:  95%|#########5| 404/425 [00:24<00:01, 14.59it/s]
Volume Decoding:  96%|#########5| 406/425 [00:25<00:01, 14.88it/s]
Volume Decoding:  96%|#########6| 408/425 [00:25<00:01, 14.63it/s]
Volume Decoding:  96%|#########6| 410/425 [00:25<00:01, 14.43it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 00:12:43"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  97%|#########6| 412/425 [00:25<00:00, 14.80it/s]
Volume Decoding:  97%|#########7| 414/425 [00:25<00:00, 14.52it/s]
Volume Decoding:  98%|#########7| 416/425 [00:25<00:00, 14.83it/s]
Volume Decoding:  98%|#########8| 418/425 [00:25<00:00, 14.57it/s]
Volume Decoding:  99%|#########8| 420/425 [00:25<00:00, 14.90it/s]
Volume Decoding:  99%|#########9| 422/425 [00:26<00:00, 14.58it/s]
Volume Decoding: 100%|#########9| 424/425 [00:26<00:00, 14.85it/s]
Volume Decoding: 100%|##########| 425/425 [00:26<00:00, 16.15it/s]

info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 00:12:44"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 00:12:50"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
[HunyaunServer] [STDOUT] 2025-07-18 00:12:53 | INFO | hunyuan3d_api | Processing mesh (faces: 910208)
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:12:56"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 00:12:58 | INFO | hunyuan3d_api | Reducing faces to 864697
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:13:02"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 00:13:03 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 00:13:07 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
info: [3D Progress] hunyaun_export: 0% (Overall: 0%) - GLB Export: Finalizing 3D model file {"service":"user-service","timestamp":"2025-07-18 00:13:07"}
[IPC Handler] Sending status: hunyaun_export - 0% - GLB Export: Finalizing 3D model file
[HunyaunServer] [STDOUT] 2025-07-18 00:13:07 | INFO | hunyuan3d_api | Starting texture generation...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:13:12"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:13:18"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:13:24"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:13:31"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] Error polling /status endpoint (attempt 1/5): socket hang up
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:23:29"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 00:23:30 | INFO | hunyuan3d_api | Using chunked image encoding
[HunyaunServer] [STDOUT] 2025-07-18 00:23:31 | INFO | hunyuan3d_api | Using chunked image encoding
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 00:23:32 | INFO | hunyuan3d_api | Using chunked image encoding
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:23:35"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:23:41"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:23:47"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:23:53"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:24:00"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:24:06"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:24:12"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 00:24:15 | INFO | hunyuan3d_api | Using chunked VAE decoding
info: [3D Progress] hunyaun_export: 0% (Overall: 0%) - GLB Export: Finalizing 3D model file {"service":"user-service","timestamp":"2025-07-18 00:24:15"}
[IPC Handler] Sending status: hunyaun_export - 0% - GLB Export: Finalizing 3D model file
[HunyaunServer] [STDOUT] 2025-07-18 00:24:15 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:24:16"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:24:22"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] Error polling /status endpoint (attempt 1/5): socket hang up
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 00:24:49 | INFO | hunyuan3d_api | Applied texture to mesh successfully
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 00:24:52"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 00:24:53 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 719.42 seconds
[HunyaunServer] [STDOUT] 2025-07-18 00:24:53 | INFO | hunyuan3d_api | Generation completed in 791.86 seconds
info: [3D Progress] hunyaun: 100% (Overall: 100%) - Generation complete {"service":"user-service","timestamp":"2025-07-18 00:24:54"}
[IPC Handler] Sending status: hunyaun - 100% - Generation complete
[HunyaunServer] Downloading generated model...
[HunyaunServer] [STDOUT] 2025-07-18 00:24:54 | INFO | hunyuan3d_api | Client is downloading a model.
[HunyaunServer] Model saved to: N:\3D AI Studio\output\hunyaun_model_2025-07-18T05-24-54-357Z.glb
info: [3D Progress] hunyaun_export: 100% (Overall: 0%) - GLB Export: Finalizing 3D model file {"service":"user-service","timestamp":"2025-07-18 00:24:56"}
[IPC Handler] Sending status: hunyaun_export - 100% - GLB Export: Finalizing 3D model file
info: [load-file] Received request for: output\hunyaun_model_2025-07-18T05-24-54-357Z.glb {"service":"user-service","timestamp":"2025-07-18 00:24:56"}
info: [load-file] Reading absolute path: N:\3D AI Studio\output\hunyaun_model_2025-07-18T05-24-54-357Z.glb {"service":"user-service","timestamp":"2025-07-18 00:24:56"}
warn: [load-file] Received invalid request for: undefined {"service":"user-service","timestamp":"2025-07-18 00:24:56"}
