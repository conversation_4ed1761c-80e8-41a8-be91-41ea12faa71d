========================================
         3D AI Studio Launcher
========================================

[1/3] Checking Node.js installation...
Γ£ô Node.js found
[2/3] Checking for node_modules...
Γ£ô Dependencies ready
[3/3] Building frontend for production...
The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
vite v5.4.19 building for production...

(!) outDir N:\3D AI Studio\dist\renderer is not inside project root and will not be emptied.
Use --emptyOutDir to override.

✓ 2114 modules transformed.
../../dist/renderer/index.html                     0.21 kB │ gzip:   0.16 kB
../../dist/renderer/assets/index-CN-azzHY.css     44.66 kB │ gzip:   7.35 kB
../../dist/renderer/assets/index-E2ArCN4p.js      11.52 kB │ gzip:   4.64 kB
../../dist/renderer/assets/index-C8Uc91uX.js   1,354.14 kB │ gzip: 368.84 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 13.79s
Γ£ô Frontend build complete

========================================
    Starting 3D AI Studio (Production)...
========================================


> 3d-ai-studio@1.0.0 start
> electron .


info: Logger initialized. {"service":"user-service","timestamp":"2025-07-18 08:20:17"}
info: App is ready. {"service":"user-service","timestamp":"2025-07-18 08:20:17"}
info: Console window hidden at startup {"service":"user-service","timestamp":"2025-07-18 08:20:17"}
[Trellis Server] Checking for lingering Python processes...
[Trellis Server] Found Python processes, killing them...
[Trellis Server] Processes found: python.exe                   10904 Console                    1 26,268,960 K
[Trellis Server] Successfully killed Python processes
info: Python process cleanup completed on startup {"service":"user-service","timestamp":"2025-07-18 08:20:21"}
info: Production mode - Loading file: N:\3D AI Studio\dist\renderer\index.html {"service":"user-service","timestamp":"2025-07-18 08:20:21"}
info: Main window web contents started loading {"service":"user-service","timestamp":"2025-07-18 08:20:21"}
info: Splash screen initialization complete {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Splash Status: Loading pipeline configurations... {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Splash Status: Scanning pipelines... {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Loading pipelines from embedded configurations... {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Loaded pipeline: Core {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Loaded pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Loaded pipeline: ImageUpscaling {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Loaded pipeline: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Loaded pipeline: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Ensured pipeline directory exists: Core {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Ensured pipeline directory exists: ImageGeneration {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Ensured pipeline directory exists: ImageUpscaling {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Skipping directory creation for system package pipeline: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Skipping directory creation for system package pipeline: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Registering pipelines... {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Main window web contents finished loading {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Main window finished loading {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: Splash screen displayed {"service":"user-service","timestamp":"2025-07-18 08:20:22"}
info: IPC: get-console-visibility called {"service":"user-service","timestamp":"2025-07-18 08:20:23"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-18 08:20:23"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-18 08:20:23"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-18 08:20:23"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-18 08:20:24"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-18 08:20:24"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-18 08:20:24"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-18 08:20:24"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-18 08:20:24"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-18 08:20:24"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-18 08:20:24"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-18 08:20:25"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-18 08:20:25"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-18 08:20:25"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-18 08:20:25"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-18 08:20:25"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-18 08:20:25"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-18 08:20:25"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-18 08:20:26"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-18 08:20:26"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-18 08:20:26"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-18 08:20:26"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-18 08:20:26"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-18 08:20:26"}
info: IPC: get-config called for key: huggingface-token {"service":"user-service","timestamp":"2025-07-18 08:20:26"}
info: [DEBUG] Found pipeline config.json files: {"0":"N:\\3D AI Studio\\pipelines\\3DPipelines\\gen3d\\hunyuan2-spz-101\\config.json","1":"N:\\3D AI Studio\\pipelines\\3DPipelines\\gen3d\\trellis-stable-projectorz-101\\config.json","2":"N:\\3D AI Studio\\pipelines\\Core\\config.json","3":"N:\\3D AI Studio\\pipelines\\ImageGeneration\\config.json","4":"N:\\3D AI Studio\\pipelines\\ImageUpscaling\\config.json","service":"user-service","timestamp":"2025-07-18 08:20:41"}
info: Registered pipeline: hunyuan2-spz-101 (from N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\config.json) {"service":"user-service","timestamp":"2025-07-18 08:20:41"}
info: Registered pipeline: trellis-stable-projectorz-101 (from N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\config.json) {"service":"user-service","timestamp":"2025-07-18 08:20:41"}
info: Registered pipeline: Core (from N:\3D AI Studio\pipelines\Core\config.json) {"service":"user-service","timestamp":"2025-07-18 08:20:41"}
info: Registered pipeline: ImageGeneration (from N:\3D AI Studio\pipelines\ImageGeneration\config.json) {"service":"user-service","timestamp":"2025-07-18 08:20:41"}
info: Registered pipeline: ImageUpscaling (from N:\3D AI Studio\pipelines\ImageUpscaling\config.json) {"service":"user-service","timestamp":"2025-07-18 08:20:41"}
info: [DEBUG] Final registered pipelines: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-18 08:20:41"}
info: PipelineLoader: Final pipelines after merge: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-18 08:20:41"}
info: Splash Status: Checking core dependencies... {"service":"user-service","timestamp":"2025-07-18 08:20:41"}
info: Checking dependencies for Core {"service":"user-service","timestamp":"2025-07-18 08:20:41"}
info: Final dependency check for Core: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-18 08:20:45"}
info: Splash Status: Core dependencies verified {"service":"user-service","timestamp":"2025-07-18 08:20:45"}
info: Splash Status: Finalizing startup... {"service":"user-service","timestamp":"2025-07-18 08:20:45"}
info: Startup complete - showing main window {"service":"user-service","timestamp":"2025-07-18 08:20:48"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-18 08:20:48"}
info: Initialization complete signal sent to renderer {"service":"user-service","timestamp":"2025-07-18 08:20:48"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-18 08:20:48"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-07-18 08:20:48"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-18 08:20:48"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 08:38:36"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 08:38:36"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 08:38:36"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 08:38:39"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 08:38:39"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 08:38:39"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 08:38:39"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 08:38:39"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 08:38:39"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 08:38:39"}
info: Model dependency check for ImageGeneration: satisfied {"service":"user-service","timestamp":"2025-07-18 08:38:39"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 08:38:42"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 08:38:42"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 08:38:42"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 08:38:42"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 08:38:42"}
info: Model dependency check for ImageUpscaling: satisfied {"service":"user-service","timestamp":"2025-07-18 08:38:42"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 08:38:46"}
info: IPC: get-image-collections called {"service":"user-service","timestamp":"2025-07-18 08:38:56"}
info: Loaded 2 collections {"service":"user-service","timestamp":"2025-07-18 08:38:56"}
info: IPC: save-image-collections called {"service":"user-service","timestamp":"2025-07-18 08:38:56"}
info: Image collections saved successfully {"service":"user-service","timestamp":"2025-07-18 08:38:56"}
info: Starting streaming image generation with model: sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 08:39:07"}
info: Starting image generation with command: N:\3D AI Studio\pipelines\ImageGeneration\env\Scripts\python.exe utils\helpers\generate_image.py --prompt a butterfly --output N:\3D AI Studio\output\generated_1752845947878_d037063b-e027-4c9c-8a87-34ebdce47286.png --model sdxl-turbo --width 512 --height 512 --guidance_scale 0 --seed 930255471 --refiner_steps 10 --preview_quality high --result_file pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-18 08:39:07"}
info: ImageGeneration stderr: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton' {"service":"user-service","timestamp":"2025-07-18 08:41:36"}
info: ImageGeneration stdout: ImageGenerator initialized: {"service":"user-service","timestamp":"2025-07-18 08:41:39"}
info: ImageGeneration stdout: Device: cuda {"service":"user-service","timestamp":"2025-07-18 08:41:39"}
info: ImageGeneration stdout: Torch dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:41:39"}
info: ImageGeneration stdout: App root: N:\3D AI Studio {"service":"user-service","timestamp":"2025-07-18 08:41:39"}
info: ImageGeneration stdout: Models path: N:\3D AI Studio\models\ImageGeneration {"service":"user-service","timestamp":"2025-07-18 08:41:39"}
info: ImageGeneration stdout: Preview quality: high {"service":"user-service","timestamp":"2025-07-18 08:41:39"}
info: ImageGeneration stdout: Initializing sdxl-turbo model... {"service":"user-service","timestamp":"2025-07-18 08:41:39"}
info: ImageGeneration stdout: Loading model: sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 08:41:39"}
info: ImageGeneration stderr: Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-18 08:41:40"}
info: ImageGeneration stderr: An error occurred while trying to fetch N:\3D AI Studio\models\ImageGeneration\sdxl-turbo: Error no file named diffusion_pytorch_model.safetensors found in directory N:\3D AI Studio\models\ImageGeneration\sdxl-turbo.
Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-18 08:41:40"}
info: ImageGeneration stdout: Error loading model sdxl-turbo: Error no file named diffusion_pytorch_model.safetensors found in directory N:\3D AI Studio\models\ImageGeneration\sdxl-turbo. {"service":"user-service","timestamp":"2025-07-18 08:41:40"}
info: ImageGeneration stderr: Traceback (most recent call last):
  File "N:\3D AI Studio\utils\helpers\generate_image.py", line 559, in load_model
    self.pipeline = StableDiffusionXLPipeline.from_pretrained(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 1022, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 833, in load_sub_model
    loaded_sub_model = load_method(cached_folder, **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\modeling_utils.py", line 1153, in from_pretrained
    resolved_model_file = _get_model_file(
                          ^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\utils\hub_utils.py", line 254, in _get_model_file
    raise EnvironmentError(
OSError: Error no file named diffusion_pytorch_model.safetensors found in directory N:\3D AI Studio\models\ImageGeneration\sdxl-turbo. {"service":"user-service","timestamp":"2025-07-18 08:41:40"}
info: ImageGeneration stdout: {"success": false, "error": "Failed to load model sdxl-turbo", "execution_time": 0.5286455154418945} {"service":"user-service","timestamp":"2025-07-18 08:41:40"}
info: Image generation process exited with code: 1 {"service":"user-service","timestamp":"2025-07-18 08:41:42"}
error: Image generation failed with code 1: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton'
Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s]An error occurred while trying to fetch N:\3D AI Studio\models\ImageGeneration\sdxl-turbo: Error no file named diffusion_pytorch_model.safetensors found in directory N:\3D AI Studio\models\ImageGeneration\sdxl-turbo.
Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "N:\3D AI Studio\utils\helpers\generate_image.py", line 559, in load_model
    self.pipeline = StableDiffusionXLPipeline.from_pretrained(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 1022, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 833, in load_sub_model
    loaded_sub_model = load_method(cached_folder, **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\modeling_utils.py", line 1153, in from_pretrained
    resolved_model_file = _get_model_file(
                          ^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\utils\hub_utils.py", line 254, in _get_model_file
    raise EnvironmentError(
OSError: Error no file named diffusion_pytorch_model.safetensors found in directory N:\3D AI Studio\models\ImageGeneration\sdxl-turbo.
 {"service":"user-service","timestamp":"2025-07-18 08:41:42"}
error: Failed to generate image (streaming): Image generation failed with code 1: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton'
Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s]An error occurred while trying to fetch N:\3D AI Studio\models\ImageGeneration\sdxl-turbo: Error no file named diffusion_pytorch_model.safetensors found in directory N:\3D AI Studio\models\ImageGeneration\sdxl-turbo.
Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "N:\3D AI Studio\utils\helpers\generate_image.py", line 559, in load_model
    self.pipeline = StableDiffusionXLPipeline.from_pretrained(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 1022, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 833, in load_sub_model
    loaded_sub_model = load_method(cached_folder, **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\modeling_utils.py", line 1153, in from_pretrained
    resolved_model_file = _get_model_file(
                          ^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\utils\hub_utils.py", line 254, in _get_model_file
    raise EnvironmentError(
OSError: Error no file named diffusion_pytorch_model.safetensors found in directory N:\3D AI Studio\models\ImageGeneration\sdxl-turbo.
 {"service":"user-service","stack":"Error: Image generation failed with code 1: A matching Triton is not available, some optimizations will not be enabled\r\nTraceback (most recent call last):\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\xformers\\__init__.py\", line 57, in _is_triton_available\r\n    import triton  # noqa\r\n    ^^^^^^^^^^^^^\r\nModuleNotFoundError: No module named 'triton'\r\n\rLoading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s]An error occurred while trying to fetch N:\\3D AI Studio\\models\\ImageGeneration\\sdxl-turbo: Error no file named diffusion_pytorch_model.safetensors found in directory N:\\3D AI Studio\\models\\ImageGeneration\\sdxl-turbo.\r\n\rLoading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s]\r\nTraceback (most recent call last):\r\n  File \"N:\\3D AI Studio\\utils\\helpers\\generate_image.py\", line 559, in load_model\r\n    self.pipeline = StableDiffusionXLPipeline.from_pretrained(\r\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\huggingface_hub\\utils\\_validators.py\", line 114, in _inner_fn\r\n    return fn(*args, **kwargs)\r\n           ^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\pipelines\\pipeline_utils.py\", line 1022, in from_pretrained\r\n    loaded_sub_model = load_sub_model(\r\n                       ^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\pipelines\\pipeline_loading_utils.py\", line 833, in load_sub_model\r\n    loaded_sub_model = load_method(cached_folder, **loading_kwargs)\r\n                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\huggingface_hub\\utils\\_validators.py\", line 114, in _inner_fn\r\n    return fn(*args, **kwargs)\r\n           ^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\models\\modeling_utils.py\", line 1153, in from_pretrained\r\n    resolved_model_file = _get_model_file(\r\n                          ^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\huggingface_hub\\utils\\_validators.py\", line 114, in _inner_fn\r\n    return fn(*args, **kwargs)\r\n           ^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\utils\\hub_utils.py\", line 254, in _get_model_file\r\n    raise EnvironmentError(\r\nOSError: Error no file named diffusion_pytorch_model.safetensors found in directory N:\\3D AI Studio\\models\\ImageGeneration\\sdxl-turbo.\r\n\n    at ChildProcess.<anonymous> (N:\\3D AI Studio\\src\\main\\pipelineManager.js:349:36)\n    at ChildProcess.emit (node:events:519:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","timestamp":"2025-07-18 08:41:42"}
error: Error stack: {"0":"E","1":"r","10":"g","100":"i","1000":"I","1001":" ","1002":"S","1003":"t","1004":"u","1005":"d","1006":"i","1007":"o","1008":"\\","1009":"p","101":"l","1010":"i","1011":"p","1012":"e","1013":"l","1014":"i","1015":"n","1016":"e","1017":"s","1018":"\\","1019":"I","102":"l","1020":"m","1021":"a","1022":"g","1023":"e","1024":"G","1025":"e","1026":"n","1027":"e","1028":"r","1029":"a","103":" ","1030":"t","1031":"i","1032":"o","1033":"n","1034":"\\","1035":"e","1036":"n","1037":"v","1038":"\\","1039":"L","104":"n","1040":"i","1041":"b","1042":"\\","1043":"s","1044":"i","1045":"t","1046":"e","1047":"-","1048":"p","1049":"a","105":"o","1050":"c","1051":"k","1052":"a","1053":"g","1054":"e","1055":"s","1056":"\\","1057":"h","1058":"u","1059":"g","106":"t","1060":"g","1061":"i","1062":"n","1063":"g","1064":"f","1065":"a","1066":"c","1067":"e","1068":"_","1069":"h","107":" ","1070":"u","1071":"b","1072":"\\","1073":"u","1074":"t","1075":"i","1076":"l","1077":"s","1078":"\\","1079":"_","108":"b","1080":"v","1081":"a","1082":"l","1083":"i","1084":"d","1085":"a","1086":"t","1087":"o","1088":"r","1089":"s","109":"e","1090":".","1091":"p","1092":"y","1093":"\"","1094":",","1095":" ","1096":"l","1097":"i","1098":"n","1099":"e","11":"e","110":" ","1100":" ","1101":"1","1102":"1","1103":"4","1104":",","1105":" ","1106":"i","1107":"n","1108":" ","1109":"_","111":"e","1110":"i","1111":"n","1112":"n","1113":"e","1114":"r","1115":"_","1116":"f","1117":"n","1118":"\r","1119":"\n","112":"n","1120":" ","1121":" ","1122":" ","1123":" ","1124":"r","1125":"e","1126":"t","1127":"u","1128":"r","1129":"n","113":"a","1130":" ","1131":"f","1132":"n","1133":"(","1134":"*","1135":"a","1136":"r","1137":"g","1138":"s","1139":",","114":"b","1140":" ","1141":"*","1142":"*","1143":"k","1144":"w","1145":"a","1146":"r","1147":"g","1148":"s","1149":")","115":"l","1150":"\r","1151":"\n","1152":" ","1153":" ","1154":" ","1155":" ","1156":" ","1157":" ","1158":" ","1159":" ","116":"e","1160":" ","1161":" ","1162":" ","1163":"^","1164":"^","1165":"^","1166":"^","1167":"^","1168":"^","1169":"^","117":"d","1170":"^","1171":"^","1172":"^","1173":"^","1174":"^","1175":"^","1176":"^","1177":"^","1178":"^","1179":"^","118":"\r","1180":"^","1181":"^","1182":"\r","1183":"\n","1184":" ","1185":" ","1186":"F","1187":"i","1188":"l","1189":"e","119":"\n","1190":" ","1191":"\"","1192":"N","1193":":","1194":"\\","1195":"3","1196":"D","1197":" ","1198":"A","1199":"I","12":" ","120":"T","1200":" ","1201":"S","1202":"t","1203":"u","1204":"d","1205":"i","1206":"o","1207":"\\","1208":"p","1209":"i","121":"r","1210":"p","1211":"e","1212":"l","1213":"i","1214":"n","1215":"e","1216":"s","1217":"\\","1218":"I","1219":"m","122":"a","1220":"a","1221":"g","1222":"e","1223":"G","1224":"e","1225":"n","1226":"e","1227":"r","1228":"a","1229":"t","123":"c","1230":"i","1231":"o","1232":"n","1233":"\\","1234":"e","1235":"n","1236":"v","1237":"\\","1238":"L","1239":"i","124":"e","1240":"b","1241":"\\","1242":"s","1243":"i","1244":"t","1245":"e","1246":"-","1247":"p","1248":"a","1249":"c","125":"b","1250":"k","1251":"a","1252":"g","1253":"e","1254":"s","1255":"\\","1256":"d","1257":"i","1258":"f","1259":"f","126":"a","1260":"u","1261":"s","1262":"e","1263":"r","1264":"s","1265":"\\","1266":"p","1267":"i","1268":"p","1269":"e","127":"c","1270":"l","1271":"i","1272":"n","1273":"e","1274":"s","1275":"\\","1276":"p","1277":"i","1278":"p","1279":"e","128":"k","1280":"l","1281":"i","1282":"n","1283":"e","1284":"_","1285":"u","1286":"t","1287":"i","1288":"l","1289":"s","129":" ","1290":".","1291":"p","1292":"y","1293":"\"","1294":",","1295":" ","1296":"l","1297":"i","1298":"n","1299":"e","13":"g","130":"(","1300":" ","1301":"1","1302":"0","1303":"2","1304":"2","1305":",","1306":" ","1307":"i","1308":"n","1309":" ","131":"m","1310":"f","1311":"r","1312":"o","1313":"m","1314":"_","1315":"p","1316":"r","1317":"e","1318":"t","1319":"r","132":"o","1320":"a","1321":"i","1322":"n","1323":"e","1324":"d","1325":"\r","1326":"\n","1327":" ","1328":" ","1329":" ","133":"s","1330":" ","1331":"l","1332":"o","1333":"a","1334":"d","1335":"e","1336":"d","1337":"_","1338":"s","1339":"u","134":"t","1340":"b","1341":"_","1342":"m","1343":"o","1344":"d","1345":"e","1346":"l","1347":" ","1348":"=","1349":" ","135":" ","1350":"l","1351":"o","1352":"a","1353":"d","1354":"_","1355":"s","1356":"u","1357":"b","1358":"_","1359":"m","136":"r","1360":"o","1361":"d","1362":"e","1363":"l","1364":"(","1365":"\r","1366":"\n","1367":" ","1368":" ","1369":" ","137":"e","1370":" ","1371":" ","1372":" ","1373":" ","1374":" ","1375":" ","1376":" ","1377":" ","1378":" ","1379":" ","138":"c","1380":" ","1381":" ","1382":" ","1383":" ","1384":" ","1385":" ","1386":" ","1387":" ","1388":" ","1389":" ","139":"e","1390":"^","1391":"^","1392":"^","1393":"^","1394":"^","1395":"^","1396":"^","1397":"^","1398":"^","1399":"^","14":"e","140":"n","1400":"^","1401":"^","1402":"^","1403":"^","1404":"^","1405":"\r","1406":"\n","1407":" ","1408":" ","1409":"F","141":"t","1410":"i","1411":"l","1412":"e","1413":" ","1414":"\"","1415":"N","1416":":","1417":"\\","1418":"3","1419":"D","142":" ","1420":" ","1421":"A","1422":"I","1423":" ","1424":"S","1425":"t","1426":"u","1427":"d","1428":"i","1429":"o","143":"c","1430":"\\","1431":"p","1432":"i","1433":"p","1434":"e","1435":"l","1436":"i","1437":"n","1438":"e","1439":"s","144":"a","1440":"\\","1441":"I","1442":"m","1443":"a","1444":"g","1445":"e","1446":"G","1447":"e","1448":"n","1449":"e","145":"l","1450":"r","1451":"a","1452":"t","1453":"i","1454":"o","1455":"n","1456":"\\","1457":"e","1458":"n","1459":"v","146":"l","1460":"\\","1461":"L","1462":"i","1463":"b","1464":"\\","1465":"s","1466":"i","1467":"t","1468":"e","1469":"-","147":" ","1470":"p","1471":"a","1472":"c","1473":"k","1474":"a","1475":"g","1476":"e","1477":"s","1478":"\\","1479":"d","148":"l","1480":"i","1481":"f","1482":"f","1483":"u","1484":"s","1485":"e","1486":"r","1487":"s","1488":"\\","1489":"p","149":"a","1490":"i","1491":"p","1492":"e","1493":"l","1494":"i","1495":"n","1496":"e","1497":"s","1498":"\\","1499":"p","15":"n","150":"s","1500":"i","1501":"p","1502":"e","1503":"l","1504":"i","1505":"n","1506":"e","1507":"_","1508":"l","1509":"o","151":"t","1510":"a","1511":"d","1512":"i","1513":"n","1514":"g","1515":"_","1516":"u","1517":"t","1518":"i","1519":"l","152":")","1520":"s","1521":".","1522":"p","1523":"y","1524":"\"","1525":",","1526":" ","1527":"l","1528":"i","1529":"n","153":":","1530":"e","1531":" ","1532":"8","1533":"3","1534":"3","1535":",","1536":" ","1537":"i","1538":"n","1539":" ","154":"\r","1540":"l","1541":"o","1542":"a","1543":"d","1544":"_","1545":"s","1546":"u","1547":"b","1548":"_","1549":"m","155":"\n","1550":"o","1551":"d","1552":"e","1553":"l","1554":"\r","1555":"\n","1556":" ","1557":" ","1558":" ","1559":" ","156":" ","1560":"l","1561":"o","1562":"a","1563":"d","1564":"e","1565":"d","1566":"_","1567":"s","1568":"u","1569":"b","157":" ","1570":"_","1571":"m","1572":"o","1573":"d","1574":"e","1575":"l","1576":" ","1577":"=","1578":" ","1579":"l","158":"F","1580":"o","1581":"a","1582":"d","1583":"_","1584":"m","1585":"e","1586":"t","1587":"h","1588":"o","1589":"d","159":"i","1590":"(","1591":"c","1592":"a","1593":"c","1594":"h","1595":"e","1596":"d","1597":"_","1598":"f","1599":"o","16":"e","160":"l","1600":"l","1601":"d","1602":"e","1603":"r","1604":",","1605":" ","1606":"*","1607":"*","1608":"l","1609":"o","161":"e","1610":"a","1611":"d","1612":"i","1613":"n","1614":"g","1615":"_","1616":"k","1617":"w","1618":"a","1619":"r","162":" ","1620":"g","1621":"s","1622":")","1623":"\r","1624":"\n","1625":" ","1626":" ","1627":" ","1628":" ","1629":" ","163":"\"","1630":" ","1631":" ","1632":" ","1633":" ","1634":" ","1635":" ","1636":" ","1637":" ","1638":" ","1639":" ","164":"N","1640":" ","1641":" ","1642":" ","1643":" ","1644":" ","1645":" ","1646":" ","1647":" ","1648":"^","1649":"^","165":":","1650":"^","1651":"^","1652":"^","1653":"^","1654":"^","1655":"^","1656":"^","1657":"^","1658":"^","1659":"^","166":"\\","1660":"^","1661":"^","1662":"^","1663":"^","1664":"^","1665":"^","1666":"^","1667":"^","1668":"^","1669":"^","167":"3","1670":"^","1671":"^","1672":"^","1673":"^","1674":"^","1675":"^","1676":"^","1677":"^","1678":"^","1679":"^","168":"D","1680":"^","1681":"^","1682":"^","1683":"^","1684":"^","1685":"^","1686":"^","1687":"^","1688":"^","1689":"^","169":" ","1690":"^","1691":"^","1692":"\r","1693":"\n","1694":" ","1695":" ","1696":"F","1697":"i","1698":"l","1699":"e","17":"r","170":"A","1700":" ","1701":"\"","1702":"N","1703":":","1704":"\\","1705":"3","1706":"D","1707":" ","1708":"A","1709":"I","171":"I","1710":" ","1711":"S","1712":"t","1713":"u","1714":"d","1715":"i","1716":"o","1717":"\\","1718":"p","1719":"i","172":" ","1720":"p","1721":"e","1722":"l","1723":"i","1724":"n","1725":"e","1726":"s","1727":"\\","1728":"I","1729":"m","173":"S","1730":"a","1731":"g","1732":"e","1733":"G","1734":"e","1735":"n","1736":"e","1737":"r","1738":"a","1739":"t","174":"t","1740":"i","1741":"o","1742":"n","1743":"\\","1744":"e","1745":"n","1746":"v","1747":"\\","1748":"L","1749":"i","175":"u","1750":"b","1751":"\\","1752":"s","1753":"i","1754":"t","1755":"e","1756":"-","1757":"p","1758":"a","1759":"c","176":"d","1760":"k","1761":"a","1762":"g","1763":"e","1764":"s","1765":"\\","1766":"h","1767":"u","1768":"g","1769":"g","177":"i","1770":"i","1771":"n","1772":"g","1773":"f","1774":"a","1775":"c","1776":"e","1777":"_","1778":"h","1779":"u","178":"o","1780":"b","1781":"\\","1782":"u","1783":"t","1784":"i","1785":"l","1786":"s","1787":"\\","1788":"_","1789":"v","179":"\\","1790":"a","1791":"l","1792":"i","1793":"d","1794":"a","1795":"t","1796":"o","1797":"r","1798":"s","1799":".","18":"a","180":"p","1800":"p","1801":"y","1802":"\"","1803":",","1804":" ","1805":"l","1806":"i","1807":"n","1808":"e","1809":" ","181":"i","1810":"1","1811":"1","1812":"4","1813":",","1814":" ","1815":"i","1816":"n","1817":" ","1818":"_","1819":"i","182":"p","1820":"n","1821":"n","1822":"e","1823":"r","1824":"_","1825":"f","1826":"n","1827":"\r","1828":"\n","1829":" ","183":"e","1830":" ","1831":" ","1832":" ","1833":"r","1834":"e","1835":"t","1836":"u","1837":"r","1838":"n","1839":" ","184":"l","1840":"f","1841":"n","1842":"(","1843":"*","1844":"a","1845":"r","1846":"g","1847":"s","1848":",","1849":" ","185":"i","1850":"*","1851":"*","1852":"k","1853":"w","1854":"a","1855":"r","1856":"g","1857":"s","1858":")","1859":"\r","186":"n","1860":"\n","1861":" ","1862":" ","1863":" ","1864":" ","1865":" ","1866":" ","1867":" ","1868":" ","1869":" ","187":"e","1870":" ","1871":" ","1872":"^","1873":"^","1874":"^","1875":"^","1876":"^","1877":"^","1878":"^","1879":"^","188":"s","1880":"^","1881":"^","1882":"^","1883":"^","1884":"^","1885":"^","1886":"^","1887":"^","1888":"^","1889":"^","189":"\\","1890":"^","1891":"\r","1892":"\n","1893":" ","1894":" ","1895":"F","1896":"i","1897":"l","1898":"e","1899":" ","19":"t","190":"I","1900":"\"","1901":"N","1902":":","1903":"\\","1904":"3","1905":"D","1906":" ","1907":"A","1908":"I","1909":" ","191":"m","1910":"S","1911":"t","1912":"u","1913":"d","1914":"i","1915":"o","1916":"\\","1917":"p","1918":"i","1919":"p","192":"a","1920":"e","1921":"l","1922":"i","1923":"n","1924":"e","1925":"s","1926":"\\","1927":"I","1928":"m","1929":"a","193":"g","1930":"g","1931":"e","1932":"G","1933":"e","1934":"n","1935":"e","1936":"r","1937":"a","1938":"t","1939":"i","194":"e","1940":"o","1941":"n","1942":"\\","1943":"e","1944":"n","1945":"v","1946":"\\","1947":"L","1948":"i","1949":"b","195":"G","1950":"\\","1951":"s","1952":"i","1953":"t","1954":"e","1955":"-","1956":"p","1957":"a","1958":"c","1959":"k","196":"e","1960":"a","1961":"g","1962":"e","1963":"s","1964":"\\","1965":"d","1966":"i","1967":"f","1968":"f","1969":"u","197":"n","1970":"s","1971":"e","1972":"r","1973":"s","1974":"\\","1975":"m","1976":"o","1977":"d","1978":"e","1979":"l","198":"e","1980":"s","1981":"\\","1982":"m","1983":"o","1984":"d","1985":"e","1986":"l","1987":"i","1988":"n","1989":"g","199":"r","1990":"_","1991":"u","1992":"t","1993":"i","1994":"l","1995":"s","1996":".","1997":"p","1998":"y","1999":"\"","2":"r","20":"i","200":"a","2000":",","2001":" ","2002":"l","2003":"i","2004":"n","2005":"e","2006":" ","2007":"1","2008":"1","2009":"5","201":"t","2010":"3","2011":",","2012":" ","2013":"i","2014":"n","2015":" ","2016":"f","2017":"r","2018":"o","2019":"m","202":"i","2020":"_","2021":"p","2022":"r","2023":"e","2024":"t","2025":"r","2026":"a","2027":"i","2028":"n","2029":"e","203":"o","2030":"d","2031":"\r","2032":"\n","2033":" ","2034":" ","2035":" ","2036":" ","2037":"r","2038":"e","2039":"s","204":"n","2040":"o","2041":"l","2042":"v","2043":"e","2044":"d","2045":"_","2046":"m","2047":"o","2048":"d","2049":"e","205":"\\","2050":"l","2051":"_","2052":"f","2053":"i","2054":"l","2055":"e","2056":" ","2057":"=","2058":" ","2059":"_","206":"e","2060":"g","2061":"e","2062":"t","2063":"_","2064":"m","2065":"o","2066":"d","2067":"e","2068":"l","2069":"_","207":"n","2070":"f","2071":"i","2072":"l","2073":"e","2074":"(","2075":"\r","2076":"\n","2077":" ","2078":" ","2079":" ","208":"v","2080":" ","2081":" ","2082":" ","2083":" ","2084":" ","2085":" ","2086":" ","2087":" ","2088":" ","2089":" ","209":"\\","2090":" ","2091":" ","2092":" ","2093":" ","2094":" ","2095":" ","2096":" ","2097":" ","2098":" ","2099":" ","21":"o","210":"L","2100":" ","2101":" ","2102":" ","2103":"^","2104":"^","2105":"^","2106":"^","2107":"^","2108":"^","2109":"^","211":"i","2110":"^","2111":"^","2112":"^","2113":"^","2114":"^","2115":"^","2116":"^","2117":"^","2118":"^","2119":"\r","212":"b","2120":"\n","2121":" ","2122":" ","2123":"F","2124":"i","2125":"l","2126":"e","2127":" ","2128":"\"","2129":"N","213":"\\","2130":":","2131":"\\","2132":"3","2133":"D","2134":" ","2135":"A","2136":"I","2137":" ","2138":"S","2139":"t","214":"s","2140":"u","2141":"d","2142":"i","2143":"o","2144":"\\","2145":"p","2146":"i","2147":"p","2148":"e","2149":"l","215":"i","2150":"i","2151":"n","2152":"e","2153":"s","2154":"\\","2155":"I","2156":"m","2157":"a","2158":"g","2159":"e","216":"t","2160":"G","2161":"e","2162":"n","2163":"e","2164":"r","2165":"a","2166":"t","2167":"i","2168":"o","2169":"n","217":"e","2170":"\\","2171":"e","2172":"n","2173":"v","2174":"\\","2175":"L","2176":"i","2177":"b","2178":"\\","2179":"s","218":"-","2180":"i","2181":"t","2182":"e","2183":"-","2184":"p","2185":"a","2186":"c","2187":"k","2188":"a","2189":"g","219":"p","2190":"e","2191":"s","2192":"\\","2193":"h","2194":"u","2195":"g","2196":"g","2197":"i","2198":"n","2199":"g","22":"n","220":"a","2200":"f","2201":"a","2202":"c","2203":"e","2204":"_","2205":"h","2206":"u","2207":"b","2208":"\\","2209":"u","221":"c","2210":"t","2211":"i","2212":"l","2213":"s","2214":"\\","2215":"_","2216":"v","2217":"a","2218":"l","2219":"i","222":"k","2220":"d","2221":"a","2222":"t","2223":"o","2224":"r","2225":"s","2226":".","2227":"p","2228":"y","2229":"\"","223":"a","2230":",","2231":" ","2232":"l","2233":"i","2234":"n","2235":"e","2236":" ","2237":"1","2238":"1","2239":"4","224":"g","2240":",","2241":" ","2242":"i","2243":"n","2244":" ","2245":"_","2246":"i","2247":"n","2248":"n","2249":"e","225":"e","2250":"r","2251":"_","2252":"f","2253":"n","2254":"\r","2255":"\n","2256":" ","2257":" ","2258":" ","2259":" ","226":"s","2260":"r","2261":"e","2262":"t","2263":"u","2264":"r","2265":"n","2266":" ","2267":"f","2268":"n","2269":"(","227":"\\","2270":"*","2271":"a","2272":"r","2273":"g","2274":"s","2275":",","2276":" ","2277":"*","2278":"*","2279":"k","228":"x","2280":"w","2281":"a","2282":"r","2283":"g","2284":"s","2285":")","2286":"\r","2287":"\n","2288":" ","2289":" ","229":"f","2290":" ","2291":" ","2292":" ","2293":" ","2294":" ","2295":" ","2296":" ","2297":" ","2298":" ","2299":"^","23":" ","230":"o","2300":"^","2301":"^","2302":"^","2303":"^","2304":"^","2305":"^","2306":"^","2307":"^","2308":"^","2309":"^","231":"r","2310":"^","2311":"^","2312":"^","2313":"^","2314":"^","2315":"^","2316":"^","2317":"^","2318":"\r","2319":"\n","232":"m","2320":" ","2321":" ","2322":"F","2323":"i","2324":"l","2325":"e","2326":" ","2327":"\"","2328":"N","2329":":","233":"e","2330":"\\","2331":"3","2332":"D","2333":" ","2334":"A","2335":"I","2336":" ","2337":"S","2338":"t","2339":"u","234":"r","2340":"d","2341":"i","2342":"o","2343":"\\","2344":"p","2345":"i","2346":"p","2347":"e","2348":"l","2349":"i","235":"s","2350":"n","2351":"e","2352":"s","2353":"\\","2354":"I","2355":"m","2356":"a","2357":"g","2358":"e","2359":"G","236":"\\","2360":"e","2361":"n","2362":"e","2363":"r","2364":"a","2365":"t","2366":"i","2367":"o","2368":"n","2369":"\\","237":"_","2370":"e","2371":"n","2372":"v","2373":"\\","2374":"L","2375":"i","2376":"b","2377":"\\","2378":"s","2379":"i","238":"_","2380":"t","2381":"e","2382":"-","2383":"p","2384":"a","2385":"c","2386":"k","2387":"a","2388":"g","2389":"e","239":"i","2390":"s","2391":"\\","2392":"d","2393":"i","2394":"f","2395":"f","2396":"u","2397":"s","2398":"e","2399":"r","24":"f","240":"n","2400":"s","2401":"\\","2402":"u","2403":"t","2404":"i","2405":"l","2406":"s","2407":"\\","2408":"h","2409":"u","241":"i","2410":"b","2411":"_","2412":"u","2413":"t","2414":"i","2415":"l","2416":"s","2417":".","2418":"p","2419":"y","242":"t","2420":"\"","2421":",","2422":" ","2423":"l","2424":"i","2425":"n","2426":"e","2427":" ","2428":"2","2429":"5","243":"_","2430":"4","2431":",","2432":" ","2433":"i","2434":"n","2435":" ","2436":"_","2437":"g","2438":"e","2439":"t","244":"_","2440":"_","2441":"m","2442":"o","2443":"d","2444":"e","2445":"l","2446":"_","2447":"f","2448":"i","2449":"l","245":".","2450":"e","2451":"\r","2452":"\n","2453":" ","2454":" ","2455":" ","2456":" ","2457":"r","2458":"a","2459":"i","246":"p","2460":"s","2461":"e","2462":" ","2463":"E","2464":"n","2465":"v","2466":"i","2467":"r","2468":"o","2469":"n","247":"y","2470":"m","2471":"e","2472":"n","2473":"t","2474":"E","2475":"r","2476":"r","2477":"o","2478":"r","2479":"(","248":"\"","2480":"\r","2481":"\n","2482":"O","2483":"S","2484":"E","2485":"r","2486":"r","2487":"o","2488":"r","2489":":","249":",","2490":" ","2491":"E","2492":"r","2493":"r","2494":"o","2495":"r","2496":" ","2497":"n","2498":"o","2499":" ","25":"a","250":" ","2500":"f","2501":"i","2502":"l","2503":"e","2504":" ","2505":"n","2506":"a","2507":"m","2508":"e","2509":"d","251":"l","2510":" ","2511":"d","2512":"i","2513":"f","2514":"f","2515":"u","2516":"s","2517":"i","2518":"o","2519":"n","252":"i","2520":"_","2521":"p","2522":"y","2523":"t","2524":"o","2525":"r","2526":"c","2527":"h","2528":"_","2529":"m","253":"n","2530":"o","2531":"d","2532":"e","2533":"l","2534":".","2535":"s","2536":"a","2537":"f","2538":"e","2539":"t","254":"e","2540":"e","2541":"n","2542":"s","2543":"o","2544":"r","2545":"s","2546":" ","2547":"f","2548":"o","2549":"u","255":" ","2550":"n","2551":"d","2552":" ","2553":"i","2554":"n","2555":" ","2556":"d","2557":"i","2558":"r","2559":"e","256":"5","2560":"c","2561":"t","2562":"o","2563":"r","2564":"y","2565":" ","2566":"N","2567":":","2568":"\\","2569":"3","257":"7","2570":"D","2571":" ","2572":"A","2573":"I","2574":" ","2575":"S","2576":"t","2577":"u","2578":"d","2579":"i","258":",","2580":"o","2581":"\\","2582":"m","2583":"o","2584":"d","2585":"e","2586":"l","2587":"s","2588":"\\","2589":"I","259":" ","2590":"m","2591":"a","2592":"g","2593":"e","2594":"G","2595":"e","2596":"n","2597":"e","2598":"r","2599":"a","26":"i","260":"i","2600":"t","2601":"i","2602":"o","2603":"n","2604":"\\","2605":"s","2606":"d","2607":"x","2608":"l","2609":"-","261":"n","2610":"t","2611":"u","2612":"r","2613":"b","2614":"o","2615":".","2616":"\r","2617":"\n","2618":"\n","2619":" ","262":" ","2620":" ","2621":" ","2622":" ","2623":"a","2624":"t","2625":" ","2626":"C","2627":"h","2628":"i","2629":"l","263":"_","2630":"d","2631":"P","2632":"r","2633":"o","2634":"c","2635":"e","2636":"s","2637":"s","2638":".","2639":"<","264":"i","2640":"a","2641":"n","2642":"o","2643":"n","2644":"y","2645":"m","2646":"o","2647":"u","2648":"s","2649":">","265":"s","2650":" ","2651":"(","2652":"N","2653":":","2654":"\\","2655":"3","2656":"D","2657":" ","2658":"A","2659":"I","266":"_","2660":" ","2661":"S","2662":"t","2663":"u","2664":"d","2665":"i","2666":"o","2667":"\\","2668":"s","2669":"r","267":"t","2670":"c","2671":"\\","2672":"m","2673":"a","2674":"i","2675":"n","2676":"\\","2677":"p","2678":"i","2679":"p","268":"r","2680":"e","2681":"l","2682":"i","2683":"n","2684":"e","2685":"M","2686":"a","2687":"n","2688":"a","2689":"g","269":"i","2690":"e","2691":"r","2692":".","2693":"j","2694":"s","2695":":","2696":"3","2697":"4","2698":"9","2699":":","27":"l","270":"t","2700":"3","2701":"6","2702":")","2703":"\n","2704":" ","2705":" ","2706":" ","2707":" ","2708":"a","2709":"t","271":"o","2710":" ","2711":"C","2712":"h","2713":"i","2714":"l","2715":"d","2716":"P","2717":"r","2718":"o","2719":"c","272":"n","2720":"e","2721":"s","2722":"s","2723":".","2724":"e","2725":"m","2726":"i","2727":"t","2728":" ","2729":"(","273":"_","2730":"n","2731":"o","2732":"d","2733":"e","2734":":","2735":"e","2736":"v","2737":"e","2738":"n","2739":"t","274":"a","2740":"s","2741":":","2742":"5","2743":"1","2744":"9","2745":":","2746":"2","2747":"8","2748":")","2749":"\n","275":"v","2750":" ","2751":" ","2752":" ","2753":" ","2754":"a","2755":"t","2756":" ","2757":"m","2758":"a","2759":"y","276":"a","2760":"b","2761":"e","2762":"C","2763":"l","2764":"o","2765":"s","2766":"e","2767":" ","2768":"(","2769":"n","277":"i","2770":"o","2771":"d","2772":"e","2773":":","2774":"i","2775":"n","2776":"t","2777":"e","2778":"r","2779":"n","278":"l","2780":"a","2781":"l","2782":"/","2783":"c","2784":"h","2785":"i","2786":"l","2787":"d","2788":"_","2789":"p","279":"a","2790":"r","2791":"o","2792":"c","2793":"e","2794":"s","2795":"s","2796":":","2797":"1","2798":"1","2799":"0","28":"e","280":"b","2800":"5","2801":":","2802":"1","2803":"6","2804":")","2805":"\n","2806":" ","2807":" ","2808":" ","2809":" ","281":"l","2810":"a","2811":"t","2812":" ","2813":"C","2814":"h","2815":"i","2816":"l","2817":"d","2818":"P","2819":"r","282":"e","2820":"o","2821":"c","2822":"e","2823":"s","2824":"s","2825":".","2826":"_","2827":"h","2828":"a","2829":"n","283":"\r","2830":"d","2831":"l","2832":"e","2833":".","2834":"o","2835":"n","2836":"e","2837":"x","2838":"i","2839":"t","284":"\n","2840":" ","2841":"(","2842":"n","2843":"o","2844":"d","2845":"e","2846":":","2847":"i","2848":"n","2849":"t","285":" ","2850":"e","2851":"r","2852":"n","2853":"a","2854":"l","2855":"/","2856":"c","2857":"h","2858":"i","2859":"l","286":" ","2860":"d","2861":"_","2862":"p","2863":"r","2864":"o","2865":"c","2866":"e","2867":"s","2868":"s","2869":":","287":" ","2870":"3","2871":"0","2872":"5","2873":":","2874":"5","2875":")","288":" ","289":"i","29":"d","290":"m","291":"p","292":"o","293":"r","294":"t","295":" ","296":"t","297":"r","298":"i","299":"t","3":"o","30":" ","300":"o","301":"n","302":" ","303":" ","304":"#","305":" ","306":"n","307":"o","308":"q","309":"a","31":"w","310":"\r","311":"\n","312":" ","313":" ","314":" ","315":" ","316":"^","317":"^","318":"^","319":"^","32":"i","320":"^","321":"^","322":"^","323":"^","324":"^","325":"^","326":"^","327":"^","328":"^","329":"\r","33":"t","330":"\n","331":"M","332":"o","333":"d","334":"u","335":"l","336":"e","337":"N","338":"o","339":"t","34":"h","340":"F","341":"o","342":"u","343":"n","344":"d","345":"E","346":"r","347":"r","348":"o","349":"r","35":" ","350":":","351":" ","352":"N","353":"o","354":" ","355":"m","356":"o","357":"d","358":"u","359":"l","36":"c","360":"e","361":" ","362":"n","363":"a","364":"m","365":"e","366":"d","367":" ","368":"'","369":"t","37":"o","370":"r","371":"i","372":"t","373":"o","374":"n","375":"'","376":"\r","377":"\n","378":"\r","379":"L","38":"d","380":"o","381":"a","382":"d","383":"i","384":"n","385":"g","386":" ","387":"p","388":"i","389":"p","39":"e","390":"e","391":"l","392":"i","393":"n","394":"e","395":" ","396":"c","397":"o","398":"m","399":"p","4":"r","40":" ","400":"o","401":"n","402":"e","403":"n","404":"t","405":"s","406":".","407":".","408":".","409":":","41":"1","410":" ","411":" ","412":" ","413":"0","414":"%","415":"|","416":" ","417":" ","418":" ","419":" ","42":":","420":" ","421":" ","422":" ","423":" ","424":" ","425":" ","426":"|","427":" ","428":"0","429":"/","43":" ","430":"7","431":" ","432":"[","433":"0","434":"0","435":":","436":"0","437":"0","438":"<","439":"?","44":"A","440":",","441":" ","442":"?","443":"i","444":"t","445":"/","446":"s","447":"]","448":"A","449":"n","45":" ","450":" ","451":"e","452":"r","453":"r","454":"o","455":"r","456":" ","457":"o","458":"c","459":"c","46":"m","460":"u","461":"r","462":"r","463":"e","464":"d","465":" ","466":"w","467":"h","468":"i","469":"l","47":"a","470":"e","471":" ","472":"t","473":"r","474":"y","475":"i","476":"n","477":"g","478":" ","479":"t","48":"t","480":"o","481":" ","482":"f","483":"e","484":"t","485":"c","486":"h","487":" ","488":"N","489":":","49":"c","490":"\\","491":"3","492":"D","493":" ","494":"A","495":"I","496":" ","497":"S","498":"t","499":"u","5":":","50":"h","500":"d","501":"i","502":"o","503":"\\","504":"m","505":"o","506":"d","507":"e","508":"l","509":"s","51":"i","510":"\\","511":"I","512":"m","513":"a","514":"g","515":"e","516":"G","517":"e","518":"n","519":"e","52":"n","520":"r","521":"a","522":"t","523":"i","524":"o","525":"n","526":"\\","527":"s","528":"d","529":"x","53":"g","530":"l","531":"-","532":"t","533":"u","534":"r","535":"b","536":"o","537":":","538":" ","539":"E","54":" ","540":"r","541":"r","542":"o","543":"r","544":" ","545":"n","546":"o","547":" ","548":"f","549":"i","55":"T","550":"l","551":"e","552":" ","553":"n","554":"a","555":"m","556":"e","557":"d","558":" ","559":"d","56":"r","560":"i","561":"f","562":"f","563":"u","564":"s","565":"i","566":"o","567":"n","568":"_","569":"p","57":"i","570":"y","571":"t","572":"o","573":"r","574":"c","575":"h","576":"_","577":"m","578":"o","579":"d","58":"t","580":"e","581":"l","582":".","583":"s","584":"a","585":"f","586":"e","587":"t","588":"e","589":"n","59":"o","590":"s","591":"o","592":"r","593":"s","594":" ","595":"f","596":"o","597":"u","598":"n","599":"d","6":" ","60":"n","600":" ","601":"i","602":"n","603":" ","604":"d","605":"i","606":"r","607":"e","608":"c","609":"t","61":" ","610":"o","611":"r","612":"y","613":" ","614":"N","615":":","616":"\\","617":"3","618":"D","619":" ","62":"i","620":"A","621":"I","622":" ","623":"S","624":"t","625":"u","626":"d","627":"i","628":"o","629":"\\","63":"s","630":"m","631":"o","632":"d","633":"e","634":"l","635":"s","636":"\\","637":"I","638":"m","639":"a","64":" ","640":"g","641":"e","642":"G","643":"e","644":"n","645":"e","646":"r","647":"a","648":"t","649":"i","65":"n","650":"o","651":"n","652":"\\","653":"s","654":"d","655":"x","656":"l","657":"-","658":"t","659":"u","66":"o","660":"r","661":"b","662":"o","663":".","664":"\r","665":"\n","666":"\r","667":"L","668":"o","669":"a","67":"t","670":"d","671":"i","672":"n","673":"g","674":" ","675":"p","676":"i","677":"p","678":"e","679":"l","68":" ","680":"i","681":"n","682":"e","683":" ","684":"c","685":"o","686":"m","687":"p","688":"o","689":"n","69":"a","690":"e","691":"n","692":"t","693":"s","694":".","695":".","696":".","697":":","698":" ","699":" ","7":"I","70":"v","700":" ","701":"0","702":"%","703":"|","704":" ","705":" ","706":" ","707":" ","708":" ","709":" ","71":"a","710":" ","711":" ","712":" ","713":" ","714":"|","715":" ","716":"0","717":"/","718":"7","719":" ","72":"i","720":"[","721":"0","722":"0","723":":","724":"0","725":"0","726":"<","727":"?","728":",","729":" ","73":"l","730":"?","731":"i","732":"t","733":"/","734":"s","735":"]","736":"\r","737":"\n","738":"T","739":"r","74":"a","740":"a","741":"c","742":"e","743":"b","744":"a","745":"c","746":"k","747":" ","748":"(","749":"m","75":"b","750":"o","751":"s","752":"t","753":" ","754":"r","755":"e","756":"c","757":"e","758":"n","759":"t","76":"l","760":" ","761":"c","762":"a","763":"l","764":"l","765":" ","766":"l","767":"a","768":"s","769":"t","77":"e","770":")","771":":","772":"\r","773":"\n","774":" ","775":" ","776":"F","777":"i","778":"l","779":"e","78":",","780":" ","781":"\"","782":"N","783":":","784":"\\","785":"3","786":"D","787":" ","788":"A","789":"I","79":" ","790":" ","791":"S","792":"t","793":"u","794":"d","795":"i","796":"o","797":"\\","798":"u","799":"t","8":"m","80":"s","800":"i","801":"l","802":"s","803":"\\","804":"h","805":"e","806":"l","807":"p","808":"e","809":"r","81":"o","810":"s","811":"\\","812":"g","813":"e","814":"n","815":"e","816":"r","817":"a","818":"t","819":"e","82":"m","820":"_","821":"i","822":"m","823":"a","824":"g","825":"e","826":".","827":"p","828":"y","829":"\"","83":"e","830":",","831":" ","832":"l","833":"i","834":"n","835":"e","836":" ","837":"5","838":"5","839":"9","84":" ","840":",","841":" ","842":"i","843":"n","844":" ","845":"l","846":"o","847":"a","848":"d","849":"_","85":"o","850":"m","851":"o","852":"d","853":"e","854":"l","855":"\r","856":"\n","857":" ","858":" ","859":" ","86":"p","860":" ","861":"s","862":"e","863":"l","864":"f","865":".","866":"p","867":"i","868":"p","869":"e","87":"t","870":"l","871":"i","872":"n","873":"e","874":" ","875":"=","876":" ","877":"S","878":"t","879":"a","88":"i","880":"b","881":"l","882":"e","883":"D","884":"i","885":"f","886":"f","887":"u","888":"s","889":"i","89":"m","890":"o","891":"n","892":"X","893":"L","894":"P","895":"i","896":"p","897":"e","898":"l","899":"i","9":"a","90":"i","900":"n","901":"e","902":".","903":"f","904":"r","905":"o","906":"m","907":"_","908":"p","909":"r","91":"z","910":"e","911":"t","912":"r","913":"a","914":"i","915":"n","916":"e","917":"d","918":"(","919":"\r","92":"a","920":"\n","921":" ","922":" ","923":" ","924":" ","925":" ","926":" ","927":" ","928":" ","929":" ","93":"t","930":" ","931":" ","932":" ","933":" ","934":" ","935":" ","936":" ","937":" ","938":" ","939":" ","94":"i","940":" ","941":"^","942":"^","943":"^","944":"^","945":"^","946":"^","947":"^","948":"^","949":"^","95":"o","950":"^","951":"^","952":"^","953":"^","954":"^","955":"^","956":"^","957":"^","958":"^","959":"^","96":"n","960":"^","961":"^","962":"^","963":"^","964":"^","965":"^","966":"^","967":"^","968":"^","969":"^","97":"s","970":"^","971":"^","972":"^","973":"^","974":"^","975":"^","976":"^","977":"^","978":"^","979":"^","98":" ","980":"^","981":"^","982":"^","983":"\r","984":"\n","985":" ","986":" ","987":"F","988":"i","989":"l","99":"w","990":"e","991":" ","992":"\"","993":"N","994":":","995":"\\","996":"3","997":"D","998":" ","999":"A","service":"user-service","timestamp":"2025-07-18 08:41:42"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-18 08:41:56"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-18 08:41:56"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-18 08:41:56"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-18 08:41:57"}
info: IPC: get-image-collections called {"service":"user-service","timestamp":"2025-07-18 08:42:14"}
info: Loaded 2 collections {"service":"user-service","timestamp":"2025-07-18 08:42:14"}
info: IPC: save-image-collections called {"service":"user-service","timestamp":"2025-07-18 08:42:15"}
info: Image collections saved successfully {"service":"user-service","timestamp":"2025-07-18 08:42:15"}
info: Starting streaming image generation with model: stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 08:42:34"}
info: Starting image generation with command: N:\3D AI Studio\pipelines\ImageGeneration\env\Scripts\python.exe utils\helpers\generate_image.py --prompt a house in the rocky cliffside --output N:\3D AI Studio\output\generated_1752846154096_1c9b6d07-0c83-4ca3-bd11-f886aae88d13.png --model stable-diffusion-xl-base-1.0 --width 1024 --height 1024 --guidance_scale 7.5 --seed 920208992 --refiner_steps 10 --preview_quality high --result_file pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-18 08:42:34"}
info: ImageGeneration stderr: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton' {"service":"user-service","timestamp":"2025-07-18 08:42:50"}
info: ImageGeneration stdout: ImageGenerator initialized: {"service":"user-service","timestamp":"2025-07-18 08:42:50"}
info: ImageGeneration stdout: Device: cuda {"service":"user-service","timestamp":"2025-07-18 08:42:50"}
info: ImageGeneration stdout: Torch dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:42:50"}
info: ImageGeneration stdout: App root: N:\3D AI Studio {"service":"user-service","timestamp":"2025-07-18 08:42:50"}
info: ImageGeneration stdout: Models path: N:\3D AI Studio\models\ImageGeneration {"service":"user-service","timestamp":"2025-07-18 08:42:50"}
info: ImageGeneration stdout: Preview quality: high {"service":"user-service","timestamp":"2025-07-18 08:42:50"}
info: ImageGeneration stdout: Initializing stable-diffusion-xl-base-1.0 model... {"service":"user-service","timestamp":"2025-07-18 08:42:50"}
info: ImageGeneration stdout: Loading model: stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 08:42:50"}
info: ImageGeneration stderr: Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-18 08:42:50"}
info: ImageGeneration stderr: Loading pipeline components...:  14%|#4        | 1/7 [09:35<57:33, 575.60s/it] {"service":"user-service","timestamp":"2025-07-18 08:52:26"}
info: ImageGeneration stderr: Loading pipeline components...:  29%|##8       | 2/7 [10:07<21:17, 255.59s/it] {"service":"user-service","timestamp":"2025-07-18 08:52:58"}
info: ImageGeneration stderr: Loading pipeline components...:  43%|####2     | 3/7 [10:07<09:16, 139.01s/it] {"service":"user-service","timestamp":"2025-07-18 08:52:58"}
info: ImageGeneration stderr: Loading pipeline components...:  57%|#####7    | 4/7 [10:07<04:12, 84.20s/it] {"service":"user-service","timestamp":"2025-07-18 08:52:58"}
info: ImageGeneration stderr: Loading pipeline components...:  71%|#######1  | 5/7 [10:31<02:05, 62.56s/it] {"service":"user-service","timestamp":"2025-07-18 08:53:22"}
info: ImageGeneration stderr: Loading pipeline components...:  86%|########5 | 6/7 [13:01<01:32, 92.25s/it] {"service":"user-service","timestamp":"2025-07-18 08:55:52"}
Loading pipeline components...: 100%|##########| 7/7 [13:01<00:00, 111.69s/it] {"service":"user-service","timestamp":"2025-07-18 08:55:52"}
info: ImageGeneration stdout: Successfully loaded stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 08:56:01"}
info: ImageGeneration stdout: Starting image generation... {"service":"user-service","timestamp":"2025-07-18 08:56:01"}
info: ImageGeneration stdout: Generating image with prompt: 'a house in the rocky cliffside' {"service":"user-service","timestamp":"2025-07-18 08:56:01"}
info: ImageGeneration stdout: Parameters: 1024x1024, steps=20, guidance=7.5, seed=920208992 {"service":"user-service","timestamp":"2025-07-18 08:56:01"}
info: ImageGeneration stderr: N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1028: FutureWarning: `callback` is deprecated and will be removed in version 1.0.0. Passing `callback` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate(
N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1034: FutureWarning: `callback_steps` is deprecated and will be removed in version 1.0.0. Passing `callback_steps` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate( {"service":"user-service","timestamp":"2025-07-18 08:56:01"}
info: ImageGeneration stderr: 0%|          | 0/20 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-18 08:56:06"}
info: ImageGeneration stderr: 5%|5         | 1/20 [00:08<02:43,  8.61s/it] {"service":"user-service","timestamp":"2025-07-18 08:56:15"}
info: ImageGeneration stderr: Traceback (most recent call last):
  File "N:\3D AI Studio\utils\helpers\generate_image.py", line 227, in decode_latents_to_image
    decoded = vae.decode(latent_tensor).sample[0].cpu()
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\utils\accelerate_utils.py", line 46, in wrapper
    return method(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\autoencoders\autoencoder_kl.py", line 323, in decode
    decoded = self._decode(z).sample
              ^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\autoencoders\autoencoder_kl.py", line 292, in _decode
    z = self.post_quant_conv(z)
        ^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stderr: File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\torch\nn\modules\conv.py", line 554, in forward
    return self._conv_forward(input, self.weight, self.bias)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\torch\nn\modules\conv.py", line 549, in _conv_forward
    return F.conv2d(
           ^^^^^^^^^
RuntimeError: Expected all tensors to be on the same device, but found at least two devices, cpu and cuda:0! (when checking argument for argument weight in method wrapper_CUDA___slow_conv2d_forward) {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cpu, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Preview Decode] Failed to decode latents to image: Expected all tensors to be on the same device, but found at least two devices, cpu and cuda:0! (when checking argument for argument weight in method wrapper_CUDA___slow_conv2d_forward) {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Preview] High-quality decode returned None {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Preview] Attempting fast preview generation... {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Simple Preview] Input latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Simple Preview] Processing latents shape: torch.Size([4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Simple Preview] Combined latent image shape: torch.Size([128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Simple Preview] Created PIL image size: (128, 128) {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Simple Preview] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stdout: [Preview] Fast preview successful, base64 length: 188296 {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: Received preview image, base64 length: 188296 {"service":"user-service","timestamp":"2025-07-18 08:56:19"}
info: ImageGeneration stderr: 10%|#         | 2/20 [00:16<02:28,  8.26s/it] {"service":"user-service","timestamp":"2025-07-18 08:56:23"}
info: ImageGeneration stderr: N:\3D AI Studio\utils\helpers\generate_image.py:234: RuntimeWarning: invalid value encountered in cast
  np_img = (decoded.permute(1, 2, 0).numpy() * 255).astype('uint8') {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:56:28"}
info: ImageGeneration stderr: 15%|#5        | 3/20 [00:25<02:22,  8.39s/it] {"service":"user-service","timestamp":"2025-07-18 08:56:32"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:56:36"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:36"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:56:36"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:36"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:56:36"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:56:36"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:56:36"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:56:36"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:36"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:56:36"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:56:36"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:56:36"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:56:36"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:56:36"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:56:36"}
info: ImageGeneration stderr: 20%|##        | 4/20 [00:32<02:09,  8.12s/it] {"service":"user-service","timestamp":"2025-07-18 08:56:39"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:56:43"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:43"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:56:43"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:43"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:56:43"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:56:43"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:56:43"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:56:43"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:43"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:56:43"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:56:43"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:56:43"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:56:43"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:56:43"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:56:43"}
info: ImageGeneration stderr: 25%|##5       | 5/20 [00:40<01:59,  7.98s/it] {"service":"user-service","timestamp":"2025-07-18 08:56:47"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:56:51"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:51"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:56:51"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:51"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:56:51"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:56:51"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:56:51"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:56:51"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:51"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:56:51"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:56:51"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:56:51"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:56:51"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:56:51"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:56:51"}
info: ImageGeneration stderr: 30%|###       | 6/20 [00:48<01:50,  7.88s/it] {"service":"user-service","timestamp":"2025-07-18 08:56:55"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:56:59"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:59"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:56:59"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:59"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:56:59"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:56:59"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:56:59"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:56:59"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:56:59"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:56:59"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:56:59"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:56:59"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:56:59"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:56:59"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:56:59"}
info: ImageGeneration stderr: 35%|###5      | 7/20 [00:56<01:42,  7.88s/it] {"service":"user-service","timestamp":"2025-07-18 08:57:03"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:57:07"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:07"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:57:07"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:07"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:57:07"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:57:07"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:57:07"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:57:07"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:07"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:57:07"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:57:07"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:57:07"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:07"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:07"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:07"}
info: ImageGeneration stderr: 40%|####      | 8/20 [01:04<01:34,  7.88s/it] {"service":"user-service","timestamp":"2025-07-18 08:57:10"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:57:14"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:14"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:57:14"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:14"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:57:14"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:57:14"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:57:14"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:57:14"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:14"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:57:14"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:57:14"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:57:14"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 247544 {"service":"user-service","timestamp":"2025-07-18 08:57:14"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 247544 {"service":"user-service","timestamp":"2025-07-18 08:57:14"}
info: Received preview image, base64 length: 247544 {"service":"user-service","timestamp":"2025-07-18 08:57:14"}
info: ImageGeneration stderr: 45%|####5     | 9/20 [01:11<01:26,  7.84s/it] {"service":"user-service","timestamp":"2025-07-18 08:57:18"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:57:22"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:22"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:57:22"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:22"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:57:22"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:57:22"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:57:22"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:57:22"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:22"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:57:22"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:57:22"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:57:22"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:22"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:22"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:22"}
info: ImageGeneration stderr: 50%|#####     | 10/20 [01:19<01:17,  7.78s/it] {"service":"user-service","timestamp":"2025-07-18 08:57:26"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:57:30"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:30"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:57:30"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:30"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:57:30"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:57:30"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:57:30"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:57:30"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:30"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:57:30"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:57:30"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:57:30"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:30"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:30"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:30"}
info: ImageGeneration stderr: 55%|#####5    | 11/20 [01:27<01:09,  7.78s/it] {"service":"user-service","timestamp":"2025-07-18 08:57:34"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:57:37"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:37"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:57:37"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:37"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:57:37"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:57:37"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:57:37"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:57:37"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:37"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:57:37"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:57:37"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:57:37"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:37"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:37"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:37"}
info: ImageGeneration stderr: 60%|######    | 12/20 [01:34<01:01,  7.71s/it] {"service":"user-service","timestamp":"2025-07-18 08:57:41"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:57:45"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:45"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:57:45"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:45"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:57:45"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:57:45"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:57:45"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:57:45"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:45"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:57:45"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:57:45"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:57:45"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:45"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:45"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:45"}
info: ImageGeneration stderr: 65%|######5   | 13/20 [01:42<00:53,  7.71s/it] {"service":"user-service","timestamp":"2025-07-18 08:57:49"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:57:53"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:53"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:57:53"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:53"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:57:53"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:57:53"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:57:53"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:57:53"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:57:53"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:57:53"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:57:53"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:57:53"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:53"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:53"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:57:53"}
info: ImageGeneration stderr: 70%|#######   | 14/20 [01:49<00:45,  7.65s/it] {"service":"user-service","timestamp":"2025-07-18 08:57:56"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:58:01"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:01"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:58:01"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:01"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:58:01"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:58:01"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:58:01"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:58:01"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:01"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:58:01"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:58:01"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:58:01"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:01"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:01"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:01"}
info: ImageGeneration stderr: 75%|#######5  | 15/20 [01:58<00:38,  7.77s/it] {"service":"user-service","timestamp":"2025-07-18 08:58:04"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:58:08"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:08"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:58:08"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:08"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:58:08"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:58:08"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:58:08"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:58:08"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:08"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:58:08"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:58:08"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:58:08"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:08"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:08"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:08"}
info: ImageGeneration stderr: 80%|########  | 16/20 [02:05<00:31,  7.76s/it] {"service":"user-service","timestamp":"2025-07-18 08:58:12"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:58:16"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:16"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:58:16"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:16"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:58:16"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:58:16"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:58:16"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:58:16"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:16"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:58:16"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:58:16"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:58:16"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:16"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:16"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:16"}
info: ImageGeneration stderr: 85%|########5 | 17/20 [02:13<00:23,  7.74s/it] {"service":"user-service","timestamp":"2025-07-18 08:58:20"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:58:24"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:24"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:58:24"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:24"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:58:24"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:58:24"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:58:24"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:58:24"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:24"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:58:24"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:58:24"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:58:24"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:24"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:24"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:24"}
info: ImageGeneration stderr: 90%|######### | 18/20 [02:21<00:15,  7.76s/it] {"service":"user-service","timestamp":"2025-07-18 08:58:28"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:58:32"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:32"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:58:32"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:32"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:58:32"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:58:32"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:58:32"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:58:32"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:32"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:58:32"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:58:32"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:58:32"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:32"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:32"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:32"}
info: ImageGeneration stderr: 95%|#########5| 19/20 [02:28<00:07,  7.74s/it] {"service":"user-service","timestamp":"2025-07-18 08:58:35"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:58:39"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:39"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:58:39"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:39"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:58:39"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:58:39"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:58:39"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:58:39"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:39"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:58:39"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:58:39"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:58:39"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:39"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:39"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:39"}
info: ImageGeneration stderr: 100%|##########| 20/20 [02:36<00:00,  7.75s/it] {"service":"user-service","timestamp":"2025-07-18 08:58:43"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: ImageGeneration stderr: 100%|##########| 20/20 [02:40<00:00,  8.04s/it] {"service":"user-service","timestamp":"2025-07-18 08:58:47"}
info: ImageGeneration stdout: DEBUG: Also saved as JPEG: N:\3D AI Studio\output\generated_1752846154096_1c9b6d07-0c83-4ca3-bd11-f886aae88d13.jpg {"service":"user-service","timestamp":"2025-07-18 08:58:50"}
info: ImageGeneration stdout: Successfully generated image: N:\3D AI Studio\output\generated_1752846154096_1c9b6d07-0c83-4ca3-bd11-f886aae88d13.png {"service":"user-service","timestamp":"2025-07-18 08:58:50"}
info: ImageGeneration stdout: {"success": true, "output_path": "N:\\3D AI Studio\\output\\generated_1752846154096_1c9b6d07-0c83-4ca3-bd11-f886aae88d13.png", "model": "stable-diffusion-xl-base-1.0", "prompt": "a house in the rocky cliffside", "width": 1024, "height": 1024, "steps": 20, "guidance_scale": 7.5, "seed": 920208992, "execution_time": 959.8411948680878} {"service":"user-service","timestamp":"2025-07-18 08:58:50"}
info: Image generation process exited with code: 0 {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: Reading result file: pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: Image generation completed successfully: N:\3D AI Studio\output\generated_1752846154096_1c9b6d07-0c83-4ca3-bd11-f886aae88d13.png {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: Result details: success=true, model=stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: Pipeline result received: success=true, output_path=N:\3D AI Studio\output\generated_1752846154096_1c9b6d07-0c83-4ca3-bd11-f886aae88d13.png {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: Processing successful result... {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: Checking if image file exists: N:\3D AI Studio\output\generated_1752846154096_1c9b6d07-0c83-4ca3-bd11-f886aae88d13.png {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: Reading image file as base64... {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: Image converted to base64 successfully (2184524 chars) {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: Sending final result to frontend... {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: Final result sent to frontend successfully {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: IPC: add-image-to-gallery called for collection: Landscapes {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: Image saved to gallery: generated_1752847134592_dzz7e7.png {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: Image added to gallery: generated_1752847134592_dzz7e7.png {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: IPC: save-image-collections called {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: Image collections saved successfully {"service":"user-service","timestamp":"2025-07-18 08:58:54"}
info: Starting streaming image generation with model: stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 09:02:29"}
info: Starting image generation with command: N:\3D AI Studio\pipelines\ImageGeneration\env\Scripts\python.exe utils\helpers\generate_image.py --prompt a house in the rocky cliffside --output N:\3D AI Studio\output\generated_1752847349288_c54ca1da-d83e-4ed9-81aa-307385e14123.png --model stable-diffusion-xl-base-1.0 --width 1024 --height 1024 --guidance_scale 7.5 --seed 91366262 --refiner_steps 10 --preview_quality high --result_file pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-18 09:02:29"}
info: ImageGeneration stderr: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton' {"service":"user-service","timestamp":"2025-07-18 09:02:45"}
info: ImageGeneration stdout: ImageGenerator initialized: {"service":"user-service","timestamp":"2025-07-18 09:02:45"}
info: ImageGeneration stdout: Device: cuda {"service":"user-service","timestamp":"2025-07-18 09:02:45"}
info: ImageGeneration stdout: Torch dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:02:45"}
info: ImageGeneration stdout: App root: N:\3D AI Studio {"service":"user-service","timestamp":"2025-07-18 09:02:45"}
info: ImageGeneration stdout: Models path: N:\3D AI Studio\models\ImageGeneration {"service":"user-service","timestamp":"2025-07-18 09:02:45"}
info: ImageGeneration stdout: Preview quality: high {"service":"user-service","timestamp":"2025-07-18 09:02:45"}
info: ImageGeneration stdout: Initializing stable-diffusion-xl-base-1.0 model... {"service":"user-service","timestamp":"2025-07-18 09:02:45"}
info: ImageGeneration stdout: Loading model: stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 09:02:45"}
info: ImageGeneration stderr: Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-18 09:02:45"}
info: ImageGeneration stderr: Loading pipeline components...:  14%|#4        | 1/7 [00:02<00:12,  2.09s/it] {"service":"user-service","timestamp":"2025-07-18 09:02:47"}
info: ImageGeneration stderr: Loading pipeline components...:  29%|##8       | 2/7 [00:07<00:19,  3.99s/it] {"service":"user-service","timestamp":"2025-07-18 09:02:53"}
info: ImageGeneration stderr: Loading pipeline components...:  57%|#####7    | 4/7 [00:07<00:04,  1.55s/it] {"service":"user-service","timestamp":"2025-07-18 09:02:53"}
info: ImageGeneration stderr: Loading pipeline components...:  71%|#######1  | 5/7 [00:08<00:02,  1.23s/it] {"service":"user-service","timestamp":"2025-07-18 09:02:53"}
info: ImageGeneration stderr: Loading pipeline components...:  86%|########5 | 6/7 [00:08<00:01,  1.05s/it] {"service":"user-service","timestamp":"2025-07-18 09:02:54"}
info: ImageGeneration stderr: Loading pipeline components...: 100%|##########| 7/7 [00:08<00:00,  1.25s/it] {"service":"user-service","timestamp":"2025-07-18 09:02:54"}
info: ImageGeneration stdout: Successfully loaded stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 09:03:04"}
info: ImageGeneration stdout: Starting image generation... {"service":"user-service","timestamp":"2025-07-18 09:03:04"}
info: ImageGeneration stdout: Generating image with prompt: 'a house in the rocky cliffside' {"service":"user-service","timestamp":"2025-07-18 09:03:04"}
info: ImageGeneration stdout: Parameters: 1024x1024, steps=20, guidance=7.5, seed=91366262 {"service":"user-service","timestamp":"2025-07-18 09:03:04"}
info: ImageGeneration stderr: N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1028: FutureWarning: `callback` is deprecated and will be removed in version 1.0.0. Passing `callback` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate(
N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1034: FutureWarning: `callback_steps` is deprecated and will be removed in version 1.0.0. Passing `callback_steps` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
  deprecate( {"service":"user-service","timestamp":"2025-07-18 09:03:04"}
info: ImageGeneration stderr: 0%|          | 0/20 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-18 09:03:06"}
info: ImageGeneration stderr: 5%|5         | 1/20 [00:05<01:37,  5.14s/it] {"service":"user-service","timestamp":"2025-07-18 09:03:11"}
info: ImageGeneration stderr: Traceback (most recent call last): {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stderr: File "N:\3D AI Studio\utils\helpers\generate_image.py", line 227, in decode_latents_to_image
    decoded = vae.decode(latent_tensor).sample[0].cpu()
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\utils\accelerate_utils.py", line 46, in wrapper
    return method(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\autoencoders\autoencoder_kl.py", line 323, in decode
    decoded = self._decode(z).sample
              ^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\autoencoders\autoencoder_kl.py", line 292, in _decode
    z = self.post_quant_conv(z)
        ^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\torch\nn\modules\conv.py", line 554, in forward
    return self._conv_forward(input, self.weight, self.bias)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\torch\nn\modules\conv.py", line 549, in _conv_forward
    return F.conv2d(
           ^^^^^^^^^
RuntimeError: Expected all tensors to be on the same device, but found at least two devices, cpu and cuda:0! (when checking argument for argument weight in method wrapper_CUDA___slow_conv2d_forward) {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cpu, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Preview Decode] Failed to decode latents to image: Expected all tensors to be on the same device, but found at least two devices, cpu and cuda:0! (when checking argument for argument weight in method wrapper_CUDA___slow_conv2d_forward) {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Preview] High-quality decode returned None {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Preview] Attempting fast preview generation... {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Simple Preview] Input latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Simple Preview] Processing latents shape: torch.Size([4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Simple Preview] Combined latent image shape: torch.Size([128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Simple Preview] Created PIL image size: (128, 128) {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Simple Preview] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stdout: [Preview] Fast preview successful, base64 length: 189808 {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: Received preview image, base64 length: 189808 {"service":"user-service","timestamp":"2025-07-18 09:03:14"}
info: ImageGeneration stderr: 10%|#         | 2/20 [00:12<01:57,  6.51s/it] {"service":"user-service","timestamp":"2025-07-18 09:03:18"}
info: ImageGeneration stderr: N:\3D AI Studio\utils\helpers\generate_image.py:234: RuntimeWarning: invalid value encountered in cast
  np_img = (decoded.permute(1, 2, 0).numpy() * 255).astype('uint8') {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:03:23"}
info: ImageGeneration stderr: 15%|#5        | 3/20 [00:21<02:11,  7.75s/it] {"service":"user-service","timestamp":"2025-07-18 09:03:27"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:03:32"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:32"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:03:32"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:32"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:03:32"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:03:32"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:03:32"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:03:32"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:32"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:03:32"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:03:32"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:03:32"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:03:32"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:03:32"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:03:32"}
info: ImageGeneration stderr: 20%|##        | 4/20 [00:30<02:12,  8.26s/it] {"service":"user-service","timestamp":"2025-07-18 09:03:36"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:03:41"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:41"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:03:41"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:41"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:03:41"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:03:41"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:03:41"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:03:41"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:41"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:03:41"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:03:41"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:03:41"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:03:41"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:03:41"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:03:41"}
info: ImageGeneration stderr: 25%|##5       | 5/20 [00:39<02:08,  8.54s/it] {"service":"user-service","timestamp":"2025-07-18 09:03:46"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:03:50"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:50"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:03:50"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:50"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:03:50"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:03:50"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:03:50"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:03:50"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:50"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:03:50"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:03:50"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:03:50"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:03:50"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:03:50"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:03:50"}
info: ImageGeneration stderr: 30%|###       | 6/20 [00:48<02:00,  8.58s/it] {"service":"user-service","timestamp":"2025-07-18 09:03:54"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:03:59"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:59"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:03:59"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:59"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:03:59"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:03:59"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:03:59"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:03:59"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:03:59"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:03:59"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:03:59"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:03:59"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:03:59"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:03:59"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:03:59"}
info: ImageGeneration stderr: 35%|###5      | 7/20 [00:57<01:52,  8.69s/it] {"service":"user-service","timestamp":"2025-07-18 09:04:03"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:04:08"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:08"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:04:08"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:08"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:04:08"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:04:08"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:04:08"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:04:08"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:08"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:04:08"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:04:08"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:04:08"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:04:08"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:04:08"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:04:08"}
info: ImageGeneration stderr: 40%|####      | 8/20 [01:06<01:44,  8.71s/it] {"service":"user-service","timestamp":"2025-07-18 09:04:12"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:04:17"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:17"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:04:17"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:17"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:04:17"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:04:17"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:04:17"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:04:17"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:17"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:04:17"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:04:17"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:04:17"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:04:17"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:04:17"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:04:17"}
info: ImageGeneration stderr: 45%|####5     | 9/20 [01:15<01:36,  8.73s/it] {"service":"user-service","timestamp":"2025-07-18 09:04:21"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:04:25"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:25"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:04:25"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:25"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:04:25"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:04:25"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:04:25"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:04:25"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:25"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:04:25"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:04:25"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:04:25"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 248924 {"service":"user-service","timestamp":"2025-07-18 09:04:25"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 248924 {"service":"user-service","timestamp":"2025-07-18 09:04:25"}
info: Received preview image, base64 length: 248924 {"service":"user-service","timestamp":"2025-07-18 09:04:25"}
info: ImageGeneration stderr: 50%|#####     | 10/20 [01:23<01:27,  8.76s/it] {"service":"user-service","timestamp":"2025-07-18 09:04:29"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:04:34"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:34"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:04:34"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:34"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:04:34"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:04:34"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:04:34"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:04:34"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:34"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:04:34"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:04:34"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:04:34"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 249108 {"service":"user-service","timestamp":"2025-07-18 09:04:34"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 249108 {"service":"user-service","timestamp":"2025-07-18 09:04:34"}
info: Received preview image, base64 length: 249108 {"service":"user-service","timestamp":"2025-07-18 09:04:34"}
info: ImageGeneration stderr: 55%|#####5    | 11/20 [01:32<01:19,  8.80s/it] {"service":"user-service","timestamp":"2025-07-18 09:04:38"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:04:43"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:43"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:04:43"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:43"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:04:43"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:04:43"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:04:43"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:04:43"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:43"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:04:43"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:04:43"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:04:43"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:04:43"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:04:43"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:04:43"}
info: ImageGeneration stderr: 60%|######    | 12/20 [01:41<01:10,  8.84s/it] {"service":"user-service","timestamp":"2025-07-18 09:04:47"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:04:52"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:52"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:04:52"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:52"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:04:52"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:04:52"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:04:52"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:04:52"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:04:52"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:04:52"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:04:52"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:04:52"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:04:52"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:04:52"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:04:52"}
info: ImageGeneration stderr: 65%|######5   | 13/20 [01:50<01:02,  8.92s/it] {"service":"user-service","timestamp":"2025-07-18 09:04:56"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:05:01"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:01"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:05:01"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:01"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:05:01"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:05:01"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:05:01"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:05:01"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:01"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:05:01"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:05:01"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:05:01"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 232996 {"service":"user-service","timestamp":"2025-07-18 09:05:01"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 232996 {"service":"user-service","timestamp":"2025-07-18 09:05:01"}
info: Received preview image, base64 length: 232996 {"service":"user-service","timestamp":"2025-07-18 09:05:01"}
info: ImageGeneration stderr: 70%|#######   | 14/20 [01:59<00:53,  8.95s/it] {"service":"user-service","timestamp":"2025-07-18 09:05:05"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:05:10"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:10"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:05:10"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:10"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:05:10"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:05:10"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:05:10"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:05:10"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:10"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:05:10"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:05:10"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:05:10"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:10"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:10"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:10"}
info: ImageGeneration stderr: 75%|#######5  | 15/20 [02:08<00:45,  9.03s/it] {"service":"user-service","timestamp":"2025-07-18 09:05:15"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:05:19"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:19"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:05:19"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:19"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:05:19"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:05:19"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:05:19"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:05:19"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:19"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:05:19"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:05:19"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:05:19"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:19"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:19"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:19"}
info: ImageGeneration stderr: 80%|########  | 16/20 [02:17<00:35,  8.93s/it] {"service":"user-service","timestamp":"2025-07-18 09:05:23"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:05:28"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:28"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:05:28"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:28"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:05:28"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:05:28"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:05:28"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:05:28"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:28"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:05:28"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:05:28"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:05:28"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:28"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:28"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:28"}
info: ImageGeneration stderr: 85%|########5 | 17/20 [02:26<00:26,  8.90s/it] {"service":"user-service","timestamp":"2025-07-18 09:05:32"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:05:37"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:37"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:05:37"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:37"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:05:37"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:05:37"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:05:37"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:05:37"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:37"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:05:37"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:05:37"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:05:37"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:37"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:37"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:37"}
info: ImageGeneration stderr: 90%|######### | 18/20 [02:35<00:17,  8.96s/it] {"service":"user-service","timestamp":"2025-07-18 09:05:41"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:05:46"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:46"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:05:46"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:46"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:05:46"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:05:46"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:05:46"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:05:46"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:46"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:05:46"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:05:46"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:05:46"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:46"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:46"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:46"}
info: ImageGeneration stderr: 95%|#########5| 19/20 [02:44<00:09,  9.01s/it] {"service":"user-service","timestamp":"2025-07-18 09:05:50"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:05:55"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:55"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:05:55"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:55"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:05:55"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:05:55"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:05:55"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:05:55"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:05:55"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:05:55"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:05:55"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:05:55"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:55"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:55"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:05:55"}
info: ImageGeneration stderr: 100%|##########| 20/20 [02:53<00:00,  8.94s/it] {"service":"user-service","timestamp":"2025-07-18 09:05:59"}
info: ImageGeneration stdout: [Preview] Generating preview image, quality: high {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: ImageGeneration stdout: [Preview] Latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: ImageGeneration stdout: [Preview] Attempting high-quality decode... {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: ImageGeneration stdout: [Preview Decode] Starting decode with latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: ImageGeneration stdout: [Preview Decode] Latents device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: ImageGeneration stdout: [Preview Decode] Using pipeline VAE {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: ImageGeneration stdout: [Preview Decode] Using scaling factor: 0.13025 {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: ImageGeneration stdout: [Preview Decode] VAE device: cuda:0, dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: ImageGeneration stdout: [Preview Decode] Scaled latents shape: torch.Size([1, 4, 128, 128]) {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: ImageGeneration stdout: [Preview Decode] Decoded shape: torch.Size([3, 1024, 1024]) {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: ImageGeneration stdout: [Preview Decode] PIL image size: (1024, 1024) {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: ImageGeneration stdout: [Preview Decode] Resized to: (512, 512) {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: ImageGeneration stdout: [Preview Decode] Successfully encoded to base64, length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: ImageGeneration stdout: [Preview] High-quality decode successful, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: Received preview image, base64 length: 6300 {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: ImageGeneration stderr: 100%|##########| 20/20 [02:58<00:00,  8.92s/it] {"service":"user-service","timestamp":"2025-07-18 09:06:04"}
info: ImageGeneration stdout: DEBUG: Also saved as JPEG: N:\3D AI Studio\output\generated_1752847349288_c54ca1da-d83e-4ed9-81aa-307385e14123.jpg {"service":"user-service","timestamp":"2025-07-18 09:06:06"}
info: ImageGeneration stdout: Successfully generated image: N:\3D AI Studio\output\generated_1752847349288_c54ca1da-d83e-4ed9-81aa-307385e14123.png {"service":"user-service","timestamp":"2025-07-18 09:06:06"}
info: ImageGeneration stdout: {"success": true, "output_path": "N:\\3D AI Studio\\output\\generated_1752847349288_c54ca1da-d83e-4ed9-81aa-307385e14123.png", "model": "stable-diffusion-xl-base-1.0", "prompt": "a house in the rocky cliffside", "width": 1024, "height": 1024, "steps": 20, "guidance_scale": 7.5, "seed": 91366262, "execution_time": 200.70107913017273} {"service":"user-service","timestamp":"2025-07-18 09:06:06"}
info: Image generation process exited with code: 0 {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: Reading result file: pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: Image generation completed successfully: N:\3D AI Studio\output\generated_1752847349288_c54ca1da-d83e-4ed9-81aa-307385e14123.png {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: Result details: success=true, model=stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: Pipeline result received: success=true, output_path=N:\3D AI Studio\output\generated_1752847349288_c54ca1da-d83e-4ed9-81aa-307385e14123.png {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: Processing successful result... {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: Checking if image file exists: N:\3D AI Studio\output\generated_1752847349288_c54ca1da-d83e-4ed9-81aa-307385e14123.png {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: Reading image file as base64... {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: Image converted to base64 successfully (2450416 chars) {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: Sending final result to frontend... {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: Final result sent to frontend successfully {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: IPC: add-image-to-gallery called for collection: Landscapes {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: Image saved to gallery: generated_1752847570354_1cx0fn.png {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: Image added to gallery: generated_1752847570354_1cx0fn.png {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: IPC: save-image-collections called {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: Image collections saved successfully {"service":"user-service","timestamp":"2025-07-18 09:06:10"}
info: Starting streaming image generation with model: stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 09:06:47"}
info: Starting image generation with command: N:\3D AI Studio\pipelines\ImageGeneration\env\Scripts\python.exe utils\helpers\generate_image.py --prompt a house in the rocky cliffside --output N:\3D AI Studio\output\generated_1752847607633_370acf39-4eb7-4c3c-a928-c57ec0758d11.png --model stable-diffusion-v1-5 --width 512 --height 512 --guidance_scale 7.5 --seed 1383301059 --refiner_steps 10 --preview_quality high --result_file pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-18 09:06:47"}
info: ImageGeneration stderr: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton' {"service":"user-service","timestamp":"2025-07-18 09:07:03"}
info: ImageGeneration stdout: ImageGenerator initialized: {"service":"user-service","timestamp":"2025-07-18 09:07:04"}
info: ImageGeneration stdout: Device: cuda {"service":"user-service","timestamp":"2025-07-18 09:07:04"}
info: ImageGeneration stdout: Torch dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:07:04"}
info: ImageGeneration stdout: App root: N:\3D AI Studio {"service":"user-service","timestamp":"2025-07-18 09:07:04"}
info: ImageGeneration stdout: Models path: N:\3D AI Studio\models\ImageGeneration {"service":"user-service","timestamp":"2025-07-18 09:07:04"}
info: ImageGeneration stdout: Preview quality: high {"service":"user-service","timestamp":"2025-07-18 09:07:04"}
info: ImageGeneration stdout: Initializing stable-diffusion-v1-5 model... {"service":"user-service","timestamp":"2025-07-18 09:07:04"}
info: ImageGeneration stdout: Loading model: stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 09:07:04"}
info: ImageGeneration stderr: Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-18 09:07:04"}
info: ImageGeneration stderr: An error occurred while trying to fetch N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5\unet: Error no file named diffusion_pytorch_model.safetensors found in directory N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5\unet. {"service":"user-service","timestamp":"2025-07-18 09:07:04"}
info: ImageGeneration stderr: Loading pipeline components...:  14%|#4        | 1/7 [03:16<19:38, 196.48s/it] {"service":"user-service","timestamp":"2025-07-18 09:10:20"}
info: ImageGeneration stderr: Loading pipeline components...:  29%|##8       | 2/7 [03:42<08:02, 96.40s/it] {"service":"user-service","timestamp":"2025-07-18 09:10:47"}
info: ImageGeneration stderr: Loading pipeline components...:  57%|#####7    | 4/7 [03:42<01:48, 36.01s/it] {"service":"user-service","timestamp":"2025-07-18 09:10:47"}
info: ImageGeneration stderr: Loading pipeline components...:  57%|#####7    | 4/7 [04:00<01:48, 36.01s/it] {"service":"user-service","timestamp":"2025-07-18 09:11:04"}
info: ImageGeneration stderr: Loading pipeline components...:  71%|#######1  | 5/7 [04:47<01:29, 44.93s/it] {"service":"user-service","timestamp":"2025-07-18 09:11:52"}
info: ImageGeneration stderr: Loading pipeline components...:  86%|########5 | 6/7 [04:47<00:47, 47.98s/it] {"service":"user-service","timestamp":"2025-07-18 09:11:52"}
info: ImageGeneration stdout: Error loading model stable-diffusion-v1-5: Error no file named config.json found in directory N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5. {"service":"user-service","timestamp":"2025-07-18 09:11:52"}
info: ImageGeneration stderr: Traceback (most recent call last):
  File "N:\3D AI Studio\utils\helpers\generate_image.py", line 609, in load_model
    self.pipeline = StableDiffusionPipeline.from_pretrained(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ {"service":"user-service","timestamp":"2025-07-18 09:11:52"}
info: ImageGeneration stderr: File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 1022, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 833, in load_sub_model
    loaded_sub_model = load_method(cached_folder, **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\modeling_utils.py", line 1014, in from_pretrained
    config, unused_kwargs, commit_hash = cls.load_config(
                                         ^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\configuration_utils.py", line 384, in load_config
    raise EnvironmentError(
OSError: Error no file named config.json found in directory N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5. {"service":"user-service","timestamp":"2025-07-18 09:11:52"}
info: ImageGeneration stdout: {"success": false, "error": "Failed to load model stable-diffusion-v1-5", "execution_time": 287.93039059638977} {"service":"user-service","timestamp":"2025-07-18 09:11:52"}
info: Image generation process exited with code: 1 {"service":"user-service","timestamp":"2025-07-18 09:11:53"}
error: Image generation failed with code 1: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton'
Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s]An error occurred while trying to fetch N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5\unet: Error no file named diffusion_pytorch_model.safetensors found in directory N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5\unet.
Loading pipeline components...:  86%|########5 | 6/7 [04:47<00:47, 47.98s/it]
Traceback (most recent call last):
  File "N:\3D AI Studio\utils\helpers\generate_image.py", line 609, in load_model
    self.pipeline = StableDiffusionPipeline.from_pretrained(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 1022, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 833, in load_sub_model
    loaded_sub_model = load_method(cached_folder, **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\modeling_utils.py", line 1014, in from_pretrained
    config, unused_kwargs, commit_hash = cls.load_config(
                                         ^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\configuration_utils.py", line 384, in load_config
    raise EnvironmentError(
OSError: Error no file named config.json found in directory N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5.
 {"service":"user-service","timestamp":"2025-07-18 09:11:53"}
error: Failed to generate image (streaming): Image generation failed with code 1: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton'
Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s]An error occurred while trying to fetch N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5\unet: Error no file named diffusion_pytorch_model.safetensors found in directory N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5\unet.
Loading pipeline components...:  86%|########5 | 6/7 [04:47<00:47, 47.98s/it]
Traceback (most recent call last):
  File "N:\3D AI Studio\utils\helpers\generate_image.py", line 609, in load_model
    self.pipeline = StableDiffusionPipeline.from_pretrained(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 1022, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 833, in load_sub_model
    loaded_sub_model = load_method(cached_folder, **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\modeling_utils.py", line 1014, in from_pretrained
    config, unused_kwargs, commit_hash = cls.load_config(
                                         ^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\configuration_utils.py", line 384, in load_config
    raise EnvironmentError(
OSError: Error no file named config.json found in directory N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5.
 {"service":"user-service","stack":"Error: Image generation failed with code 1: A matching Triton is not available, some optimizations will not be enabled\r\nTraceback (most recent call last):\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\xformers\\__init__.py\", line 57, in _is_triton_available\r\n    import triton  # noqa\r\n    ^^^^^^^^^^^^^\r\nModuleNotFoundError: No module named 'triton'\r\n\rLoading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s]An error occurred while trying to fetch N:\\3D AI Studio\\models\\ImageGeneration\\stable-diffusion-v1-5\\unet: Error no file named diffusion_pytorch_model.safetensors found in directory N:\\3D AI Studio\\models\\ImageGeneration\\stable-diffusion-v1-5\\unet.\r\n\rLoading pipeline components...:  14%|#4        | 1/7 [03:16<19:38, 196.48s/it]\rLoading pipeline components...:  29%|##8       | 2/7 [03:42<08:02, 96.40s/it] \rLoading pipeline components...:  57%|#####7    | 4/7 [03:42<01:48, 36.01s/it]\rLoading pipeline components...:  57%|#####7    | 4/7 [04:00<01:48, 36.01s/it]\rLoading pipeline components...:  71%|#######1  | 5/7 [04:47<01:29, 44.93s/it]\rLoading pipeline components...:  86%|########5 | 6/7 [04:47<00:47, 47.98s/it]\r\nTraceback (most recent call last):\r\n  File \"N:\\3D AI Studio\\utils\\helpers\\generate_image.py\", line 609, in load_model\r\n    self.pipeline = StableDiffusionPipeline.from_pretrained(\r\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\huggingface_hub\\utils\\_validators.py\", line 114, in _inner_fn\r\n    return fn(*args, **kwargs)\r\n           ^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\pipelines\\pipeline_utils.py\", line 1022, in from_pretrained\r\n    loaded_sub_model = load_sub_model(\r\n                       ^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\pipelines\\pipeline_loading_utils.py\", line 833, in load_sub_model\r\n    loaded_sub_model = load_method(cached_folder, **loading_kwargs)\r\n                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\huggingface_hub\\utils\\_validators.py\", line 114, in _inner_fn\r\n    return fn(*args, **kwargs)\r\n           ^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\models\\modeling_utils.py\", line 1014, in from_pretrained\r\n    config, unused_kwargs, commit_hash = cls.load_config(\r\n                                         ^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\huggingface_hub\\utils\\_validators.py\", line 114, in _inner_fn\r\n    return fn(*args, **kwargs)\r\n           ^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\configuration_utils.py\", line 384, in load_config\r\n    raise EnvironmentError(\r\nOSError: Error no file named config.json found in directory N:\\3D AI Studio\\models\\ImageGeneration\\stable-diffusion-v1-5.\r\n\n    at ChildProcess.<anonymous> (N:\\3D AI Studio\\src\\main\\pipelineManager.js:349:36)\n    at ChildProcess.emit (node:events:519:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","timestamp":"2025-07-18 09:11:53"}
error: Error stack: {"0":"E","1":"r","10":"g","100":"i","1000":",","1001":" ","1002":"3","1003":"6","1004":".","1005":"0","1006":"1","1007":"s","1008":"/","1009":"i","101":"l","1010":"t","1011":"]","1012":"\r","1013":"L","1014":"o","1015":"a","1016":"d","1017":"i","1018":"n","1019":"g","102":"l","1020":" ","1021":"p","1022":"i","1023":"p","1024":"e","1025":"l","1026":"i","1027":"n","1028":"e","1029":" ","103":" ","1030":"c","1031":"o","1032":"m","1033":"p","1034":"o","1035":"n","1036":"e","1037":"n","1038":"t","1039":"s","104":"n","1040":".","1041":".","1042":".","1043":":","1044":" ","1045":" ","1046":"7","1047":"1","1048":"%","1049":"|","105":"o","1050":"#","1051":"#","1052":"#","1053":"#","1054":"#","1055":"#","1056":"#","1057":"1","1058":" ","1059":" ","106":"t","1060":"|","1061":" ","1062":"5","1063":"/","1064":"7","1065":" ","1066":"[","1067":"0","1068":"4","1069":":","107":" ","1070":"4","1071":"7","1072":"<","1073":"0","1074":"1","1075":":","1076":"2","1077":"9","1078":",","1079":" ","108":"b","1080":"4","1081":"4","1082":".","1083":"9","1084":"3","1085":"s","1086":"/","1087":"i","1088":"t","1089":"]","109":"e","1090":"\r","1091":"L","1092":"o","1093":"a","1094":"d","1095":"i","1096":"n","1097":"g","1098":" ","1099":"p","11":"e","110":" ","1100":"i","1101":"p","1102":"e","1103":"l","1104":"i","1105":"n","1106":"e","1107":" ","1108":"c","1109":"o","111":"e","1110":"m","1111":"p","1112":"o","1113":"n","1114":"e","1115":"n","1116":"t","1117":"s","1118":".","1119":".","112":"n","1120":".","1121":":","1122":" ","1123":" ","1124":"8","1125":"6","1126":"%","1127":"|","1128":"#","1129":"#","113":"a","1130":"#","1131":"#","1132":"#","1133":"#","1134":"#","1135":"#","1136":"5","1137":" ","1138":"|","1139":" ","114":"b","1140":"6","1141":"/","1142":"7","1143":" ","1144":"[","1145":"0","1146":"4","1147":":","1148":"4","1149":"7","115":"l","1150":"<","1151":"0","1152":"0","1153":":","1154":"4","1155":"7","1156":",","1157":" ","1158":"4","1159":"7","116":"e","1160":".","1161":"9","1162":"8","1163":"s","1164":"/","1165":"i","1166":"t","1167":"]","1168":"\r","1169":"\n","117":"d","1170":"T","1171":"r","1172":"a","1173":"c","1174":"e","1175":"b","1176":"a","1177":"c","1178":"k","1179":" ","118":"\r","1180":"(","1181":"m","1182":"o","1183":"s","1184":"t","1185":" ","1186":"r","1187":"e","1188":"c","1189":"e","119":"\n","1190":"n","1191":"t","1192":" ","1193":"c","1194":"a","1195":"l","1196":"l","1197":" ","1198":"l","1199":"a","12":" ","120":"T","1200":"s","1201":"t","1202":")","1203":":","1204":"\r","1205":"\n","1206":" ","1207":" ","1208":"F","1209":"i","121":"r","1210":"l","1211":"e","1212":" ","1213":"\"","1214":"N","1215":":","1216":"\\","1217":"3","1218":"D","1219":" ","122":"a","1220":"A","1221":"I","1222":" ","1223":"S","1224":"t","1225":"u","1226":"d","1227":"i","1228":"o","1229":"\\","123":"c","1230":"u","1231":"t","1232":"i","1233":"l","1234":"s","1235":"\\","1236":"h","1237":"e","1238":"l","1239":"p","124":"e","1240":"e","1241":"r","1242":"s","1243":"\\","1244":"g","1245":"e","1246":"n","1247":"e","1248":"r","1249":"a","125":"b","1250":"t","1251":"e","1252":"_","1253":"i","1254":"m","1255":"a","1256":"g","1257":"e","1258":".","1259":"p","126":"a","1260":"y","1261":"\"","1262":",","1263":" ","1264":"l","1265":"i","1266":"n","1267":"e","1268":" ","1269":"6","127":"c","1270":"0","1271":"9","1272":",","1273":" ","1274":"i","1275":"n","1276":" ","1277":"l","1278":"o","1279":"a","128":"k","1280":"d","1281":"_","1282":"m","1283":"o","1284":"d","1285":"e","1286":"l","1287":"\r","1288":"\n","1289":" ","129":" ","1290":" ","1291":" ","1292":" ","1293":"s","1294":"e","1295":"l","1296":"f","1297":".","1298":"p","1299":"i","13":"g","130":"(","1300":"p","1301":"e","1302":"l","1303":"i","1304":"n","1305":"e","1306":" ","1307":"=","1308":" ","1309":"S","131":"m","1310":"t","1311":"a","1312":"b","1313":"l","1314":"e","1315":"D","1316":"i","1317":"f","1318":"f","1319":"u","132":"o","1320":"s","1321":"i","1322":"o","1323":"n","1324":"P","1325":"i","1326":"p","1327":"e","1328":"l","1329":"i","133":"s","1330":"n","1331":"e","1332":".","1333":"f","1334":"r","1335":"o","1336":"m","1337":"_","1338":"p","1339":"r","134":"t","1340":"e","1341":"t","1342":"r","1343":"a","1344":"i","1345":"n","1346":"e","1347":"d","1348":"(","1349":"\r","135":" ","1350":"\n","1351":" ","1352":" ","1353":" ","1354":" ","1355":" ","1356":" ","1357":" ","1358":" ","1359":" ","136":"r","1360":" ","1361":" ","1362":" ","1363":" ","1364":" ","1365":" ","1366":" ","1367":" ","1368":" ","1369":" ","137":"e","1370":" ","1371":"^","1372":"^","1373":"^","1374":"^","1375":"^","1376":"^","1377":"^","1378":"^","1379":"^","138":"c","1380":"^","1381":"^","1382":"^","1383":"^","1384":"^","1385":"^","1386":"^","1387":"^","1388":"^","1389":"^","139":"e","1390":"^","1391":"^","1392":"^","1393":"^","1394":"^","1395":"^","1396":"^","1397":"^","1398":"^","1399":"^","14":"e","140":"n","1400":"^","1401":"^","1402":"^","1403":"^","1404":"^","1405":"^","1406":"^","1407":"^","1408":"^","1409":"^","141":"t","1410":"^","1411":"\r","1412":"\n","1413":" ","1414":" ","1415":"F","1416":"i","1417":"l","1418":"e","1419":" ","142":" ","1420":"\"","1421":"N","1422":":","1423":"\\","1424":"3","1425":"D","1426":" ","1427":"A","1428":"I","1429":" ","143":"c","1430":"S","1431":"t","1432":"u","1433":"d","1434":"i","1435":"o","1436":"\\","1437":"p","1438":"i","1439":"p","144":"a","1440":"e","1441":"l","1442":"i","1443":"n","1444":"e","1445":"s","1446":"\\","1447":"I","1448":"m","1449":"a","145":"l","1450":"g","1451":"e","1452":"G","1453":"e","1454":"n","1455":"e","1456":"r","1457":"a","1458":"t","1459":"i","146":"l","1460":"o","1461":"n","1462":"\\","1463":"e","1464":"n","1465":"v","1466":"\\","1467":"L","1468":"i","1469":"b","147":" ","1470":"\\","1471":"s","1472":"i","1473":"t","1474":"e","1475":"-","1476":"p","1477":"a","1478":"c","1479":"k","148":"l","1480":"a","1481":"g","1482":"e","1483":"s","1484":"\\","1485":"h","1486":"u","1487":"g","1488":"g","1489":"i","149":"a","1490":"n","1491":"g","1492":"f","1493":"a","1494":"c","1495":"e","1496":"_","1497":"h","1498":"u","1499":"b","15":"n","150":"s","1500":"\\","1501":"u","1502":"t","1503":"i","1504":"l","1505":"s","1506":"\\","1507":"_","1508":"v","1509":"a","151":"t","1510":"l","1511":"i","1512":"d","1513":"a","1514":"t","1515":"o","1516":"r","1517":"s","1518":".","1519":"p","152":")","1520":"y","1521":"\"","1522":",","1523":" ","1524":"l","1525":"i","1526":"n","1527":"e","1528":" ","1529":"1","153":":","1530":"1","1531":"4","1532":",","1533":" ","1534":"i","1535":"n","1536":" ","1537":"_","1538":"i","1539":"n","154":"\r","1540":"n","1541":"e","1542":"r","1543":"_","1544":"f","1545":"n","1546":"\r","1547":"\n","1548":" ","1549":" ","155":"\n","1550":" ","1551":" ","1552":"r","1553":"e","1554":"t","1555":"u","1556":"r","1557":"n","1558":" ","1559":"f","156":" ","1560":"n","1561":"(","1562":"*","1563":"a","1564":"r","1565":"g","1566":"s","1567":",","1568":" ","1569":"*","157":" ","1570":"*","1571":"k","1572":"w","1573":"a","1574":"r","1575":"g","1576":"s","1577":")","1578":"\r","1579":"\n","158":"F","1580":" ","1581":" ","1582":" ","1583":" ","1584":" ","1585":" ","1586":" ","1587":" ","1588":" ","1589":" ","159":"i","1590":" ","1591":"^","1592":"^","1593":"^","1594":"^","1595":"^","1596":"^","1597":"^","1598":"^","1599":"^","16":"e","160":"l","1600":"^","1601":"^","1602":"^","1603":"^","1604":"^","1605":"^","1606":"^","1607":"^","1608":"^","1609":"^","161":"e","1610":"\r","1611":"\n","1612":" ","1613":" ","1614":"F","1615":"i","1616":"l","1617":"e","1618":" ","1619":"\"","162":" ","1620":"N","1621":":","1622":"\\","1623":"3","1624":"D","1625":" ","1626":"A","1627":"I","1628":" ","1629":"S","163":"\"","1630":"t","1631":"u","1632":"d","1633":"i","1634":"o","1635":"\\","1636":"p","1637":"i","1638":"p","1639":"e","164":"N","1640":"l","1641":"i","1642":"n","1643":"e","1644":"s","1645":"\\","1646":"I","1647":"m","1648":"a","1649":"g","165":":","1650":"e","1651":"G","1652":"e","1653":"n","1654":"e","1655":"r","1656":"a","1657":"t","1658":"i","1659":"o","166":"\\","1660":"n","1661":"\\","1662":"e","1663":"n","1664":"v","1665":"\\","1666":"L","1667":"i","1668":"b","1669":"\\","167":"3","1670":"s","1671":"i","1672":"t","1673":"e","1674":"-","1675":"p","1676":"a","1677":"c","1678":"k","1679":"a","168":"D","1680":"g","1681":"e","1682":"s","1683":"\\","1684":"d","1685":"i","1686":"f","1687":"f","1688":"u","1689":"s","169":" ","1690":"e","1691":"r","1692":"s","1693":"\\","1694":"p","1695":"i","1696":"p","1697":"e","1698":"l","1699":"i","17":"r","170":"A","1700":"n","1701":"e","1702":"s","1703":"\\","1704":"p","1705":"i","1706":"p","1707":"e","1708":"l","1709":"i","171":"I","1710":"n","1711":"e","1712":"_","1713":"u","1714":"t","1715":"i","1716":"l","1717":"s","1718":".","1719":"p","172":" ","1720":"y","1721":"\"","1722":",","1723":" ","1724":"l","1725":"i","1726":"n","1727":"e","1728":" ","1729":"1","173":"S","1730":"0","1731":"2","1732":"2","1733":",","1734":" ","1735":"i","1736":"n","1737":" ","1738":"f","1739":"r","174":"t","1740":"o","1741":"m","1742":"_","1743":"p","1744":"r","1745":"e","1746":"t","1747":"r","1748":"a","1749":"i","175":"u","1750":"n","1751":"e","1752":"d","1753":"\r","1754":"\n","1755":" ","1756":" ","1757":" ","1758":" ","1759":"l","176":"d","1760":"o","1761":"a","1762":"d","1763":"e","1764":"d","1765":"_","1766":"s","1767":"u","1768":"b","1769":"_","177":"i","1770":"m","1771":"o","1772":"d","1773":"e","1774":"l","1775":" ","1776":"=","1777":" ","1778":"l","1779":"o","178":"o","1780":"a","1781":"d","1782":"_","1783":"s","1784":"u","1785":"b","1786":"_","1787":"m","1788":"o","1789":"d","179":"\\","1790":"e","1791":"l","1792":"(","1793":"\r","1794":"\n","1795":" ","1796":" ","1797":" ","1798":" ","1799":" ","18":"a","180":"p","1800":" ","1801":" ","1802":" ","1803":" ","1804":" ","1805":" ","1806":" ","1807":" ","1808":" ","1809":" ","181":"i","1810":" ","1811":" ","1812":" ","1813":" ","1814":" ","1815":" ","1816":" ","1817":" ","1818":"^","1819":"^","182":"p","1820":"^","1821":"^","1822":"^","1823":"^","1824":"^","1825":"^","1826":"^","1827":"^","1828":"^","1829":"^","183":"e","1830":"^","1831":"^","1832":"^","1833":"\r","1834":"\n","1835":" ","1836":" ","1837":"F","1838":"i","1839":"l","184":"l","1840":"e","1841":" ","1842":"\"","1843":"N","1844":":","1845":"\\","1846":"3","1847":"D","1848":" ","1849":"A","185":"i","1850":"I","1851":" ","1852":"S","1853":"t","1854":"u","1855":"d","1856":"i","1857":"o","1858":"\\","1859":"p","186":"n","1860":"i","1861":"p","1862":"e","1863":"l","1864":"i","1865":"n","1866":"e","1867":"s","1868":"\\","1869":"I","187":"e","1870":"m","1871":"a","1872":"g","1873":"e","1874":"G","1875":"e","1876":"n","1877":"e","1878":"r","1879":"a","188":"s","1880":"t","1881":"i","1882":"o","1883":"n","1884":"\\","1885":"e","1886":"n","1887":"v","1888":"\\","1889":"L","189":"\\","1890":"i","1891":"b","1892":"\\","1893":"s","1894":"i","1895":"t","1896":"e","1897":"-","1898":"p","1899":"a","19":"t","190":"I","1900":"c","1901":"k","1902":"a","1903":"g","1904":"e","1905":"s","1906":"\\","1907":"d","1908":"i","1909":"f","191":"m","1910":"f","1911":"u","1912":"s","1913":"e","1914":"r","1915":"s","1916":"\\","1917":"p","1918":"i","1919":"p","192":"a","1920":"e","1921":"l","1922":"i","1923":"n","1924":"e","1925":"s","1926":"\\","1927":"p","1928":"i","1929":"p","193":"g","1930":"e","1931":"l","1932":"i","1933":"n","1934":"e","1935":"_","1936":"l","1937":"o","1938":"a","1939":"d","194":"e","1940":"i","1941":"n","1942":"g","1943":"_","1944":"u","1945":"t","1946":"i","1947":"l","1948":"s","1949":".","195":"G","1950":"p","1951":"y","1952":"\"","1953":",","1954":" ","1955":"l","1956":"i","1957":"n","1958":"e","1959":" ","196":"e","1960":"8","1961":"3","1962":"3","1963":",","1964":" ","1965":"i","1966":"n","1967":" ","1968":"l","1969":"o","197":"n","1970":"a","1971":"d","1972":"_","1973":"s","1974":"u","1975":"b","1976":"_","1977":"m","1978":"o","1979":"d","198":"e","1980":"e","1981":"l","1982":"\r","1983":"\n","1984":" ","1985":" ","1986":" ","1987":" ","1988":"l","1989":"o","199":"r","1990":"a","1991":"d","1992":"e","1993":"d","1994":"_","1995":"s","1996":"u","1997":"b","1998":"_","1999":"m","2":"r","20":"i","200":"a","2000":"o","2001":"d","2002":"e","2003":"l","2004":" ","2005":"=","2006":" ","2007":"l","2008":"o","2009":"a","201":"t","2010":"d","2011":"_","2012":"m","2013":"e","2014":"t","2015":"h","2016":"o","2017":"d","2018":"(","2019":"c","202":"i","2020":"a","2021":"c","2022":"h","2023":"e","2024":"d","2025":"_","2026":"f","2027":"o","2028":"l","2029":"d","203":"o","2030":"e","2031":"r","2032":",","2033":" ","2034":"*","2035":"*","2036":"l","2037":"o","2038":"a","2039":"d","204":"n","2040":"i","2041":"n","2042":"g","2043":"_","2044":"k","2045":"w","2046":"a","2047":"r","2048":"g","2049":"s","205":"\\","2050":")","2051":"\r","2052":"\n","2053":" ","2054":" ","2055":" ","2056":" ","2057":" ","2058":" ","2059":" ","206":"e","2060":" ","2061":" ","2062":" ","2063":" ","2064":" ","2065":" ","2066":" ","2067":" ","2068":" ","2069":" ","207":"n","2070":" ","2071":" ","2072":" ","2073":" ","2074":" ","2075":" ","2076":"^","2077":"^","2078":"^","2079":"^","208":"v","2080":"^","2081":"^","2082":"^","2083":"^","2084":"^","2085":"^","2086":"^","2087":"^","2088":"^","2089":"^","209":"\\","2090":"^","2091":"^","2092":"^","2093":"^","2094":"^","2095":"^","2096":"^","2097":"^","2098":"^","2099":"^","21":"o","210":"L","2100":"^","2101":"^","2102":"^","2103":"^","2104":"^","2105":"^","2106":"^","2107":"^","2108":"^","2109":"^","211":"i","2110":"^","2111":"^","2112":"^","2113":"^","2114":"^","2115":"^","2116":"^","2117":"^","2118":"^","2119":"^","212":"b","2120":"\r","2121":"\n","2122":" ","2123":" ","2124":"F","2125":"i","2126":"l","2127":"e","2128":" ","2129":"\"","213":"\\","2130":"N","2131":":","2132":"\\","2133":"3","2134":"D","2135":" ","2136":"A","2137":"I","2138":" ","2139":"S","214":"s","2140":"t","2141":"u","2142":"d","2143":"i","2144":"o","2145":"\\","2146":"p","2147":"i","2148":"p","2149":"e","215":"i","2150":"l","2151":"i","2152":"n","2153":"e","2154":"s","2155":"\\","2156":"I","2157":"m","2158":"a","2159":"g","216":"t","2160":"e","2161":"G","2162":"e","2163":"n","2164":"e","2165":"r","2166":"a","2167":"t","2168":"i","2169":"o","217":"e","2170":"n","2171":"\\","2172":"e","2173":"n","2174":"v","2175":"\\","2176":"L","2177":"i","2178":"b","2179":"\\","218":"-","2180":"s","2181":"i","2182":"t","2183":"e","2184":"-","2185":"p","2186":"a","2187":"c","2188":"k","2189":"a","219":"p","2190":"g","2191":"e","2192":"s","2193":"\\","2194":"h","2195":"u","2196":"g","2197":"g","2198":"i","2199":"n","22":"n","220":"a","2200":"g","2201":"f","2202":"a","2203":"c","2204":"e","2205":"_","2206":"h","2207":"u","2208":"b","2209":"\\","221":"c","2210":"u","2211":"t","2212":"i","2213":"l","2214":"s","2215":"\\","2216":"_","2217":"v","2218":"a","2219":"l","222":"k","2220":"i","2221":"d","2222":"a","2223":"t","2224":"o","2225":"r","2226":"s","2227":".","2228":"p","2229":"y","223":"a","2230":"\"","2231":",","2232":" ","2233":"l","2234":"i","2235":"n","2236":"e","2237":" ","2238":"1","2239":"1","224":"g","2240":"4","2241":",","2242":" ","2243":"i","2244":"n","2245":" ","2246":"_","2247":"i","2248":"n","2249":"n","225":"e","2250":"e","2251":"r","2252":"_","2253":"f","2254":"n","2255":"\r","2256":"\n","2257":" ","2258":" ","2259":" ","226":"s","2260":" ","2261":"r","2262":"e","2263":"t","2264":"u","2265":"r","2266":"n","2267":" ","2268":"f","2269":"n","227":"\\","2270":"(","2271":"*","2272":"a","2273":"r","2274":"g","2275":"s","2276":",","2277":" ","2278":"*","2279":"*","228":"x","2280":"k","2281":"w","2282":"a","2283":"r","2284":"g","2285":"s","2286":")","2287":"\r","2288":"\n","2289":" ","229":"f","2290":" ","2291":" ","2292":" ","2293":" ","2294":" ","2295":" ","2296":" ","2297":" ","2298":" ","2299":" ","23":" ","230":"o","2300":"^","2301":"^","2302":"^","2303":"^","2304":"^","2305":"^","2306":"^","2307":"^","2308":"^","2309":"^","231":"r","2310":"^","2311":"^","2312":"^","2313":"^","2314":"^","2315":"^","2316":"^","2317":"^","2318":"^","2319":"\r","232":"m","2320":"\n","2321":" ","2322":" ","2323":"F","2324":"i","2325":"l","2326":"e","2327":" ","2328":"\"","2329":"N","233":"e","2330":":","2331":"\\","2332":"3","2333":"D","2334":" ","2335":"A","2336":"I","2337":" ","2338":"S","2339":"t","234":"r","2340":"u","2341":"d","2342":"i","2343":"o","2344":"\\","2345":"p","2346":"i","2347":"p","2348":"e","2349":"l","235":"s","2350":"i","2351":"n","2352":"e","2353":"s","2354":"\\","2355":"I","2356":"m","2357":"a","2358":"g","2359":"e","236":"\\","2360":"G","2361":"e","2362":"n","2363":"e","2364":"r","2365":"a","2366":"t","2367":"i","2368":"o","2369":"n","237":"_","2370":"\\","2371":"e","2372":"n","2373":"v","2374":"\\","2375":"L","2376":"i","2377":"b","2378":"\\","2379":"s","238":"_","2380":"i","2381":"t","2382":"e","2383":"-","2384":"p","2385":"a","2386":"c","2387":"k","2388":"a","2389":"g","239":"i","2390":"e","2391":"s","2392":"\\","2393":"d","2394":"i","2395":"f","2396":"f","2397":"u","2398":"s","2399":"e","24":"f","240":"n","2400":"r","2401":"s","2402":"\\","2403":"m","2404":"o","2405":"d","2406":"e","2407":"l","2408":"s","2409":"\\","241":"i","2410":"m","2411":"o","2412":"d","2413":"e","2414":"l","2415":"i","2416":"n","2417":"g","2418":"_","2419":"u","242":"t","2420":"t","2421":"i","2422":"l","2423":"s","2424":".","2425":"p","2426":"y","2427":"\"","2428":",","2429":" ","243":"_","2430":"l","2431":"i","2432":"n","2433":"e","2434":" ","2435":"1","2436":"0","2437":"1","2438":"4","2439":",","244":"_","2440":" ","2441":"i","2442":"n","2443":" ","2444":"f","2445":"r","2446":"o","2447":"m","2448":"_","2449":"p","245":".","2450":"r","2451":"e","2452":"t","2453":"r","2454":"a","2455":"i","2456":"n","2457":"e","2458":"d","2459":"\r","246":"p","2460":"\n","2461":" ","2462":" ","2463":" ","2464":" ","2465":"c","2466":"o","2467":"n","2468":"f","2469":"i","247":"y","2470":"g","2471":",","2472":" ","2473":"u","2474":"n","2475":"u","2476":"s","2477":"e","2478":"d","2479":"_","248":"\"","2480":"k","2481":"w","2482":"a","2483":"r","2484":"g","2485":"s","2486":",","2487":" ","2488":"c","2489":"o","249":",","2490":"m","2491":"m","2492":"i","2493":"t","2494":"_","2495":"h","2496":"a","2497":"s","2498":"h","2499":" ","25":"a","250":" ","2500":"=","2501":" ","2502":"c","2503":"l","2504":"s","2505":".","2506":"l","2507":"o","2508":"a","2509":"d","251":"l","2510":"_","2511":"c","2512":"o","2513":"n","2514":"f","2515":"i","2516":"g","2517":"(","2518":"\r","2519":"\n","252":"i","2520":" ","2521":" ","2522":" ","2523":" ","2524":" ","2525":" ","2526":" ","2527":" ","2528":" ","2529":" ","253":"n","2530":" ","2531":" ","2532":" ","2533":" ","2534":" ","2535":" ","2536":" ","2537":" ","2538":" ","2539":" ","254":"e","2540":" ","2541":" ","2542":" ","2543":" ","2544":" ","2545":" ","2546":" ","2547":" ","2548":" ","2549":" ","255":" ","2550":" ","2551":" ","2552":" ","2553":" ","2554":" ","2555":" ","2556":" ","2557":" ","2558":" ","2559":" ","256":"5","2560":" ","2561":"^","2562":"^","2563":"^","2564":"^","2565":"^","2566":"^","2567":"^","2568":"^","2569":"^","257":"7","2570":"^","2571":"^","2572":"^","2573":"^","2574":"^","2575":"^","2576":"^","2577":"\r","2578":"\n","2579":" ","258":",","2580":" ","2581":"F","2582":"i","2583":"l","2584":"e","2585":" ","2586":"\"","2587":"N","2588":":","2589":"\\","259":" ","2590":"3","2591":"D","2592":" ","2593":"A","2594":"I","2595":" ","2596":"S","2597":"t","2598":"u","2599":"d","26":"i","260":"i","2600":"i","2601":"o","2602":"\\","2603":"p","2604":"i","2605":"p","2606":"e","2607":"l","2608":"i","2609":"n","261":"n","2610":"e","2611":"s","2612":"\\","2613":"I","2614":"m","2615":"a","2616":"g","2617":"e","2618":"G","2619":"e","262":" ","2620":"n","2621":"e","2622":"r","2623":"a","2624":"t","2625":"i","2626":"o","2627":"n","2628":"\\","2629":"e","263":"_","2630":"n","2631":"v","2632":"\\","2633":"L","2634":"i","2635":"b","2636":"\\","2637":"s","2638":"i","2639":"t","264":"i","2640":"e","2641":"-","2642":"p","2643":"a","2644":"c","2645":"k","2646":"a","2647":"g","2648":"e","2649":"s","265":"s","2650":"\\","2651":"h","2652":"u","2653":"g","2654":"g","2655":"i","2656":"n","2657":"g","2658":"f","2659":"a","266":"_","2660":"c","2661":"e","2662":"_","2663":"h","2664":"u","2665":"b","2666":"\\","2667":"u","2668":"t","2669":"i","267":"t","2670":"l","2671":"s","2672":"\\","2673":"_","2674":"v","2675":"a","2676":"l","2677":"i","2678":"d","2679":"a","268":"r","2680":"t","2681":"o","2682":"r","2683":"s","2684":".","2685":"p","2686":"y","2687":"\"","2688":",","2689":" ","269":"i","2690":"l","2691":"i","2692":"n","2693":"e","2694":" ","2695":"1","2696":"1","2697":"4","2698":",","2699":" ","27":"l","270":"t","2700":"i","2701":"n","2702":" ","2703":"_","2704":"i","2705":"n","2706":"n","2707":"e","2708":"r","2709":"_","271":"o","2710":"f","2711":"n","2712":"\r","2713":"\n","2714":" ","2715":" ","2716":" ","2717":" ","2718":"r","2719":"e","272":"n","2720":"t","2721":"u","2722":"r","2723":"n","2724":" ","2725":"f","2726":"n","2727":"(","2728":"*","2729":"a","273":"_","2730":"r","2731":"g","2732":"s","2733":",","2734":" ","2735":"*","2736":"*","2737":"k","2738":"w","2739":"a","274":"a","2740":"r","2741":"g","2742":"s","2743":")","2744":"\r","2745":"\n","2746":" ","2747":" ","2748":" ","2749":" ","275":"v","2750":" ","2751":" ","2752":" ","2753":" ","2754":" ","2755":" ","2756":" ","2757":"^","2758":"^","2759":"^","276":"a","2760":"^","2761":"^","2762":"^","2763":"^","2764":"^","2765":"^","2766":"^","2767":"^","2768":"^","2769":"^","277":"i","2770":"^","2771":"^","2772":"^","2773":"^","2774":"^","2775":"^","2776":"\r","2777":"\n","2778":" ","2779":" ","278":"l","2780":"F","2781":"i","2782":"l","2783":"e","2784":" ","2785":"\"","2786":"N","2787":":","2788":"\\","2789":"3","279":"a","2790":"D","2791":" ","2792":"A","2793":"I","2794":" ","2795":"S","2796":"t","2797":"u","2798":"d","2799":"i","28":"e","280":"b","2800":"o","2801":"\\","2802":"p","2803":"i","2804":"p","2805":"e","2806":"l","2807":"i","2808":"n","2809":"e","281":"l","2810":"s","2811":"\\","2812":"I","2813":"m","2814":"a","2815":"g","2816":"e","2817":"G","2818":"e","2819":"n","282":"e","2820":"e","2821":"r","2822":"a","2823":"t","2824":"i","2825":"o","2826":"n","2827":"\\","2828":"e","2829":"n","283":"\r","2830":"v","2831":"\\","2832":"L","2833":"i","2834":"b","2835":"\\","2836":"s","2837":"i","2838":"t","2839":"e","284":"\n","2840":"-","2841":"p","2842":"a","2843":"c","2844":"k","2845":"a","2846":"g","2847":"e","2848":"s","2849":"\\","285":" ","2850":"d","2851":"i","2852":"f","2853":"f","2854":"u","2855":"s","2856":"e","2857":"r","2858":"s","2859":"\\","286":" ","2860":"c","2861":"o","2862":"n","2863":"f","2864":"i","2865":"g","2866":"u","2867":"r","2868":"a","2869":"t","287":" ","2870":"i","2871":"o","2872":"n","2873":"_","2874":"u","2875":"t","2876":"i","2877":"l","2878":"s","2879":".","288":" ","2880":"p","2881":"y","2882":"\"","2883":",","2884":" ","2885":"l","2886":"i","2887":"n","2888":"e","2889":" ","289":"i","2890":"3","2891":"8","2892":"4","2893":",","2894":" ","2895":"i","2896":"n","2897":" ","2898":"l","2899":"o","29":"d","290":"m","2900":"a","2901":"d","2902":"_","2903":"c","2904":"o","2905":"n","2906":"f","2907":"i","2908":"g","2909":"\r","291":"p","2910":"\n","2911":" ","2912":" ","2913":" ","2914":" ","2915":"r","2916":"a","2917":"i","2918":"s","2919":"e","292":"o","2920":" ","2921":"E","2922":"n","2923":"v","2924":"i","2925":"r","2926":"o","2927":"n","2928":"m","2929":"e","293":"r","2930":"n","2931":"t","2932":"E","2933":"r","2934":"r","2935":"o","2936":"r","2937":"(","2938":"\r","2939":"\n","294":"t","2940":"O","2941":"S","2942":"E","2943":"r","2944":"r","2945":"o","2946":"r","2947":":","2948":" ","2949":"E","295":" ","2950":"r","2951":"r","2952":"o","2953":"r","2954":" ","2955":"n","2956":"o","2957":" ","2958":"f","2959":"i","296":"t","2960":"l","2961":"e","2962":" ","2963":"n","2964":"a","2965":"m","2966":"e","2967":"d","2968":" ","2969":"c","297":"r","2970":"o","2971":"n","2972":"f","2973":"i","2974":"g","2975":".","2976":"j","2977":"s","2978":"o","2979":"n","298":"i","2980":" ","2981":"f","2982":"o","2983":"u","2984":"n","2985":"d","2986":" ","2987":"i","2988":"n","2989":" ","299":"t","2990":"d","2991":"i","2992":"r","2993":"e","2994":"c","2995":"t","2996":"o","2997":"r","2998":"y","2999":" ","3":"o","30":" ","300":"o","3000":"N","3001":":","3002":"\\","3003":"3","3004":"D","3005":" ","3006":"A","3007":"I","3008":" ","3009":"S","301":"n","3010":"t","3011":"u","3012":"d","3013":"i","3014":"o","3015":"\\","3016":"m","3017":"o","3018":"d","3019":"e","302":" ","3020":"l","3021":"s","3022":"\\","3023":"I","3024":"m","3025":"a","3026":"g","3027":"e","3028":"G","3029":"e","303":" ","3030":"n","3031":"e","3032":"r","3033":"a","3034":"t","3035":"i","3036":"o","3037":"n","3038":"\\","3039":"s","304":"#","3040":"t","3041":"a","3042":"b","3043":"l","3044":"e","3045":"-","3046":"d","3047":"i","3048":"f","3049":"f","305":" ","3050":"u","3051":"s","3052":"i","3053":"o","3054":"n","3055":"-","3056":"v","3057":"1","3058":"-","3059":"5","306":"n","3060":".","3061":"\r","3062":"\n","3063":"\n","3064":" ","3065":" ","3066":" ","3067":" ","3068":"a","3069":"t","307":"o","3070":" ","3071":"C","3072":"h","3073":"i","3074":"l","3075":"d","3076":"P","3077":"r","3078":"o","3079":"c","308":"q","3080":"e","3081":"s","3082":"s","3083":".","3084":"<","3085":"a","3086":"n","3087":"o","3088":"n","3089":"y","309":"a","3090":"m","3091":"o","3092":"u","3093":"s","3094":">","3095":" ","3096":"(","3097":"N","3098":":","3099":"\\","31":"w","310":"\r","3100":"3","3101":"D","3102":" ","3103":"A","3104":"I","3105":" ","3106":"S","3107":"t","3108":"u","3109":"d","311":"\n","3110":"i","3111":"o","3112":"\\","3113":"s","3114":"r","3115":"c","3116":"\\","3117":"m","3118":"a","3119":"i","312":" ","3120":"n","3121":"\\","3122":"p","3123":"i","3124":"p","3125":"e","3126":"l","3127":"i","3128":"n","3129":"e","313":" ","3130":"M","3131":"a","3132":"n","3133":"a","3134":"g","3135":"e","3136":"r","3137":".","3138":"j","3139":"s","314":" ","3140":":","3141":"3","3142":"4","3143":"9","3144":":","3145":"3","3146":"6","3147":")","3148":"\n","3149":" ","315":" ","3150":" ","3151":" ","3152":" ","3153":"a","3154":"t","3155":" ","3156":"C","3157":"h","3158":"i","3159":"l","316":"^","3160":"d","3161":"P","3162":"r","3163":"o","3164":"c","3165":"e","3166":"s","3167":"s","3168":".","3169":"e","317":"^","3170":"m","3171":"i","3172":"t","3173":" ","3174":"(","3175":"n","3176":"o","3177":"d","3178":"e","3179":":","318":"^","3180":"e","3181":"v","3182":"e","3183":"n","3184":"t","3185":"s","3186":":","3187":"5","3188":"1","3189":"9","319":"^","3190":":","3191":"2","3192":"8","3193":")","3194":"\n","3195":" ","3196":" ","3197":" ","3198":" ","3199":"a","32":"i","320":"^","3200":"t","3201":" ","3202":"m","3203":"a","3204":"y","3205":"b","3206":"e","3207":"C","3208":"l","3209":"o","321":"^","3210":"s","3211":"e","3212":" ","3213":"(","3214":"n","3215":"o","3216":"d","3217":"e","3218":":","3219":"i","322":"^","3220":"n","3221":"t","3222":"e","3223":"r","3224":"n","3225":"a","3226":"l","3227":"/","3228":"c","3229":"h","323":"^","3230":"i","3231":"l","3232":"d","3233":"_","3234":"p","3235":"r","3236":"o","3237":"c","3238":"e","3239":"s","324":"^","3240":"s","3241":":","3242":"1","3243":"1","3244":"0","3245":"5","3246":":","3247":"1","3248":"6","3249":")","325":"^","3250":"\n","3251":" ","3252":" ","3253":" ","3254":" ","3255":"a","3256":"t","3257":" ","3258":"C","3259":"h","326":"^","3260":"i","3261":"l","3262":"d","3263":"P","3264":"r","3265":"o","3266":"c","3267":"e","3268":"s","3269":"s","327":"^","3270":".","3271":"_","3272":"h","3273":"a","3274":"n","3275":"d","3276":"l","3277":"e","3278":".","3279":"o","328":"^","3280":"n","3281":"e","3282":"x","3283":"i","3284":"t","3285":" ","3286":"(","3287":"n","3288":"o","3289":"d","329":"\r","3290":"e","3291":":","3292":"i","3293":"n","3294":"t","3295":"e","3296":"r","3297":"n","3298":"a","3299":"l","33":"t","330":"\n","3300":"/","3301":"c","3302":"h","3303":"i","3304":"l","3305":"d","3306":"_","3307":"p","3308":"r","3309":"o","331":"M","3310":"c","3311":"e","3312":"s","3313":"s","3314":":","3315":"3","3316":"0","3317":"5","3318":":","3319":"5","332":"o","3320":")","333":"d","334":"u","335":"l","336":"e","337":"N","338":"o","339":"t","34":"h","340":"F","341":"o","342":"u","343":"n","344":"d","345":"E","346":"r","347":"r","348":"o","349":"r","35":" ","350":":","351":" ","352":"N","353":"o","354":" ","355":"m","356":"o","357":"d","358":"u","359":"l","36":"c","360":"e","361":" ","362":"n","363":"a","364":"m","365":"e","366":"d","367":" ","368":"'","369":"t","37":"o","370":"r","371":"i","372":"t","373":"o","374":"n","375":"'","376":"\r","377":"\n","378":"\r","379":"L","38":"d","380":"o","381":"a","382":"d","383":"i","384":"n","385":"g","386":" ","387":"p","388":"i","389":"p","39":"e","390":"e","391":"l","392":"i","393":"n","394":"e","395":" ","396":"c","397":"o","398":"m","399":"p","4":"r","40":" ","400":"o","401":"n","402":"e","403":"n","404":"t","405":"s","406":".","407":".","408":".","409":":","41":"1","410":" ","411":" ","412":" ","413":"0","414":"%","415":"|","416":" ","417":" ","418":" ","419":" ","42":":","420":" ","421":" ","422":" ","423":" ","424":" ","425":" ","426":"|","427":" ","428":"0","429":"/","43":" ","430":"7","431":" ","432":"[","433":"0","434":"0","435":":","436":"0","437":"0","438":"<","439":"?","44":"A","440":",","441":" ","442":"?","443":"i","444":"t","445":"/","446":"s","447":"]","448":"A","449":"n","45":" ","450":" ","451":"e","452":"r","453":"r","454":"o","455":"r","456":" ","457":"o","458":"c","459":"c","46":"m","460":"u","461":"r","462":"r","463":"e","464":"d","465":" ","466":"w","467":"h","468":"i","469":"l","47":"a","470":"e","471":" ","472":"t","473":"r","474":"y","475":"i","476":"n","477":"g","478":" ","479":"t","48":"t","480":"o","481":" ","482":"f","483":"e","484":"t","485":"c","486":"h","487":" ","488":"N","489":":","49":"c","490":"\\","491":"3","492":"D","493":" ","494":"A","495":"I","496":" ","497":"S","498":"t","499":"u","5":":","50":"h","500":"d","501":"i","502":"o","503":"\\","504":"m","505":"o","506":"d","507":"e","508":"l","509":"s","51":"i","510":"\\","511":"I","512":"m","513":"a","514":"g","515":"e","516":"G","517":"e","518":"n","519":"e","52":"n","520":"r","521":"a","522":"t","523":"i","524":"o","525":"n","526":"\\","527":"s","528":"t","529":"a","53":"g","530":"b","531":"l","532":"e","533":"-","534":"d","535":"i","536":"f","537":"f","538":"u","539":"s","54":" ","540":"i","541":"o","542":"n","543":"-","544":"v","545":"1","546":"-","547":"5","548":"\\","549":"u","55":"T","550":"n","551":"e","552":"t","553":":","554":" ","555":"E","556":"r","557":"r","558":"o","559":"r","56":"r","560":" ","561":"n","562":"o","563":" ","564":"f","565":"i","566":"l","567":"e","568":" ","569":"n","57":"i","570":"a","571":"m","572":"e","573":"d","574":" ","575":"d","576":"i","577":"f","578":"f","579":"u","58":"t","580":"s","581":"i","582":"o","583":"n","584":"_","585":"p","586":"y","587":"t","588":"o","589":"r","59":"o","590":"c","591":"h","592":"_","593":"m","594":"o","595":"d","596":"e","597":"l","598":".","599":"s","6":" ","60":"n","600":"a","601":"f","602":"e","603":"t","604":"e","605":"n","606":"s","607":"o","608":"r","609":"s","61":" ","610":" ","611":"f","612":"o","613":"u","614":"n","615":"d","616":" ","617":"i","618":"n","619":" ","62":"i","620":"d","621":"i","622":"r","623":"e","624":"c","625":"t","626":"o","627":"r","628":"y","629":" ","63":"s","630":"N","631":":","632":"\\","633":"3","634":"D","635":" ","636":"A","637":"I","638":" ","639":"S","64":" ","640":"t","641":"u","642":"d","643":"i","644":"o","645":"\\","646":"m","647":"o","648":"d","649":"e","65":"n","650":"l","651":"s","652":"\\","653":"I","654":"m","655":"a","656":"g","657":"e","658":"G","659":"e","66":"o","660":"n","661":"e","662":"r","663":"a","664":"t","665":"i","666":"o","667":"n","668":"\\","669":"s","67":"t","670":"t","671":"a","672":"b","673":"l","674":"e","675":"-","676":"d","677":"i","678":"f","679":"f","68":" ","680":"u","681":"s","682":"i","683":"o","684":"n","685":"-","686":"v","687":"1","688":"-","689":"5","69":"a","690":"\\","691":"u","692":"n","693":"e","694":"t","695":".","696":"\r","697":"\n","698":"\r","699":"L","7":"I","70":"v","700":"o","701":"a","702":"d","703":"i","704":"n","705":"g","706":" ","707":"p","708":"i","709":"p","71":"a","710":"e","711":"l","712":"i","713":"n","714":"e","715":" ","716":"c","717":"o","718":"m","719":"p","72":"i","720":"o","721":"n","722":"e","723":"n","724":"t","725":"s","726":".","727":".","728":".","729":":","73":"l","730":" ","731":" ","732":"1","733":"4","734":"%","735":"|","736":"#","737":"4","738":" ","739":" ","74":"a","740":" ","741":" ","742":" ","743":" ","744":" ","745":" ","746":"|","747":" ","748":"1","749":"/","75":"b","750":"7","751":" ","752":"[","753":"0","754":"3","755":":","756":"1","757":"6","758":"<","759":"1","76":"l","760":"9","761":":","762":"3","763":"8","764":",","765":" ","766":"1","767":"9","768":"6","769":".","77":"e","770":"4","771":"8","772":"s","773":"/","774":"i","775":"t","776":"]","777":"\r","778":"L","779":"o","78":",","780":"a","781":"d","782":"i","783":"n","784":"g","785":" ","786":"p","787":"i","788":"p","789":"e","79":" ","790":"l","791":"i","792":"n","793":"e","794":" ","795":"c","796":"o","797":"m","798":"p","799":"o","8":"m","80":"s","800":"n","801":"e","802":"n","803":"t","804":"s","805":".","806":".","807":".","808":":","809":" ","81":"o","810":" ","811":"2","812":"9","813":"%","814":"|","815":"#","816":"#","817":"8","818":" ","819":" ","82":"m","820":" ","821":" ","822":" ","823":" ","824":" ","825":"|","826":" ","827":"2","828":"/","829":"7","83":"e","830":" ","831":"[","832":"0","833":"3","834":":","835":"4","836":"2","837":"<","838":"0","839":"8","84":" ","840":":","841":"0","842":"2","843":",","844":" ","845":"9","846":"6","847":".","848":"4","849":"0","85":"o","850":"s","851":"/","852":"i","853":"t","854":"]","855":" ","856":"\r","857":"L","858":"o","859":"a","86":"p","860":"d","861":"i","862":"n","863":"g","864":" ","865":"p","866":"i","867":"p","868":"e","869":"l","87":"t","870":"i","871":"n","872":"e","873":" ","874":"c","875":"o","876":"m","877":"p","878":"o","879":"n","88":"i","880":"e","881":"n","882":"t","883":"s","884":".","885":".","886":".","887":":","888":" ","889":" ","89":"m","890":"5","891":"7","892":"%","893":"|","894":"#","895":"#","896":"#","897":"#","898":"#","899":"7","9":"a","90":"i","900":" ","901":" ","902":" ","903":" ","904":"|","905":" ","906":"4","907":"/","908":"7","909":" ","91":"z","910":"[","911":"0","912":"3","913":":","914":"4","915":"2","916":"<","917":"0","918":"1","919":":","92":"a","920":"4","921":"8","922":",","923":" ","924":"3","925":"6","926":".","927":"0","928":"1","929":"s","93":"t","930":"/","931":"i","932":"t","933":"]","934":"\r","935":"L","936":"o","937":"a","938":"d","939":"i","94":"i","940":"n","941":"g","942":" ","943":"p","944":"i","945":"p","946":"e","947":"l","948":"i","949":"n","95":"o","950":"e","951":" ","952":"c","953":"o","954":"m","955":"p","956":"o","957":"n","958":"e","959":"n","96":"n","960":"t","961":"s","962":".","963":".","964":".","965":":","966":" ","967":" ","968":"5","969":"7","97":"s","970":"%","971":"|","972":"#","973":"#","974":"#","975":"#","976":"#","977":"7","978":" ","979":" ","98":" ","980":" ","981":" ","982":"|","983":" ","984":"4","985":"/","986":"7","987":" ","988":"[","989":"0","99":"w","990":"4","991":":","992":"0","993":"0","994":"<","995":"0","996":"1","997":":","998":"4","999":"8","service":"user-service","timestamp":"2025-07-18 09:11:53"}
info: Starting streaming image generation with model: stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 09:12:40"}
info: Starting image generation with command: N:\3D AI Studio\pipelines\ImageGeneration\env\Scripts\python.exe utils\helpers\generate_image.py --prompt a house in the rocky cliffside --output N:\3D AI Studio\output\generated_1752847960362_9ce01b62-aa7f-45d8-abae-f9bc4d92f462.png --model stable-diffusion-2-1 --width 512 --height 512 --guidance_scale 7.5 --seed 1018453686 --refiner_steps 10 --preview_quality high --result_file pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-18 09:12:40"}
info: ImageGeneration stderr: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton' {"service":"user-service","timestamp":"2025-07-18 09:12:56"}
info: ImageGeneration stdout: ImageGenerator initialized: {"service":"user-service","timestamp":"2025-07-18 09:12:56"}
info: ImageGeneration stdout: Device: cuda {"service":"user-service","timestamp":"2025-07-18 09:12:56"}
info: ImageGeneration stdout: Torch dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:12:56"}
info: ImageGeneration stdout: App root: N:\3D AI Studio {"service":"user-service","timestamp":"2025-07-18 09:12:56"}
info: ImageGeneration stdout: Models path: N:\3D AI Studio\models\ImageGeneration {"service":"user-service","timestamp":"2025-07-18 09:12:56"}
info: ImageGeneration stdout: Preview quality: high {"service":"user-service","timestamp":"2025-07-18 09:12:56"}
info: ImageGeneration stdout: Initializing stable-diffusion-2-1 model... {"service":"user-service","timestamp":"2025-07-18 09:12:56"}
info: ImageGeneration stdout: Loading model: stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 09:12:56"}
info: ImageGeneration stderr: Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-18 09:12:56"}
info: ImageGeneration stderr: Loading pipeline components...:  17%|#6        | 1/6 [01:16<06:22, 76.52s/it] {"service":"user-service","timestamp":"2025-07-18 09:14:13"}
info: ImageGeneration stderr: Loading pipeline components...:  33%|###3      | 2/6 [04:36<09:57, 149.43s/it] {"service":"user-service","timestamp":"2025-07-18 09:17:33"}
info: ImageGeneration stderr: Loading pipeline components...:  50%|#####     | 3/6 [04:37<04:37, 92.34s/it] {"service":"user-service","timestamp":"2025-07-18 09:17:33"}
info: ImageGeneration stdout: Error loading model stable-diffusion-2-1: Error no file named config.json found in directory N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1. {"service":"user-service","timestamp":"2025-07-18 09:17:33"}
info: ImageGeneration stderr: Traceback (most recent call last):
  File "N:\3D AI Studio\utils\helpers\generate_image.py", line 624, in load_model
    self.pipeline = StableDiffusionPipeline.from_pretrained(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 1022, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 833, in load_sub_model
    loaded_sub_model = load_method(cached_folder, **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\modeling_utils.py", line 1014, in from_pretrained
    config, unused_kwargs, commit_hash = cls.load_config(
                                         ^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\configuration_utils.py", line 384, in load_config
    raise EnvironmentError(
OSError: Error no file named config.json found in directory N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1. {"service":"user-service","timestamp":"2025-07-18 09:17:33"}
info: ImageGeneration stdout: {"success": false, "error": "Failed to load model stable-diffusion-2-1", "execution_time": 277.02721428871155} {"service":"user-service","timestamp":"2025-07-18 09:17:33"}
info: Image generation process exited with code: 1 {"service":"user-service","timestamp":"2025-07-18 09:17:35"}
error: Image generation failed with code 1: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton'
Loading pipeline components...:  50%|#####     | 3/6 [04:37<04:37, 92.34s/it]
Traceback (most recent call last):
  File "N:\3D AI Studio\utils\helpers\generate_image.py", line 624, in load_model
    self.pipeline = StableDiffusionPipeline.from_pretrained(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 1022, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 833, in load_sub_model
    loaded_sub_model = load_method(cached_folder, **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\modeling_utils.py", line 1014, in from_pretrained
    config, unused_kwargs, commit_hash = cls.load_config(
                                         ^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\configuration_utils.py", line 384, in load_config
    raise EnvironmentError(
OSError: Error no file named config.json found in directory N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1.
 {"service":"user-service","timestamp":"2025-07-18 09:17:35"}
error: Failed to generate image (streaming): Image generation failed with code 1: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton'
Loading pipeline components...:  50%|#####     | 3/6 [04:37<04:37, 92.34s/it]
Traceback (most recent call last):
  File "N:\3D AI Studio\utils\helpers\generate_image.py", line 624, in load_model
    self.pipeline = StableDiffusionPipeline.from_pretrained(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_utils.py", line 1022, in from_pretrained
    loaded_sub_model = load_sub_model(
                       ^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\pipeline_loading_utils.py", line 833, in load_sub_model
    loaded_sub_model = load_method(cached_folder, **loading_kwargs)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\modeling_utils.py", line 1014, in from_pretrained
    config, unused_kwargs, commit_hash = cls.load_config(
                                         ^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\utils\_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\configuration_utils.py", line 384, in load_config
    raise EnvironmentError(
OSError: Error no file named config.json found in directory N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1.
 {"service":"user-service","stack":"Error: Image generation failed with code 1: A matching Triton is not available, some optimizations will not be enabled\r\nTraceback (most recent call last):\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\xformers\\__init__.py\", line 57, in _is_triton_available\r\n    import triton  # noqa\r\n    ^^^^^^^^^^^^^\r\nModuleNotFoundError: No module named 'triton'\r\n\rLoading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]\rLoading pipeline components...:  17%|#6        | 1/6 [01:16<06:22, 76.52s/it]\rLoading pipeline components...:  33%|###3      | 2/6 [04:36<09:57, 149.43s/it]\rLoading pipeline components...:  50%|#####     | 3/6 [04:37<04:37, 92.34s/it] \r\nTraceback (most recent call last):\r\n  File \"N:\\3D AI Studio\\utils\\helpers\\generate_image.py\", line 624, in load_model\r\n    self.pipeline = StableDiffusionPipeline.from_pretrained(\r\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\huggingface_hub\\utils\\_validators.py\", line 114, in _inner_fn\r\n    return fn(*args, **kwargs)\r\n           ^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\pipelines\\pipeline_utils.py\", line 1022, in from_pretrained\r\n    loaded_sub_model = load_sub_model(\r\n                       ^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\pipelines\\pipeline_loading_utils.py\", line 833, in load_sub_model\r\n    loaded_sub_model = load_method(cached_folder, **loading_kwargs)\r\n                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\huggingface_hub\\utils\\_validators.py\", line 114, in _inner_fn\r\n    return fn(*args, **kwargs)\r\n           ^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\models\\modeling_utils.py\", line 1014, in from_pretrained\r\n    config, unused_kwargs, commit_hash = cls.load_config(\r\n                                         ^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\huggingface_hub\\utils\\_validators.py\", line 114, in _inner_fn\r\n    return fn(*args, **kwargs)\r\n           ^^^^^^^^^^^^^^^^^^^\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\diffusers\\configuration_utils.py\", line 384, in load_config\r\n    raise EnvironmentError(\r\nOSError: Error no file named config.json found in directory N:\\3D AI Studio\\models\\ImageGeneration\\stable-diffusion-2-1.\r\n\n    at ChildProcess.<anonymous> (N:\\3D AI Studio\\src\\main\\pipelineManager.js:349:36)\n    at ChildProcess.emit (node:events:519:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","timestamp":"2025-07-18 09:17:35"}
error: Error stack: {"0":"E","1":"r","10":"g","100":"i","1000":"\\","1001":"h","1002":"u","1003":"g","1004":"g","1005":"i","1006":"n","1007":"g","1008":"f","1009":"a","101":"l","1010":"c","1011":"e","1012":"_","1013":"h","1014":"u","1015":"b","1016":"\\","1017":"u","1018":"t","1019":"i","102":"l","1020":"l","1021":"s","1022":"\\","1023":"_","1024":"v","1025":"a","1026":"l","1027":"i","1028":"d","1029":"a","103":" ","1030":"t","1031":"o","1032":"r","1033":"s","1034":".","1035":"p","1036":"y","1037":"\"","1038":",","1039":" ","104":"n","1040":"l","1041":"i","1042":"n","1043":"e","1044":" ","1045":"1","1046":"1","1047":"4","1048":",","1049":" ","105":"o","1050":"i","1051":"n","1052":" ","1053":"_","1054":"i","1055":"n","1056":"n","1057":"e","1058":"r","1059":"_","106":"t","1060":"f","1061":"n","1062":"\r","1063":"\n","1064":" ","1065":" ","1066":" ","1067":" ","1068":"r","1069":"e","107":" ","1070":"t","1071":"u","1072":"r","1073":"n","1074":" ","1075":"f","1076":"n","1077":"(","1078":"*","1079":"a","108":"b","1080":"r","1081":"g","1082":"s","1083":",","1084":" ","1085":"*","1086":"*","1087":"k","1088":"w","1089":"a","109":"e","1090":"r","1091":"g","1092":"s","1093":")","1094":"\r","1095":"\n","1096":" ","1097":" ","1098":" ","1099":" ","11":"e","110":" ","1100":" ","1101":" ","1102":" ","1103":" ","1104":" ","1105":" ","1106":" ","1107":"^","1108":"^","1109":"^","111":"e","1110":"^","1111":"^","1112":"^","1113":"^","1114":"^","1115":"^","1116":"^","1117":"^","1118":"^","1119":"^","112":"n","1120":"^","1121":"^","1122":"^","1123":"^","1124":"^","1125":"^","1126":"\r","1127":"\n","1128":" ","1129":" ","113":"a","1130":"F","1131":"i","1132":"l","1133":"e","1134":" ","1135":"\"","1136":"N","1137":":","1138":"\\","1139":"3","114":"b","1140":"D","1141":" ","1142":"A","1143":"I","1144":" ","1145":"S","1146":"t","1147":"u","1148":"d","1149":"i","115":"l","1150":"o","1151":"\\","1152":"p","1153":"i","1154":"p","1155":"e","1156":"l","1157":"i","1158":"n","1159":"e","116":"e","1160":"s","1161":"\\","1162":"I","1163":"m","1164":"a","1165":"g","1166":"e","1167":"G","1168":"e","1169":"n","117":"d","1170":"e","1171":"r","1172":"a","1173":"t","1174":"i","1175":"o","1176":"n","1177":"\\","1178":"e","1179":"n","118":"\r","1180":"v","1181":"\\","1182":"L","1183":"i","1184":"b","1185":"\\","1186":"s","1187":"i","1188":"t","1189":"e","119":"\n","1190":"-","1191":"p","1192":"a","1193":"c","1194":"k","1195":"a","1196":"g","1197":"e","1198":"s","1199":"\\","12":" ","120":"T","1200":"d","1201":"i","1202":"f","1203":"f","1204":"u","1205":"s","1206":"e","1207":"r","1208":"s","1209":"\\","121":"r","1210":"p","1211":"i","1212":"p","1213":"e","1214":"l","1215":"i","1216":"n","1217":"e","1218":"s","1219":"\\","122":"a","1220":"p","1221":"i","1222":"p","1223":"e","1224":"l","1225":"i","1226":"n","1227":"e","1228":"_","1229":"u","123":"c","1230":"t","1231":"i","1232":"l","1233":"s","1234":".","1235":"p","1236":"y","1237":"\"","1238":",","1239":" ","124":"e","1240":"l","1241":"i","1242":"n","1243":"e","1244":" ","1245":"1","1246":"0","1247":"2","1248":"2","1249":",","125":"b","1250":" ","1251":"i","1252":"n","1253":" ","1254":"f","1255":"r","1256":"o","1257":"m","1258":"_","1259":"p","126":"a","1260":"r","1261":"e","1262":"t","1263":"r","1264":"a","1265":"i","1266":"n","1267":"e","1268":"d","1269":"\r","127":"c","1270":"\n","1271":" ","1272":" ","1273":" ","1274":" ","1275":"l","1276":"o","1277":"a","1278":"d","1279":"e","128":"k","1280":"d","1281":"_","1282":"s","1283":"u","1284":"b","1285":"_","1286":"m","1287":"o","1288":"d","1289":"e","129":" ","1290":"l","1291":" ","1292":"=","1293":" ","1294":"l","1295":"o","1296":"a","1297":"d","1298":"_","1299":"s","13":"g","130":"(","1300":"u","1301":"b","1302":"_","1303":"m","1304":"o","1305":"d","1306":"e","1307":"l","1308":"(","1309":"\r","131":"m","1310":"\n","1311":" ","1312":" ","1313":" ","1314":" ","1315":" ","1316":" ","1317":" ","1318":" ","1319":" ","132":"o","1320":" ","1321":" ","1322":" ","1323":" ","1324":" ","1325":" ","1326":" ","1327":" ","1328":" ","1329":" ","133":"s","1330":" ","1331":" ","1332":" ","1333":" ","1334":"^","1335":"^","1336":"^","1337":"^","1338":"^","1339":"^","134":"t","1340":"^","1341":"^","1342":"^","1343":"^","1344":"^","1345":"^","1346":"^","1347":"^","1348":"^","1349":"\r","135":" ","1350":"\n","1351":" ","1352":" ","1353":"F","1354":"i","1355":"l","1356":"e","1357":" ","1358":"\"","1359":"N","136":"r","1360":":","1361":"\\","1362":"3","1363":"D","1364":" ","1365":"A","1366":"I","1367":" ","1368":"S","1369":"t","137":"e","1370":"u","1371":"d","1372":"i","1373":"o","1374":"\\","1375":"p","1376":"i","1377":"p","1378":"e","1379":"l","138":"c","1380":"i","1381":"n","1382":"e","1383":"s","1384":"\\","1385":"I","1386":"m","1387":"a","1388":"g","1389":"e","139":"e","1390":"G","1391":"e","1392":"n","1393":"e","1394":"r","1395":"a","1396":"t","1397":"i","1398":"o","1399":"n","14":"e","140":"n","1400":"\\","1401":"e","1402":"n","1403":"v","1404":"\\","1405":"L","1406":"i","1407":"b","1408":"\\","1409":"s","141":"t","1410":"i","1411":"t","1412":"e","1413":"-","1414":"p","1415":"a","1416":"c","1417":"k","1418":"a","1419":"g","142":" ","1420":"e","1421":"s","1422":"\\","1423":"d","1424":"i","1425":"f","1426":"f","1427":"u","1428":"s","1429":"e","143":"c","1430":"r","1431":"s","1432":"\\","1433":"p","1434":"i","1435":"p","1436":"e","1437":"l","1438":"i","1439":"n","144":"a","1440":"e","1441":"s","1442":"\\","1443":"p","1444":"i","1445":"p","1446":"e","1447":"l","1448":"i","1449":"n","145":"l","1450":"e","1451":"_","1452":"l","1453":"o","1454":"a","1455":"d","1456":"i","1457":"n","1458":"g","1459":"_","146":"l","1460":"u","1461":"t","1462":"i","1463":"l","1464":"s","1465":".","1466":"p","1467":"y","1468":"\"","1469":",","147":" ","1470":" ","1471":"l","1472":"i","1473":"n","1474":"e","1475":" ","1476":"8","1477":"3","1478":"3","1479":",","148":"l","1480":" ","1481":"i","1482":"n","1483":" ","1484":"l","1485":"o","1486":"a","1487":"d","1488":"_","1489":"s","149":"a","1490":"u","1491":"b","1492":"_","1493":"m","1494":"o","1495":"d","1496":"e","1497":"l","1498":"\r","1499":"\n","15":"n","150":"s","1500":" ","1501":" ","1502":" ","1503":" ","1504":"l","1505":"o","1506":"a","1507":"d","1508":"e","1509":"d","151":"t","1510":"_","1511":"s","1512":"u","1513":"b","1514":"_","1515":"m","1516":"o","1517":"d","1518":"e","1519":"l","152":")","1520":" ","1521":"=","1522":" ","1523":"l","1524":"o","1525":"a","1526":"d","1527":"_","1528":"m","1529":"e","153":":","1530":"t","1531":"h","1532":"o","1533":"d","1534":"(","1535":"c","1536":"a","1537":"c","1538":"h","1539":"e","154":"\r","1540":"d","1541":"_","1542":"f","1543":"o","1544":"l","1545":"d","1546":"e","1547":"r","1548":",","1549":" ","155":"\n","1550":"*","1551":"*","1552":"l","1553":"o","1554":"a","1555":"d","1556":"i","1557":"n","1558":"g","1559":"_","156":" ","1560":"k","1561":"w","1562":"a","1563":"r","1564":"g","1565":"s","1566":")","1567":"\r","1568":"\n","1569":" ","157":" ","1570":" ","1571":" ","1572":" ","1573":" ","1574":" ","1575":" ","1576":" ","1577":" ","1578":" ","1579":" ","158":"F","1580":" ","1581":" ","1582":" ","1583":" ","1584":" ","1585":" ","1586":" ","1587":" ","1588":" ","1589":" ","159":"i","1590":" ","1591":" ","1592":"^","1593":"^","1594":"^","1595":"^","1596":"^","1597":"^","1598":"^","1599":"^","16":"e","160":"l","1600":"^","1601":"^","1602":"^","1603":"^","1604":"^","1605":"^","1606":"^","1607":"^","1608":"^","1609":"^","161":"e","1610":"^","1611":"^","1612":"^","1613":"^","1614":"^","1615":"^","1616":"^","1617":"^","1618":"^","1619":"^","162":" ","1620":"^","1621":"^","1622":"^","1623":"^","1624":"^","1625":"^","1626":"^","1627":"^","1628":"^","1629":"^","163":"\"","1630":"^","1631":"^","1632":"^","1633":"^","1634":"^","1635":"^","1636":"\r","1637":"\n","1638":" ","1639":" ","164":"N","1640":"F","1641":"i","1642":"l","1643":"e","1644":" ","1645":"\"","1646":"N","1647":":","1648":"\\","1649":"3","165":":","1650":"D","1651":" ","1652":"A","1653":"I","1654":" ","1655":"S","1656":"t","1657":"u","1658":"d","1659":"i","166":"\\","1660":"o","1661":"\\","1662":"p","1663":"i","1664":"p","1665":"e","1666":"l","1667":"i","1668":"n","1669":"e","167":"3","1670":"s","1671":"\\","1672":"I","1673":"m","1674":"a","1675":"g","1676":"e","1677":"G","1678":"e","1679":"n","168":"D","1680":"e","1681":"r","1682":"a","1683":"t","1684":"i","1685":"o","1686":"n","1687":"\\","1688":"e","1689":"n","169":" ","1690":"v","1691":"\\","1692":"L","1693":"i","1694":"b","1695":"\\","1696":"s","1697":"i","1698":"t","1699":"e","17":"r","170":"A","1700":"-","1701":"p","1702":"a","1703":"c","1704":"k","1705":"a","1706":"g","1707":"e","1708":"s","1709":"\\","171":"I","1710":"h","1711":"u","1712":"g","1713":"g","1714":"i","1715":"n","1716":"g","1717":"f","1718":"a","1719":"c","172":" ","1720":"e","1721":"_","1722":"h","1723":"u","1724":"b","1725":"\\","1726":"u","1727":"t","1728":"i","1729":"l","173":"S","1730":"s","1731":"\\","1732":"_","1733":"v","1734":"a","1735":"l","1736":"i","1737":"d","1738":"a","1739":"t","174":"t","1740":"o","1741":"r","1742":"s","1743":".","1744":"p","1745":"y","1746":"\"","1747":",","1748":" ","1749":"l","175":"u","1750":"i","1751":"n","1752":"e","1753":" ","1754":"1","1755":"1","1756":"4","1757":",","1758":" ","1759":"i","176":"d","1760":"n","1761":" ","1762":"_","1763":"i","1764":"n","1765":"n","1766":"e","1767":"r","1768":"_","1769":"f","177":"i","1770":"n","1771":"\r","1772":"\n","1773":" ","1774":" ","1775":" ","1776":" ","1777":"r","1778":"e","1779":"t","178":"o","1780":"u","1781":"r","1782":"n","1783":" ","1784":"f","1785":"n","1786":"(","1787":"*","1788":"a","1789":"r","179":"\\","1790":"g","1791":"s","1792":",","1793":" ","1794":"*","1795":"*","1796":"k","1797":"w","1798":"a","1799":"r","18":"a","180":"p","1800":"g","1801":"s","1802":")","1803":"\r","1804":"\n","1805":" ","1806":" ","1807":" ","1808":" ","1809":" ","181":"i","1810":" ","1811":" ","1812":" ","1813":" ","1814":" ","1815":" ","1816":"^","1817":"^","1818":"^","1819":"^","182":"p","1820":"^","1821":"^","1822":"^","1823":"^","1824":"^","1825":"^","1826":"^","1827":"^","1828":"^","1829":"^","183":"e","1830":"^","1831":"^","1832":"^","1833":"^","1834":"^","1835":"\r","1836":"\n","1837":" ","1838":" ","1839":"F","184":"l","1840":"i","1841":"l","1842":"e","1843":" ","1844":"\"","1845":"N","1846":":","1847":"\\","1848":"3","1849":"D","185":"i","1850":" ","1851":"A","1852":"I","1853":" ","1854":"S","1855":"t","1856":"u","1857":"d","1858":"i","1859":"o","186":"n","1860":"\\","1861":"p","1862":"i","1863":"p","1864":"e","1865":"l","1866":"i","1867":"n","1868":"e","1869":"s","187":"e","1870":"\\","1871":"I","1872":"m","1873":"a","1874":"g","1875":"e","1876":"G","1877":"e","1878":"n","1879":"e","188":"s","1880":"r","1881":"a","1882":"t","1883":"i","1884":"o","1885":"n","1886":"\\","1887":"e","1888":"n","1889":"v","189":"\\","1890":"\\","1891":"L","1892":"i","1893":"b","1894":"\\","1895":"s","1896":"i","1897":"t","1898":"e","1899":"-","19":"t","190":"I","1900":"p","1901":"a","1902":"c","1903":"k","1904":"a","1905":"g","1906":"e","1907":"s","1908":"\\","1909":"d","191":"m","1910":"i","1911":"f","1912":"f","1913":"u","1914":"s","1915":"e","1916":"r","1917":"s","1918":"\\","1919":"m","192":"a","1920":"o","1921":"d","1922":"e","1923":"l","1924":"s","1925":"\\","1926":"m","1927":"o","1928":"d","1929":"e","193":"g","1930":"l","1931":"i","1932":"n","1933":"g","1934":"_","1935":"u","1936":"t","1937":"i","1938":"l","1939":"s","194":"e","1940":".","1941":"p","1942":"y","1943":"\"","1944":",","1945":" ","1946":"l","1947":"i","1948":"n","1949":"e","195":"G","1950":" ","1951":"1","1952":"0","1953":"1","1954":"4","1955":",","1956":" ","1957":"i","1958":"n","1959":" ","196":"e","1960":"f","1961":"r","1962":"o","1963":"m","1964":"_","1965":"p","1966":"r","1967":"e","1968":"t","1969":"r","197":"n","1970":"a","1971":"i","1972":"n","1973":"e","1974":"d","1975":"\r","1976":"\n","1977":" ","1978":" ","1979":" ","198":"e","1980":" ","1981":"c","1982":"o","1983":"n","1984":"f","1985":"i","1986":"g","1987":",","1988":" ","1989":"u","199":"r","1990":"n","1991":"u","1992":"s","1993":"e","1994":"d","1995":"_","1996":"k","1997":"w","1998":"a","1999":"r","2":"r","20":"i","200":"a","2000":"g","2001":"s","2002":",","2003":" ","2004":"c","2005":"o","2006":"m","2007":"m","2008":"i","2009":"t","201":"t","2010":"_","2011":"h","2012":"a","2013":"s","2014":"h","2015":" ","2016":"=","2017":" ","2018":"c","2019":"l","202":"i","2020":"s","2021":".","2022":"l","2023":"o","2024":"a","2025":"d","2026":"_","2027":"c","2028":"o","2029":"n","203":"o","2030":"f","2031":"i","2032":"g","2033":"(","2034":"\r","2035":"\n","2036":" ","2037":" ","2038":" ","2039":" ","204":"n","2040":" ","2041":" ","2042":" ","2043":" ","2044":" ","2045":" ","2046":" ","2047":" ","2048":" ","2049":" ","205":"\\","2050":" ","2051":" ","2052":" ","2053":" ","2054":" ","2055":" ","2056":" ","2057":" ","2058":" ","2059":" ","206":"e","2060":" ","2061":" ","2062":" ","2063":" ","2064":" ","2065":" ","2066":" ","2067":" ","2068":" ","2069":" ","207":"n","2070":" ","2071":" ","2072":" ","2073":" ","2074":" ","2075":" ","2076":" ","2077":"^","2078":"^","2079":"^","208":"v","2080":"^","2081":"^","2082":"^","2083":"^","2084":"^","2085":"^","2086":"^","2087":"^","2088":"^","2089":"^","209":"\\","2090":"^","2091":"^","2092":"^","2093":"\r","2094":"\n","2095":" ","2096":" ","2097":"F","2098":"i","2099":"l","21":"o","210":"L","2100":"e","2101":" ","2102":"\"","2103":"N","2104":":","2105":"\\","2106":"3","2107":"D","2108":" ","2109":"A","211":"i","2110":"I","2111":" ","2112":"S","2113":"t","2114":"u","2115":"d","2116":"i","2117":"o","2118":"\\","2119":"p","212":"b","2120":"i","2121":"p","2122":"e","2123":"l","2124":"i","2125":"n","2126":"e","2127":"s","2128":"\\","2129":"I","213":"\\","2130":"m","2131":"a","2132":"g","2133":"e","2134":"G","2135":"e","2136":"n","2137":"e","2138":"r","2139":"a","214":"s","2140":"t","2141":"i","2142":"o","2143":"n","2144":"\\","2145":"e","2146":"n","2147":"v","2148":"\\","2149":"L","215":"i","2150":"i","2151":"b","2152":"\\","2153":"s","2154":"i","2155":"t","2156":"e","2157":"-","2158":"p","2159":"a","216":"t","2160":"c","2161":"k","2162":"a","2163":"g","2164":"e","2165":"s","2166":"\\","2167":"h","2168":"u","2169":"g","217":"e","2170":"g","2171":"i","2172":"n","2173":"g","2174":"f","2175":"a","2176":"c","2177":"e","2178":"_","2179":"h","218":"-","2180":"u","2181":"b","2182":"\\","2183":"u","2184":"t","2185":"i","2186":"l","2187":"s","2188":"\\","2189":"_","219":"p","2190":"v","2191":"a","2192":"l","2193":"i","2194":"d","2195":"a","2196":"t","2197":"o","2198":"r","2199":"s","22":"n","220":"a","2200":".","2201":"p","2202":"y","2203":"\"","2204":",","2205":" ","2206":"l","2207":"i","2208":"n","2209":"e","221":"c","2210":" ","2211":"1","2212":"1","2213":"4","2214":",","2215":" ","2216":"i","2217":"n","2218":" ","2219":"_","222":"k","2220":"i","2221":"n","2222":"n","2223":"e","2224":"r","2225":"_","2226":"f","2227":"n","2228":"\r","2229":"\n","223":"a","2230":" ","2231":" ","2232":" ","2233":" ","2234":"r","2235":"e","2236":"t","2237":"u","2238":"r","2239":"n","224":"g","2240":" ","2241":"f","2242":"n","2243":"(","2244":"*","2245":"a","2246":"r","2247":"g","2248":"s","2249":",","225":"e","2250":" ","2251":"*","2252":"*","2253":"k","2254":"w","2255":"a","2256":"r","2257":"g","2258":"s","2259":")","226":"s","2260":"\r","2261":"\n","2262":" ","2263":" ","2264":" ","2265":" ","2266":" ","2267":" ","2268":" ","2269":" ","227":"\\","2270":" ","2271":" ","2272":" ","2273":"^","2274":"^","2275":"^","2276":"^","2277":"^","2278":"^","2279":"^","228":"x","2280":"^","2281":"^","2282":"^","2283":"^","2284":"^","2285":"^","2286":"^","2287":"^","2288":"^","2289":"^","229":"f","2290":"^","2291":"^","2292":"\r","2293":"\n","2294":" ","2295":" ","2296":"F","2297":"i","2298":"l","2299":"e","23":" ","230":"o","2300":" ","2301":"\"","2302":"N","2303":":","2304":"\\","2305":"3","2306":"D","2307":" ","2308":"A","2309":"I","231":"r","2310":" ","2311":"S","2312":"t","2313":"u","2314":"d","2315":"i","2316":"o","2317":"\\","2318":"p","2319":"i","232":"m","2320":"p","2321":"e","2322":"l","2323":"i","2324":"n","2325":"e","2326":"s","2327":"\\","2328":"I","2329":"m","233":"e","2330":"a","2331":"g","2332":"e","2333":"G","2334":"e","2335":"n","2336":"e","2337":"r","2338":"a","2339":"t","234":"r","2340":"i","2341":"o","2342":"n","2343":"\\","2344":"e","2345":"n","2346":"v","2347":"\\","2348":"L","2349":"i","235":"s","2350":"b","2351":"\\","2352":"s","2353":"i","2354":"t","2355":"e","2356":"-","2357":"p","2358":"a","2359":"c","236":"\\","2360":"k","2361":"a","2362":"g","2363":"e","2364":"s","2365":"\\","2366":"d","2367":"i","2368":"f","2369":"f","237":"_","2370":"u","2371":"s","2372":"e","2373":"r","2374":"s","2375":"\\","2376":"c","2377":"o","2378":"n","2379":"f","238":"_","2380":"i","2381":"g","2382":"u","2383":"r","2384":"a","2385":"t","2386":"i","2387":"o","2388":"n","2389":"_","239":"i","2390":"u","2391":"t","2392":"i","2393":"l","2394":"s","2395":".","2396":"p","2397":"y","2398":"\"","2399":",","24":"f","240":"n","2400":" ","2401":"l","2402":"i","2403":"n","2404":"e","2405":" ","2406":"3","2407":"8","2408":"4","2409":",","241":"i","2410":" ","2411":"i","2412":"n","2413":" ","2414":"l","2415":"o","2416":"a","2417":"d","2418":"_","2419":"c","242":"t","2420":"o","2421":"n","2422":"f","2423":"i","2424":"g","2425":"\r","2426":"\n","2427":" ","2428":" ","2429":" ","243":"_","2430":" ","2431":"r","2432":"a","2433":"i","2434":"s","2435":"e","2436":" ","2437":"E","2438":"n","2439":"v","244":"_","2440":"i","2441":"r","2442":"o","2443":"n","2444":"m","2445":"e","2446":"n","2447":"t","2448":"E","2449":"r","245":".","2450":"r","2451":"o","2452":"r","2453":"(","2454":"\r","2455":"\n","2456":"O","2457":"S","2458":"E","2459":"r","246":"p","2460":"r","2461":"o","2462":"r","2463":":","2464":" ","2465":"E","2466":"r","2467":"r","2468":"o","2469":"r","247":"y","2470":" ","2471":"n","2472":"o","2473":" ","2474":"f","2475":"i","2476":"l","2477":"e","2478":" ","2479":"n","248":"\"","2480":"a","2481":"m","2482":"e","2483":"d","2484":" ","2485":"c","2486":"o","2487":"n","2488":"f","2489":"i","249":",","2490":"g","2491":".","2492":"j","2493":"s","2494":"o","2495":"n","2496":" ","2497":"f","2498":"o","2499":"u","25":"a","250":" ","2500":"n","2501":"d","2502":" ","2503":"i","2504":"n","2505":" ","2506":"d","2507":"i","2508":"r","2509":"e","251":"l","2510":"c","2511":"t","2512":"o","2513":"r","2514":"y","2515":" ","2516":"N","2517":":","2518":"\\","2519":"3","252":"i","2520":"D","2521":" ","2522":"A","2523":"I","2524":" ","2525":"S","2526":"t","2527":"u","2528":"d","2529":"i","253":"n","2530":"o","2531":"\\","2532":"m","2533":"o","2534":"d","2535":"e","2536":"l","2537":"s","2538":"\\","2539":"I","254":"e","2540":"m","2541":"a","2542":"g","2543":"e","2544":"G","2545":"e","2546":"n","2547":"e","2548":"r","2549":"a","255":" ","2550":"t","2551":"i","2552":"o","2553":"n","2554":"\\","2555":"s","2556":"t","2557":"a","2558":"b","2559":"l","256":"5","2560":"e","2561":"-","2562":"d","2563":"i","2564":"f","2565":"f","2566":"u","2567":"s","2568":"i","2569":"o","257":"7","2570":"n","2571":"-","2572":"2","2573":"-","2574":"1","2575":".","2576":"\r","2577":"\n","2578":"\n","2579":" ","258":",","2580":" ","2581":" ","2582":" ","2583":"a","2584":"t","2585":" ","2586":"C","2587":"h","2588":"i","2589":"l","259":" ","2590":"d","2591":"P","2592":"r","2593":"o","2594":"c","2595":"e","2596":"s","2597":"s","2598":".","2599":"<","26":"i","260":"i","2600":"a","2601":"n","2602":"o","2603":"n","2604":"y","2605":"m","2606":"o","2607":"u","2608":"s","2609":">","261":"n","2610":" ","2611":"(","2612":"N","2613":":","2614":"\\","2615":"3","2616":"D","2617":" ","2618":"A","2619":"I","262":" ","2620":" ","2621":"S","2622":"t","2623":"u","2624":"d","2625":"i","2626":"o","2627":"\\","2628":"s","2629":"r","263":"_","2630":"c","2631":"\\","2632":"m","2633":"a","2634":"i","2635":"n","2636":"\\","2637":"p","2638":"i","2639":"p","264":"i","2640":"e","2641":"l","2642":"i","2643":"n","2644":"e","2645":"M","2646":"a","2647":"n","2648":"a","2649":"g","265":"s","2650":"e","2651":"r","2652":".","2653":"j","2654":"s","2655":":","2656":"3","2657":"4","2658":"9","2659":":","266":"_","2660":"3","2661":"6","2662":")","2663":"\n","2664":" ","2665":" ","2666":" ","2667":" ","2668":"a","2669":"t","267":"t","2670":" ","2671":"C","2672":"h","2673":"i","2674":"l","2675":"d","2676":"P","2677":"r","2678":"o","2679":"c","268":"r","2680":"e","2681":"s","2682":"s","2683":".","2684":"e","2685":"m","2686":"i","2687":"t","2688":" ","2689":"(","269":"i","2690":"n","2691":"o","2692":"d","2693":"e","2694":":","2695":"e","2696":"v","2697":"e","2698":"n","2699":"t","27":"l","270":"t","2700":"s","2701":":","2702":"5","2703":"1","2704":"9","2705":":","2706":"2","2707":"8","2708":")","2709":"\n","271":"o","2710":" ","2711":" ","2712":" ","2713":" ","2714":"a","2715":"t","2716":" ","2717":"m","2718":"a","2719":"y","272":"n","2720":"b","2721":"e","2722":"C","2723":"l","2724":"o","2725":"s","2726":"e","2727":" ","2728":"(","2729":"n","273":"_","2730":"o","2731":"d","2732":"e","2733":":","2734":"i","2735":"n","2736":"t","2737":"e","2738":"r","2739":"n","274":"a","2740":"a","2741":"l","2742":"/","2743":"c","2744":"h","2745":"i","2746":"l","2747":"d","2748":"_","2749":"p","275":"v","2750":"r","2751":"o","2752":"c","2753":"e","2754":"s","2755":"s","2756":":","2757":"1","2758":"1","2759":"0","276":"a","2760":"5","2761":":","2762":"1","2763":"6","2764":")","2765":"\n","2766":" ","2767":" ","2768":" ","2769":" ","277":"i","2770":"a","2771":"t","2772":" ","2773":"C","2774":"h","2775":"i","2776":"l","2777":"d","2778":"P","2779":"r","278":"l","2780":"o","2781":"c","2782":"e","2783":"s","2784":"s","2785":".","2786":"_","2787":"h","2788":"a","2789":"n","279":"a","2790":"d","2791":"l","2792":"e","2793":".","2794":"o","2795":"n","2796":"e","2797":"x","2798":"i","2799":"t","28":"e","280":"b","2800":" ","2801":"(","2802":"n","2803":"o","2804":"d","2805":"e","2806":":","2807":"i","2808":"n","2809":"t","281":"l","2810":"e","2811":"r","2812":"n","2813":"a","2814":"l","2815":"/","2816":"c","2817":"h","2818":"i","2819":"l","282":"e","2820":"d","2821":"_","2822":"p","2823":"r","2824":"o","2825":"c","2826":"e","2827":"s","2828":"s","2829":":","283":"\r","2830":"3","2831":"0","2832":"5","2833":":","2834":"5","2835":")","284":"\n","285":" ","286":" ","287":" ","288":" ","289":"i","29":"d","290":"m","291":"p","292":"o","293":"r","294":"t","295":" ","296":"t","297":"r","298":"i","299":"t","3":"o","30":" ","300":"o","301":"n","302":" ","303":" ","304":"#","305":" ","306":"n","307":"o","308":"q","309":"a","31":"w","310":"\r","311":"\n","312":" ","313":" ","314":" ","315":" ","316":"^","317":"^","318":"^","319":"^","32":"i","320":"^","321":"^","322":"^","323":"^","324":"^","325":"^","326":"^","327":"^","328":"^","329":"\r","33":"t","330":"\n","331":"M","332":"o","333":"d","334":"u","335":"l","336":"e","337":"N","338":"o","339":"t","34":"h","340":"F","341":"o","342":"u","343":"n","344":"d","345":"E","346":"r","347":"r","348":"o","349":"r","35":" ","350":":","351":" ","352":"N","353":"o","354":" ","355":"m","356":"o","357":"d","358":"u","359":"l","36":"c","360":"e","361":" ","362":"n","363":"a","364":"m","365":"e","366":"d","367":" ","368":"'","369":"t","37":"o","370":"r","371":"i","372":"t","373":"o","374":"n","375":"'","376":"\r","377":"\n","378":"\r","379":"L","38":"d","380":"o","381":"a","382":"d","383":"i","384":"n","385":"g","386":" ","387":"p","388":"i","389":"p","39":"e","390":"e","391":"l","392":"i","393":"n","394":"e","395":" ","396":"c","397":"o","398":"m","399":"p","4":"r","40":" ","400":"o","401":"n","402":"e","403":"n","404":"t","405":"s","406":".","407":".","408":".","409":":","41":"1","410":" ","411":" ","412":" ","413":"0","414":"%","415":"|","416":" ","417":" ","418":" ","419":" ","42":":","420":" ","421":" ","422":" ","423":" ","424":" ","425":" ","426":"|","427":" ","428":"0","429":"/","43":" ","430":"6","431":" ","432":"[","433":"0","434":"0","435":":","436":"0","437":"0","438":"<","439":"?","44":"A","440":",","441":" ","442":"?","443":"i","444":"t","445":"/","446":"s","447":"]","448":"\r","449":"L","45":" ","450":"o","451":"a","452":"d","453":"i","454":"n","455":"g","456":" ","457":"p","458":"i","459":"p","46":"m","460":"e","461":"l","462":"i","463":"n","464":"e","465":" ","466":"c","467":"o","468":"m","469":"p","47":"a","470":"o","471":"n","472":"e","473":"n","474":"t","475":"s","476":".","477":".","478":".","479":":","48":"t","480":" ","481":" ","482":"1","483":"7","484":"%","485":"|","486":"#","487":"6","488":" ","489":" ","49":"c","490":" ","491":" ","492":" ","493":" ","494":" ","495":" ","496":"|","497":" ","498":"1","499":"/","5":":","50":"h","500":"6","501":" ","502":"[","503":"0","504":"1","505":":","506":"1","507":"6","508":"<","509":"0","51":"i","510":"6","511":":","512":"2","513":"2","514":",","515":" ","516":"7","517":"6","518":".","519":"5","52":"n","520":"2","521":"s","522":"/","523":"i","524":"t","525":"]","526":"\r","527":"L","528":"o","529":"a","53":"g","530":"d","531":"i","532":"n","533":"g","534":" ","535":"p","536":"i","537":"p","538":"e","539":"l","54":" ","540":"i","541":"n","542":"e","543":" ","544":"c","545":"o","546":"m","547":"p","548":"o","549":"n","55":"T","550":"e","551":"n","552":"t","553":"s","554":".","555":".","556":".","557":":","558":" ","559":" ","56":"r","560":"3","561":"3","562":"%","563":"|","564":"#","565":"#","566":"#","567":"3","568":" ","569":" ","57":"i","570":" ","571":" ","572":" ","573":" ","574":"|","575":" ","576":"2","577":"/","578":"6","579":" ","58":"t","580":"[","581":"0","582":"4","583":":","584":"3","585":"6","586":"<","587":"0","588":"9","589":":","59":"o","590":"5","591":"7","592":",","593":" ","594":"1","595":"4","596":"9","597":".","598":"4","599":"3","6":" ","60":"n","600":"s","601":"/","602":"i","603":"t","604":"]","605":"\r","606":"L","607":"o","608":"a","609":"d","61":" ","610":"i","611":"n","612":"g","613":" ","614":"p","615":"i","616":"p","617":"e","618":"l","619":"i","62":"i","620":"n","621":"e","622":" ","623":"c","624":"o","625":"m","626":"p","627":"o","628":"n","629":"e","63":"s","630":"n","631":"t","632":"s","633":".","634":".","635":".","636":":","637":" ","638":" ","639":"5","64":" ","640":"0","641":"%","642":"|","643":"#","644":"#","645":"#","646":"#","647":"#","648":" ","649":" ","65":"n","650":" ","651":" ","652":" ","653":"|","654":" ","655":"3","656":"/","657":"6","658":" ","659":"[","66":"o","660":"0","661":"4","662":":","663":"3","664":"7","665":"<","666":"0","667":"4","668":":","669":"3","67":"t","670":"7","671":",","672":" ","673":"9","674":"2","675":".","676":"3","677":"4","678":"s","679":"/","68":" ","680":"i","681":"t","682":"]","683":" ","684":"\r","685":"\n","686":"T","687":"r","688":"a","689":"c","69":"a","690":"e","691":"b","692":"a","693":"c","694":"k","695":" ","696":"(","697":"m","698":"o","699":"s","7":"I","70":"v","700":"t","701":" ","702":"r","703":"e","704":"c","705":"e","706":"n","707":"t","708":" ","709":"c","71":"a","710":"a","711":"l","712":"l","713":" ","714":"l","715":"a","716":"s","717":"t","718":")","719":":","72":"i","720":"\r","721":"\n","722":" ","723":" ","724":"F","725":"i","726":"l","727":"e","728":" ","729":"\"","73":"l","730":"N","731":":","732":"\\","733":"3","734":"D","735":" ","736":"A","737":"I","738":" ","739":"S","74":"a","740":"t","741":"u","742":"d","743":"i","744":"o","745":"\\","746":"u","747":"t","748":"i","749":"l","75":"b","750":"s","751":"\\","752":"h","753":"e","754":"l","755":"p","756":"e","757":"r","758":"s","759":"\\","76":"l","760":"g","761":"e","762":"n","763":"e","764":"r","765":"a","766":"t","767":"e","768":"_","769":"i","77":"e","770":"m","771":"a","772":"g","773":"e","774":".","775":"p","776":"y","777":"\"","778":",","779":" ","78":",","780":"l","781":"i","782":"n","783":"e","784":" ","785":"6","786":"2","787":"4","788":",","789":" ","79":" ","790":"i","791":"n","792":" ","793":"l","794":"o","795":"a","796":"d","797":"_","798":"m","799":"o","8":"m","80":"s","800":"d","801":"e","802":"l","803":"\r","804":"\n","805":" ","806":" ","807":" ","808":" ","809":"s","81":"o","810":"e","811":"l","812":"f","813":".","814":"p","815":"i","816":"p","817":"e","818":"l","819":"i","82":"m","820":"n","821":"e","822":" ","823":"=","824":" ","825":"S","826":"t","827":"a","828":"b","829":"l","83":"e","830":"e","831":"D","832":"i","833":"f","834":"f","835":"u","836":"s","837":"i","838":"o","839":"n","84":" ","840":"P","841":"i","842":"p","843":"e","844":"l","845":"i","846":"n","847":"e","848":".","849":"f","85":"o","850":"r","851":"o","852":"m","853":"_","854":"p","855":"r","856":"e","857":"t","858":"r","859":"a","86":"p","860":"i","861":"n","862":"e","863":"d","864":"(","865":"\r","866":"\n","867":" ","868":" ","869":" ","87":"t","870":" ","871":" ","872":" ","873":" ","874":" ","875":" ","876":" ","877":" ","878":" ","879":" ","88":"i","880":" ","881":" ","882":" ","883":" ","884":" ","885":" ","886":" ","887":"^","888":"^","889":"^","89":"m","890":"^","891":"^","892":"^","893":"^","894":"^","895":"^","896":"^","897":"^","898":"^","899":"^","9":"a","90":"i","900":"^","901":"^","902":"^","903":"^","904":"^","905":"^","906":"^","907":"^","908":"^","909":"^","91":"z","910":"^","911":"^","912":"^","913":"^","914":"^","915":"^","916":"^","917":"^","918":"^","919":"^","92":"a","920":"^","921":"^","922":"^","923":"^","924":"^","925":"^","926":"^","927":"\r","928":"\n","929":" ","93":"t","930":" ","931":"F","932":"i","933":"l","934":"e","935":" ","936":"\"","937":"N","938":":","939":"\\","94":"i","940":"3","941":"D","942":" ","943":"A","944":"I","945":" ","946":"S","947":"t","948":"u","949":"d","95":"o","950":"i","951":"o","952":"\\","953":"p","954":"i","955":"p","956":"e","957":"l","958":"i","959":"n","96":"n","960":"e","961":"s","962":"\\","963":"I","964":"m","965":"a","966":"g","967":"e","968":"G","969":"e","97":"s","970":"n","971":"e","972":"r","973":"a","974":"t","975":"i","976":"o","977":"n","978":"\\","979":"e","98":" ","980":"n","981":"v","982":"\\","983":"L","984":"i","985":"b","986":"\\","987":"s","988":"i","989":"t","99":"w","990":"e","991":"-","992":"p","993":"a","994":"c","995":"k","996":"a","997":"g","998":"e","999":"s","service":"user-service","timestamp":"2025-07-18 09:17:35"}
info: Starting streaming image generation with model: flux-dev {"service":"user-service","timestamp":"2025-07-18 09:19:53"}
info: Starting image generation with command: N:\3D AI Studio\pipelines\ImageGeneration\env\Scripts\python.exe utils\helpers\generate_image.py --prompt a house in the rocky cliffside --output N:\3D AI Studio\output\generated_1752848393705_83649ba1-309a-4bc5-b483-50d17d33070a.png --model flux-dev --width 1024 --height 1024 --guidance_scale 3.5 --seed 1508350847 --refiner_steps 10 --preview_quality high --result_file pipelines\ImageGeneration\temp_result.json {"service":"user-service","timestamp":"2025-07-18 09:19:53"}
info: ImageGeneration stderr: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton' {"service":"user-service","timestamp":"2025-07-18 09:20:10"}
info: ImageGeneration stdout: ImageGenerator initialized: {"service":"user-service","timestamp":"2025-07-18 09:20:10"}
info: ImageGeneration stdout: Device: cuda {"service":"user-service","timestamp":"2025-07-18 09:20:10"}
info: ImageGeneration stdout: Torch dtype: torch.float16 {"service":"user-service","timestamp":"2025-07-18 09:20:10"}
info: ImageGeneration stdout: App root: N:\3D AI Studio {"service":"user-service","timestamp":"2025-07-18 09:20:10"}
info: ImageGeneration stdout: Models path: N:\3D AI Studio\models\ImageGeneration {"service":"user-service","timestamp":"2025-07-18 09:20:10"}
info: ImageGeneration stdout: Preview quality: high {"service":"user-service","timestamp":"2025-07-18 09:20:10"}
info: ImageGeneration stdout: Initializing flux-dev model... {"service":"user-service","timestamp":"2025-07-18 09:20:10"}
info: ImageGeneration stdout: Loading model: flux-dev {"service":"user-service","timestamp":"2025-07-18 09:20:10"}
info: ImageGeneration stdout: Loading regular FLUX Dev pipeline... {"service":"user-service","timestamp":"2025-07-18 09:20:10"}
info: ImageGeneration stderr: Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-18 09:20:10"}
info: ImageGeneration stderr: Loading pipeline components...:  29%|##8       | 2/7 [00:18<00:4 {"servic {"service":"user-service","timestamp":"2025-07-18 09:29:01"}28"}
info: ImageGeneration stderr: Loading checkpoint shards: 100%|##########| 4/4 [08:32<00:00, 128.07s/it] {"service":"user-service","timestamp":"2025-07-18 09:29:01"}
info: ImageGeneration stderr: Loading pipeline components...:  43%|####2     | 3/7 [08:50<14:3 {"servic {"service":"user-service","timestamp":"2025-07-18 09:40:33"}:01"}
info: ImageGeneration stderr:  {"service":"user-service","timestamp":"2025-07-18 09:46:16"} 344.60s/it] {"service":"user-service","timestamp":"2025-07-18 09:46:16"}
info: Image generation process exited with code: 3221225477 {"service":"user-service","timestamp":"2025-07-18 09:46:27"}
error: Image generation failed with code 3221225477: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton'
Loading checkpoint shards: 100%|##########| 4/4 [08:32<00:00, 128.07s/it]/it]
Loading pipeline components...:  43%|####2     | 3/7 [08:50<14:35, 218.84 {"service":"user-service","timestamp":"2025-07-18 09:46:27"}
error: Failed to generate image (streaming): Image generation failed with code 3221225477: A matching Triton is not available, some optimizations will not be enabled
Traceback (most recent call last):
  File "N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
    import triton  # noqa
    ^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'triton'
Loading checkpoint shards: 100%|##########| 4/4 [08:32<00:00, 128.07s/it]/it]
Loading pipeline components...:  43%|####2     | 3/7 [08:50<14:35, 218.84 {"service":"user-service","stack":"Error: Image generation failed with code 3221225477: A matching Triton is not available, some optimizations will not be enabled\r\nTraceback (most recent call last):\r\n  File \"N:\\3D AI Studio\\pipelines\\ImageGeneration\\env\\Lib\\site-packages\\xformers\\__init__.py\", line 57, in _is_triton_available\r\n    import triton  # noqa\r\n    ^^^^^^^^^^^^^\r\nModuleNotFoundError: No module named 'triton'\r\n\rLoading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s]\rLoading pipeline components...:  29%|##8       | 2/7 [00:18<00:45,  9.11s/it]\r\n\rLoading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]\u001b[A\r\n\rLoading checkpoint shards:  25%|##5       | 1/4 [02:23<07:09, 143.28s/it]\u001b[A\r\n\rLoading checkpoint shards:  50%|#####     | 2/4 [04:30<04:27, 133.78s/it]\u001b[A\r\n\rLoading checkpoint shards:  75%|#######5  | 3/4 [06:42<02:13, 133.12s/it]\u001b[A\r\n\rLoading checkpoint shards: 100%|##########| 4/4 [08:32<00:00, 123.82s/it]\u001b[A\rLoading checkpoint shards: 100%|##########| 4/4 [08:32<00:00, 128.07s/it]\r\n\rLoading pipeline components...:  43%|####2     | 3/7 [08:50<14:35, 218.84s/it]\r\n\rLoading checkpoint shards:   0%|          | 0/5 [00:00<?, ?it/s]\u001b[A\r\n\rLoading checkpoint shards:  20%|##        | 1/5 [05:46<23:06, 346.64s/it]\u001b[A\r\n\rLoading checkpoint shards:  40%|####      | 2/5 [11:31<17:17, 345.84s/it]\u001b[A\r\n\rLoading checkpoint shards:  60%|######    | 3/5 [17:15<11:29, 344.60s/it]\u001b[A\n    at ChildProcess.<anonymous> (N:\\3D AI Studio\\src\\main\\pipelineManager.js:349:36)\n    at ChildProcess.emit (node:events:519:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","timestamp":"2025-07-18 09:46:27"}
error: Error stack: {"0":"E","1":"r","10":"g","100":"z","1000":"a","1001":"d","1002":"i","1003":"n","1004":"g","1005":" ","1006":"p","1007":"i","1008":"p","1009":"e","101":"a","1010":"l","1011":"i","1012":"n","1013":"e","1014":" ","1015":"c","1016":"o","1017":"m","1018":"p","1019":"o","102":"t","1020":"n","1021":"e","1022":"n","1023":"t","1024":"s","1025":".","1026":".","1027":".","1028":":","1029":" ","103":"i","1030":" ","1031":"4","1032":"3","1033":"%","1034":"|","1035":"#","1036":"#","1037":"#","1038":"#","1039":"2","104":"o","1040":" ","1041":" ","1042":" ","1043":" ","1044":" ","1045":"|","1046":" ","1047":"3","1048":"/","1049":"7","105":"n","1050":" ","1051":"[","1052":"0","1053":"8","1054":":","1055":"5","1056":"0","1057":"<","1058":"1","1059":"4","106":"s","1060":":","1061":"3","1062":"5","1063":",","1064":" ","1065":"2","1066":"1","1067":"8","1068":".","1069":"8","107":" ","1070":"4","1071":"s","1072":"/","1073":"i","1074":"t","1075":"]","1076":"\r","1077":"\n","1078":"\r","1079":"L","108":"w","1080":"o","1081":"a","1082":"d","1083":"i","1084":"n","1085":"g","1086":" ","1087":"c","1088":"h","1089":"e","109":"i","1090":"c","1091":"k","1092":"p","1093":"o","1094":"i","1095":"n","1096":"t","1097":" ","1098":"s","1099":"h","11":"e","110":"l","1100":"a","1101":"r","1102":"d","1103":"s","1104":":","1105":" ","1106":" ","1107":" ","1108":"0","1109":"%","111":"l","1110":"|","1111":" ","1112":" ","1113":" ","1114":" ","1115":" ","1116":" ","1117":" ","1118":" ","1119":" ","112":" ","1120":" ","1121":"|","1122":" ","1123":"0","1124":"/","1125":"5","1126":" ","1127":"[","1128":"0","1129":"0","113":"n","1130":":","1131":"0","1132":"0","1133":"<","1134":"?","1135":",","1136":" ","1137":"?","1138":"i","1139":"t","114":"o","1140":"/","1141":"s","1142":"]","1143":"\u001b","1144":"[","1145":"A","1146":"\r","1147":"\n","1148":"\r","1149":"L","115":"t","1150":"o","1151":"a","1152":"d","1153":"i","1154":"n","1155":"g","1156":" ","1157":"c","1158":"h","1159":"e","116":" ","1160":"c","1161":"k","1162":"p","1163":"o","1164":"i","1165":"n","1166":"t","1167":" ","1168":"s","1169":"h","117":"b","1170":"a","1171":"r","1172":"d","1173":"s","1174":":","1175":" ","1176":" ","1177":"2","1178":"0","1179":"%","118":"e","1180":"|","1181":"#","1182":"#","1183":" ","1184":" ","1185":" ","1186":" ","1187":" ","1188":" ","1189":" ","119":" ","1190":" ","1191":"|","1192":" ","1193":"1","1194":"/","1195":"5","1196":" ","1197":"[","1198":"0","1199":"5","12":" ","120":"e","1200":":","1201":"4","1202":"6","1203":"<","1204":"2","1205":"3","1206":":","1207":"0","1208":"6","1209":",","121":"n","1210":" ","1211":"3","1212":"4","1213":"6","1214":".","1215":"6","1216":"4","1217":"s","1218":"/","1219":"i","122":"a","1220":"t","1221":"]","1222":"\u001b","1223":"[","1224":"A","1225":"\r","1226":"\n","1227":"\r","1228":"L","1229":"o","123":"b","1230":"a","1231":"d","1232":"i","1233":"n","1234":"g","1235":" ","1236":"c","1237":"h","1238":"e","1239":"c","124":"l","1240":"k","1241":"p","1242":"o","1243":"i","1244":"n","1245":"t","1246":" ","1247":"s","1248":"h","1249":"a","125":"e","1250":"r","1251":"d","1252":"s","1253":":","1254":" ","1255":" ","1256":"4","1257":"0","1258":"%","1259":"|","126":"d","1260":"#","1261":"#","1262":"#","1263":"#","1264":" ","1265":" ","1266":" ","1267":" ","1268":" ","1269":" ","127":"\r","1270":"|","1271":" ","1272":"2","1273":"/","1274":"5","1275":" ","1276":"[","1277":"1","1278":"1","1279":":","128":"\n","1280":"3","1281":"1","1282":"<","1283":"1","1284":"7","1285":":","1286":"1","1287":"7","1288":",","1289":" ","129":"T","1290":"3","1291":"4","1292":"5","1293":".","1294":"8","1295":"4","1296":"s","1297":"/","1298":"i","1299":"t","13":"g","130":"r","1300":"]","1301":"\u001b","1302":"[","1303":"A","1304":"\r","1305":"\n","1306":"\r","1307":"L","1308":"o","1309":"a","131":"a","1310":"d","1311":"i","1312":"n","1313":"g","1314":" ","1315":"c","1316":"h","1317":"e","1318":"c","1319":"k","132":"c","1320":"p","1321":"o","1322":"i","1323":"n","1324":"t","1325":" ","1326":"s","1327":"h","1328":"a","1329":"r","133":"e","1330":"d","1331":"s","1332":":","1333":" ","1334":" ","1335":"6","1336":"0","1337":"%","1338":"|","1339":"#","134":"b","1340":"#","1341":"#","1342":"#","1343":"#","1344":"#","1345":" ","1346":" ","1347":" ","1348":" ","1349":"|","135":"a","1350":" ","1351":"3","1352":"/","1353":"5","1354":" ","1355":"[","1356":"1","1357":"7","1358":":","1359":"1","136":"c","1360":"5","1361":"<","1362":"1","1363":"1","1364":":","1365":"2","1366":"9","1367":",","1368":" ","1369":"3","137":"k","1370":"4","1371":"4","1372":".","1373":"6","1374":"0","1375":"s","1376":"/","1377":"i","1378":"t","1379":"]","138":" ","1380":"\u001b","1381":"[","1382":"A","1383":"\n","1384":" ","1385":" ","1386":" ","1387":" ","1388":"a","1389":"t","139":"(","1390":" ","1391":"C","1392":"h","1393":"i","1394":"l","1395":"d","1396":"P","1397":"r","1398":"o","1399":"c","14":"e","140":"m","1400":"e","1401":"s","1402":"s","1403":".","1404":"<","1405":"a","1406":"n","1407":"o","1408":"n","1409":"y","141":"o","1410":"m","1411":"o","1412":"u","1413":"s","1414":">","1415":" ","1416":"(","1417":"N","1418":":","1419":"\\","142":"s","1420":"3","1421":"D","1422":" ","1423":"A","1424":"I","1425":" ","1426":"S","1427":"t","1428":"u","1429":"d","143":"t","1430":"i","1431":"o","1432":"\\","1433":"s","1434":"r","1435":"c","1436":"\\","1437":"m","1438":"a","1439":"i","144":" ","1440":"n","1441":"\\","1442":"p","1443":"i","1444":"p","1445":"e","1446":"l","1447":"i","1448":"n","1449":"e","145":"r","1450":"M","1451":"a","1452":"n","1453":"a","1454":"g","1455":"e","1456":"r","1457":".","1458":"j","1459":"s","146":"e","1460":":","1461":"3","1462":"4","1463":"9","1464":":","1465":"3","1466":"6","1467":")","1468":"\n","1469":" ","147":"c","1470":" ","1471":" ","1472":" ","1473":"a","1474":"t","1475":" ","1476":"C","1477":"h","1478":"i","1479":"l","148":"e","1480":"d","1481":"P","1482":"r","1483":"o","1484":"c","1485":"e","1486":"s","1487":"s","1488":".","1489":"e","149":"n","1490":"m","1491":"i","1492":"t","1493":" ","1494":"(","1495":"n","1496":"o","1497":"d","1498":"e","1499":":","15":"n","150":"t","1500":"e","1501":"v","1502":"e","1503":"n","1504":"t","1505":"s","1506":":","1507":"5","1508":"1","1509":"9","151":" ","1510":":","1511":"2","1512":"8","1513":")","1514":"\n","1515":" ","1516":" ","1517":" ","1518":" ","1519":"a","152":"c","1520":"t","1521":" ","1522":"m","1523":"a","1524":"y","1525":"b","1526":"e","1527":"C","1528":"l","1529":"o","153":"a","1530":"s","1531":"e","1532":" ","1533":"(","1534":"n","1535":"o","1536":"d","1537":"e","1538":":","1539":"i","154":"l","1540":"n","1541":"t","1542":"e","1543":"r","1544":"n","1545":"a","1546":"l","1547":"/","1548":"c","1549":"h","155":"l","1550":"i","1551":"l","1552":"d","1553":"_","1554":"p","1555":"r","1556":"o","1557":"c","1558":"e","1559":"s","156":" ","1560":"s","1561":":","1562":"1","1563":"1","1564":"0","1565":"5","1566":":","1567":"1","1568":"6","1569":")","157":"l","1570":"\n","1571":" ","1572":" ","1573":" ","1574":" ","1575":"a","1576":"t","1577":" ","1578":"C","1579":"h","158":"a","1580":"i","1581":"l","1582":"d","1583":"P","1584":"r","1585":"o","1586":"c","1587":"e","1588":"s","1589":"s","159":"s","1590":".","1591":"_","1592":"h","1593":"a","1594":"n","1595":"d","1596":"l","1597":"e","1598":".","1599":"o","16":"e","160":"t","1600":"n","1601":"e","1602":"x","1603":"i","1604":"t","1605":" ","1606":"(","1607":"n","1608":"o","1609":"d","161":")","1610":"e","1611":":","1612":"i","1613":"n","1614":"t","1615":"e","1616":"r","1617":"n","1618":"a","1619":"l","162":":","1620":"/","1621":"c","1622":"h","1623":"i","1624":"l","1625":"d","1626":"_","1627":"p","1628":"r","1629":"o","163":"\r","1630":"c","1631":"e","1632":"s","1633":"s","1634":":","1635":"3","1636":"0","1637":"5","1638":":","1639":"5","164":"\n","1640":")","165":" ","166":" ","167":"F","168":"i","169":"l","17":"r","170":"e","171":" ","172":"\"","173":"N","174":":","175":"\\","176":"3","177":"D","178":" ","179":"A","18":"a","180":"I","181":" ","182":"S","183":"t","184":"u","185":"d","186":"i","187":"o","188":"\\","189":"p","19":"t","190":"i","191":"p","192":"e","193":"l","194":"i","195":"n","196":"e","197":"s","198":"\\","199":"I","2":"r","20":"i","200":"m","201":"a","202":"g","203":"e","204":"G","205":"e","206":"n","207":"e","208":"r","209":"a","21":"o","210":"t","211":"i","212":"o","213":"n","214":"\\","215":"e","216":"n","217":"v","218":"\\","219":"L","22":"n","220":"i","221":"b","222":"\\","223":"s","224":"i","225":"t","226":"e","227":"-","228":"p","229":"a","23":" ","230":"c","231":"k","232":"a","233":"g","234":"e","235":"s","236":"\\","237":"x","238":"f","239":"o","24":"f","240":"r","241":"m","242":"e","243":"r","244":"s","245":"\\","246":"_","247":"_","248":"i","249":"n","25":"a","250":"i","251":"t","252":"_","253":"_","254":".","255":"p","256":"y","257":"\"","258":",","259":" ","26":"i","260":"l","261":"i","262":"n","263":"e","264":" ","265":"5","266":"7","267":",","268":" ","269":"i","27":"l","270":"n","271":" ","272":"_","273":"i","274":"s","275":"_","276":"t","277":"r","278":"i","279":"t","28":"e","280":"o","281":"n","282":"_","283":"a","284":"v","285":"a","286":"i","287":"l","288":"a","289":"b","29":"d","290":"l","291":"e","292":"\r","293":"\n","294":" ","295":" ","296":" ","297":" ","298":"i","299":"m","3":"o","30":" ","300":"p","301":"o","302":"r","303":"t","304":" ","305":"t","306":"r","307":"i","308":"t","309":"o","31":"w","310":"n","311":" ","312":" ","313":"#","314":" ","315":"n","316":"o","317":"q","318":"a","319":"\r","32":"i","320":"\n","321":" ","322":" ","323":" ","324":" ","325":"^","326":"^","327":"^","328":"^","329":"^","33":"t","330":"^","331":"^","332":"^","333":"^","334":"^","335":"^","336":"^","337":"^","338":"\r","339":"\n","34":"h","340":"M","341":"o","342":"d","343":"u","344":"l","345":"e","346":"N","347":"o","348":"t","349":"F","35":" ","350":"o","351":"u","352":"n","353":"d","354":"E","355":"r","356":"r","357":"o","358":"r","359":":","36":"c","360":" ","361":"N","362":"o","363":" ","364":"m","365":"o","366":"d","367":"u","368":"l","369":"e","37":"o","370":" ","371":"n","372":"a","373":"m","374":"e","375":"d","376":" ","377":"'","378":"t","379":"r","38":"d","380":"i","381":"t","382":"o","383":"n","384":"'","385":"\r","386":"\n","387":"\r","388":"L","389":"o","39":"e","390":"a","391":"d","392":"i","393":"n","394":"g","395":" ","396":"p","397":"i","398":"p","399":"e","4":"r","40":" ","400":"l","401":"i","402":"n","403":"e","404":" ","405":"c","406":"o","407":"m","408":"p","409":"o","41":"3","410":"n","411":"e","412":"n","413":"t","414":"s","415":".","416":".","417":".","418":":","419":" ","42":"2","420":" ","421":" ","422":"0","423":"%","424":"|","425":" ","426":" ","427":" ","428":" ","429":" ","43":"2","430":" ","431":" ","432":" ","433":" ","434":" ","435":"|","436":" ","437":"0","438":"/","439":"7","44":"1","440":" ","441":"[","442":"0","443":"0","444":":","445":"0","446":"0","447":"<","448":"?","449":",","45":"2","450":" ","451":"?","452":"i","453":"t","454":"/","455":"s","456":"]","457":"\r","458":"L","459":"o","46":"2","460":"a","461":"d","462":"i","463":"n","464":"g","465":" ","466":"p","467":"i","468":"p","469":"e","47":"5","470":"l","471":"i","472":"n","473":"e","474":" ","475":"c","476":"o","477":"m","478":"p","479":"o","48":"4","480":"n","481":"e","482":"n","483":"t","484":"s","485":".","486":".","487":".","488":":","489":" ","49":"7","490":" ","491":"2","492":"9","493":"%","494":"|","495":"#","496":"#","497":"8","498":" ","499":" ","5":":","50":"7","500":" ","501":" ","502":" ","503":" ","504":" ","505":"|","506":" ","507":"2","508":"/","509":"7","51":":","510":" ","511":"[","512":"0","513":"0","514":":","515":"1","516":"8","517":"<","518":"0","519":"0","52":" ","520":":","521":"4","522":"5","523":",","524":" ","525":" ","526":"9","527":".","528":"1","529":"1","53":"A","530":"s","531":"/","532":"i","533":"t","534":"]","535":"\r","536":"\n","537":"\r","538":"L","539":"o","54":" ","540":"a","541":"d","542":"i","543":"n","544":"g","545":" ","546":"c","547":"h","548":"e","549":"c","55":"m","550":"k","551":"p","552":"o","553":"i","554":"n","555":"t","556":" ","557":"s","558":"h","559":"a","56":"a","560":"r","561":"d","562":"s","563":":","564":" ","565":" ","566":" ","567":"0","568":"%","569":"|","57":"t","570":" ","571":" ","572":" ","573":" ","574":" ","575":" ","576":" ","577":" ","578":" ","579":" ","58":"c","580":"|","581":" ","582":"0","583":"/","584":"4","585":" ","586":"[","587":"0","588":"0","589":":","59":"h","590":"0","591":"0","592":"<","593":"?","594":",","595":" ","596":"?","597":"i","598":"t","599":"/","6":" ","60":"i","600":"s","601":"]","602":"\u001b","603":"[","604":"A","605":"\r","606":"\n","607":"\r","608":"L","609":"o","61":"n","610":"a","611":"d","612":"i","613":"n","614":"g","615":" ","616":"c","617":"h","618":"e","619":"c","62":"g","620":"k","621":"p","622":"o","623":"i","624":"n","625":"t","626":" ","627":"s","628":"h","629":"a","63":" ","630":"r","631":"d","632":"s","633":":","634":" ","635":" ","636":"2","637":"5","638":"%","639":"|","64":"T","640":"#","641":"#","642":"5","643":" ","644":" ","645":" ","646":" ","647":" ","648":" ","649":" ","65":"r","650":"|","651":" ","652":"1","653":"/","654":"4","655":" ","656":"[","657":"0","658":"2","659":":","66":"i","660":"2","661":"3","662":"<","663":"0","664":"7","665":":","666":"0","667":"9","668":",","669":" ","67":"t","670":"1","671":"4","672":"3","673":".","674":"2","675":"8","676":"s","677":"/","678":"i","679":"t","68":"o","680":"]","681":"\u001b","682":"[","683":"A","684":"\r","685":"\n","686":"\r","687":"L","688":"o","689":"a","69":"n","690":"d","691":"i","692":"n","693":"g","694":" ","695":"c","696":"h","697":"e","698":"c","699":"k","7":"I","70":" ","700":"p","701":"o","702":"i","703":"n","704":"t","705":" ","706":"s","707":"h","708":"a","709":"r","71":"i","710":"d","711":"s","712":":","713":" ","714":" ","715":"5","716":"0","717":"%","718":"|","719":"#","72":"s","720":"#","721":"#","722":"#","723":"#","724":" ","725":" ","726":" ","727":" ","728":" ","729":"|","73":" ","730":" ","731":"2","732":"/","733":"4","734":" ","735":"[","736":"0","737":"4","738":":","739":"3","74":"n","740":"0","741":"<","742":"0","743":"4","744":":","745":"2","746":"7","747":",","748":" ","749":"1","75":"o","750":"3","751":"3","752":".","753":"7","754":"8","755":"s","756":"/","757":"i","758":"t","759":"]","76":"t","760":"\u001b","761":"[","762":"A","763":"\r","764":"\n","765":"\r","766":"L","767":"o","768":"a","769":"d","77":" ","770":"i","771":"n","772":"g","773":" ","774":"c","775":"h","776":"e","777":"c","778":"k","779":"p","78":"a","780":"o","781":"i","782":"n","783":"t","784":" ","785":"s","786":"h","787":"a","788":"r","789":"d","79":"v","790":"s","791":":","792":" ","793":" ","794":"7","795":"5","796":"%","797":"|","798":"#","799":"#","8":"m","80":"a","800":"#","801":"#","802":"#","803":"#","804":"#","805":"5","806":" ","807":" ","808":"|","809":" ","81":"i","810":"3","811":"/","812":"4","813":" ","814":"[","815":"0","816":"6","817":":","818":"4","819":"2","82":"l","820":"<","821":"0","822":"2","823":":","824":"1","825":"3","826":",","827":" ","828":"1","829":"3","83":"a","830":"3","831":".","832":"1","833":"2","834":"s","835":"/","836":"i","837":"t","838":"]","839":"\u001b","84":"b","840":"[","841":"A","842":"\r","843":"\n","844":"\r","845":"L","846":"o","847":"a","848":"d","849":"i","85":"l","850":"n","851":"g","852":" ","853":"c","854":"h","855":"e","856":"c","857":"k","858":"p","859":"o","86":"e","860":"i","861":"n","862":"t","863":" ","864":"s","865":"h","866":"a","867":"r","868":"d","869":"s","87":",","870":":","871":" ","872":"1","873":"0","874":"0","875":"%","876":"|","877":"#","878":"#","879":"#","88":" ","880":"#","881":"#","882":"#","883":"#","884":"#","885":"#","886":"#","887":"|","888":" ","889":"4","89":"s","890":"/","891":"4","892":" ","893":"[","894":"0","895":"8","896":":","897":"3","898":"2","899":"<","9":"a","90":"o","900":"0","901":"0","902":":","903":"0","904":"0","905":",","906":" ","907":"1","908":"2","909":"3","91":"m","910":".","911":"8","912":"2","913":"s","914":"/","915":"i","916":"t","917":"]","918":"\u001b","919":"[","92":"e","920":"A","921":"\r","922":"L","923":"o","924":"a","925":"d","926":"i","927":"n","928":"g","929":" ","93":" ","930":"c","931":"h","932":"e","933":"c","934":"k","935":"p","936":"o","937":"i","938":"n","939":"t","94":"o","940":" ","941":"s","942":"h","943":"a","944":"r","945":"d","946":"s","947":":","948":" ","949":"1","95":"p","950":"0","951":"0","952":"%","953":"|","954":"#","955":"#","956":"#","957":"#","958":"#","959":"#","96":"t","960":"#","961":"#","962":"#","963":"#","964":"|","965":" ","966":"4","967":"/","968":"4","969":" ","97":"i","970":"[","971":"0","972":"8","973":":","974":"3","975":"2","976":"<","977":"0","978":"0","979":":","98":"m","980":"0","981":"0","982":",","983":" ","984":"1","985":"2","986":"8","987":".","988":"0","989":"7","99":"i","990":"s","991":"/","992":"i","993":"t","994":"]","995":"\r","996":"\n","997":"\r","998":"L","999":"o","service":"user-service","timestamp":"2025-07-18 09:46:27"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-18 09:48:33"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-18 09:48:33"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-18 09:48:33"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-18 09:48:33"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-18 09:48:33"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-18 09:48:33"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-18 09:48:33"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-18 09:48:34"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-18 09:48:34"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-18 09:48:34"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-18 09:48:34"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-18 09:48:35"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-18 09:48:35"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-18 09:48:35"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-18 09:48:35"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-18 09:48:35"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-18 09:48:35"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-18 09:48:35"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-18 09:48:35"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-18 09:48:35"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-18 09:48:35"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-18 09:48:35"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-18 09:48:35"}
info: [upload-file] Received ΓÇô filename: 20250613_Wood_Chair_With_Brown_Cussions.jpg, size: 989189 bytes (Uint8Array) {"service":"user-service","timestamp":"2025-07-18 09:48:46"}
info: Buffer received (bytes): 989189 {"service":"user-service","timestamp":"2025-07-18 09:48:46"}
info: Uploaded original file saved: N:\3D AI Studio\uploads\6f494f3b-e727-4b1f-b3e3-6e0442064c94\20250613_Wood_Chair_With_Brown_Cussions.jpg {"service":"user-service","timestamp":"2025-07-18 09:48:46"}
info: Starting background removal for N:\3D AI Studio\uploads\6f494f3b-e727-4b1f-b3e3-6e0442064c94\20250613_Wood_Chair_With_Brown_Cussions.jpg... {"service":"user-service","timestamp":"2025-07-18 09:48:46"}
info: Spawning: N:\3D AI Studio\pipelines\Core\env\Scripts\python.exe N:\3D AI Studio\src\main\python_helpers\remove_background.py N:\3D AI Studio\uploads\6f494f3b-e727-4b1f-b3e3-6e0442064c94\20250613_Wood_Chair_With_Brown_Cussions.jpg N:\3D AI Studio\uploads\6f494f3b-e727-4b1f-b3e3-6e0442064c94\20250613_Wood_Chair_With_Brown_Cussions_processed.png {"service":"user-service","timestamp":"2025-07-18 09:48:46"}
info: [load-file] Received request for: uploads/6f494f3b-e727-4b1f-b3e3-6e0442064c94/20250613_Wood_Chair_With_Brown_Cussions_processed.png {"service":"user-service","timestamp":"2025-07-18 09:49:25"}
info: [load-file] Reading absolute path: N:\3D AI Studio\uploads\6f494f3b-e727-4b1f-b3e3-6e0442064c94\20250613_Wood_Chair_With_Brown_Cussions_processed.png {"service":"user-service","timestamp":"2025-07-18 09:49:25"}
info: IPC: run-pipeline called for: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 09:49:42"}
info: runPipeline request: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 09:49:42"}
info: runPipeline: Registered pipelines at call time: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-18 09:49:42"}
info: Checking dependencies for hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 09:49:42"}
info: Final dependency check for hunyuan2-spz-101: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-18 09:49:42"}
[IPC Handler] Sending status: hunyaun - 25% - Launching Hunyaun server and generating 3D model...
[HunyaunServer] generate3DModel called with imagePath: uploads/6f494f3b-e727-4b1f-b3e3-6e0442064c94/20250613_Wood_Chair_With_Brown_Cussions_processed.png
[HunyaunServer] Settings received: {
  ss_steps: 12,
  ss_cfg_strength: 7.5,
  slat_steps: 12,
  slat_cfg_strength: 3,
  randomize_seed: true,
  seed: 757499,
  simplify: 0.95,
  texture_size: 1024,
  enable_lighting_optimizer: true,
  octree_resolution: 128,
  num_inference_steps: 5,
  guidance_scale: 5,
  enable_texture: true,
  face_count: 40000
}
[HunyaunServer] Checking for lingering Python processes...
[HunyaunServer] No Python processes found to clean up (or cleanup failed)
[HunyaunServer] isHunyaunRunning() returned: false
[HunyaunServer] [Hunyaun Progress] Stage tracking reset for new generation
[HunyaunServer] Server not running, starting server...
info: [3D Progress] hunyaun_preprocessing: 0% (Overall: 0%) - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-18 09:49:43"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] Starting server...
[HunyaunServer] RUN_BAT: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\run-projectorz_(faster)\run-stableprojectorz-full-multiview.bat
[HunyaunServer] Batch file exists: true
[HunyaunServer] Hunyaun server process started
[HunyaunServer] startHunyaunServer() called successfully
[HunyaunServer] Waiting for Hunyaun server to be ready...
[HunyaunServer] [STDOUT] N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe"
[HunyaunServer] [STDOUT] "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\get-pip.py"
[HunyaunServer] [STDOUT] DEBUG: VENV_PATH is "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv"
[HunyaunServer] [STDOUT] DEBUG: Checking for "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\activate.bat"
[HunyaunServer] [STDOUT] DEBUG: Virtual environment already exists, skipping creation
[HunyaunServer] [STDOUT]         1 file(s) copied.
[HunyaunServer] [STDOUT] Portable Python located at: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\python\python.exe
[HunyaunServer] [STDOUT] Virtual environment Python set to: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[HunyaunServer] [STDOUT] _
[HunyaunServer] [STDOUT] Current Python: "N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\tools\.\..\code\venv\Scripts\python.exe"
[HunyaunServer] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv
[HunyaunServer] [STDOUT] Starting the server, please wait...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 09:49:51"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 09:50:21"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 09:50:51"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 09:51:21"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 09:51:51"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 09:52:22"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 09:52:52"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun_preprocessing: 0% (Overall: 0%) - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-18 09:53:01"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] [System Info] Python: 3.11.9   | PyTorch: 2.5.1+cu124 | CUDA: 12.4
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[HunyaunServer] [STDOUT] Initializing Hunyuan3D models from tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv
[HunyaunServer] [STDERR] 2025-07-18 09:53:04,415 - hy3dgen.shapgen - INFO - Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv

info: [3D Progress] hunyaun_preprocessing: 0% (Overall: 0%) - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-18 09:53:04"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] 2025-07-18 09:53:04 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
[HunyaunServer] [STDERR] 2025-07-18 09:53:04,416 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface

[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Fetching 3 files: 100%|##########| 3/3 [00:00<?, ?it/s]

[HunyaunServer] [STDERR] 2025-07-18 09:53:15,622 - hy3dgen.shapgen - INFO - Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors

[HunyaunServer] [STDOUT] 2025-07-18 09:53:15 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 09:53:32"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 09:54:02"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun_preprocessing: 0% (Overall: 0%) - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-18 09:54:09"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|##########| 13/13 [00:00<00:00, 3278.77it/s]

Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
Fetching 17 files: 100%|##########| 17/17 [00:00<00:00, 3404.30it/s]

Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
Loading pipeline components...:  17%|#6        | 1/6 [00:01<00:07,  1.58s/it]
Loading pipeline components...:  33%|###3      | 2/6 [00:01<00:02,  1.37it/s]
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Loading pipeline components...:  50%|#####     | 3/6 [00:10<00:13,  4.57s/it]
info: [3D Progress] hunyaun_preprocessing: 0% (Overall: 0%) - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-18 09:54:20"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
Loading pipeline components...:  83%|########3 | 5/6 [00:10<00:02,  2.01s/it]
Loading pipeline components...: 100%|##########| 6/6 [00:11<00:00,  1.87s/it]

Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Loading pipeline components...:  17%|#6        | 1/6 [00:05<00:25,  5.04s/it]
Loading pipeline components...:  33%|###3      | 2/6 [00:05<00:08,  2.14s/it]
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
info: [3D Progress] hunyaun: 0% (Overall: 0%) - Waiting for Hunyaun server to start... {"service":"user-service","timestamp":"2025-07-18 09:54:42"}
[IPC Handler] Sending status: hunyaun - 0% - Waiting for Hunyaun server to start...
Loading pipeline components...:  50%|#####     | 3/6 [00:31<00:38, 12.98s/it]
Loading pipeline components...:  83%|########3 | 5/6 [00:31<00:05,  5.69s/it]
Loading pipeline components...: 100%|##########| 6/6 [00:31<00:00,  5.23s/it]

info: [3D Progress] hunyaun_preprocessing: 0% (Overall: 0%) - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-18 09:54:53"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] 2025-07-18 09:54:52 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
info: [3D Progress] hunyaun_preprocessing: 0% (Overall: 0%) - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-18 09:54:53"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] [STDOUT] VRAM allocated at startup: 0.0MB
[HunyaunServer] [STDOUT] Server is active and listening on 127.0.0.1:7960
[HunyaunServer] Server is running - HTTP endpoint responding
info: [3D Progress] hunyaun_preprocessing: 0% (Overall: 0%) - Image Preprocessing: Preparing image for 3D generation {"service":"user-service","timestamp":"2025-07-18 09:54:53"}
[IPC Handler] Sending status: hunyaun_preprocessing - 0% - Image Preprocessing: Preparing image for 3D generation
[HunyaunServer] Mapped API settings: {
  guidance_scale: 5,
  num_inference_steps: 20,
  octree_resolution: 256,
  texture_size: 1024,
  mesh_simplify_ratio: 0.15,
  num_chunks: 40,
  apply_texture: true
}
[HunyaunServer] Final request data: {
  single_multi_img_input: [ '[IMAGE_DATA_OMITTED]' ],
  seed: 70511,
  guidance_scale: 5,
  num_inference_steps: 20,
  octree_resolution: 256,
  num_chunks: 40,
  mesh_simplify_ratio: 0.15,
  texture_size: 1024,
  apply_texture: true,
  output_format: 'glb'
}
[HunyaunServer] Sending generation request to Hunyaun server (no timeout)...
[HunyaunServer] [STDOUT] ==================================================
[HunyaunServer] [STDOUT] 2025-07-18 09:54:54 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
[HunyaunServer] [STDOUT] 2025-07-18 09:54:54 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
[HunyaunServer] [STDOUT] 2025-07-18 09:54:56 | INFO | hunyuan3d_api | Shape generation model moved to cuda
[HunyaunServer] Socket hang up during POST request - server may still be processing. Starting status polling...
[HunyaunServer] POST error details: ECONNRESET socket hang up
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 09:55:02"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 09:55:08"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::   0%|          | 0/20 [00:00<?, ?it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::   5%|5         | 1/20 [00:02<00:40,  2.11s/it]
Diffusion Sampling::  10%|#         | 2/20 [00:02<00:19,  1.08s/it]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  15%|#5        | 3/20 [00:03<00:17,  1.06s/it]
Diffusion Sampling::  20%|##        | 4/20 [00:04<00:16,  1.05s/it]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 09:55:14"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  25%|##5       | 5/20 [00:05<00:15,  1.04s/it]
Diffusion Sampling::  30%|###       | 6/20 [00:06<00:14,  1.04s/it]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:17"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Diffusion Sampling::  35%|###5      | 7/20 [00:07<00:13,  1.04s/it]
Diffusion Sampling::  40%|####      | 8/20 [00:08<00:12,  1.03s/it]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 09:55:18"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  45%|####5     | 9/20 [00:09<00:11,  1.03s/it]
Diffusion Sampling::  50%|#####     | 10/20 [00:10<00:10,  1.03s/it]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  55%|#####5    | 11/20 [00:11<00:09,  1.03s/it]
Diffusion Sampling::  60%|######    | 12/20 [00:12<00:08,  1.04s/it]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  65%|######5   | 13/20 [00:13<00:07,  1.03s/it]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 09:55:24"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  70%|#######   | 14/20 [00:14<00:06,  1.03s/it]
Diffusion Sampling::  75%|#######5  | 15/20 [00:15<00:05,  1.03s/it]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  80%|########  | 16/20 [00:16<00:04,  1.03s/it]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:27"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Diffusion Sampling::  85%|########5 | 17/20 [00:17<00:03,  1.03s/it]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 09:55:28"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling::  90%|######### | 18/20 [00:18<00:02,  1.03s/it]
Diffusion Sampling::  95%|#########5| 19/20 [00:20<00:01,  1.03s/it]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Diffusion Sampling:: 100%|##########| 20/20 [00:21<00:00,  1.05s/it]

Volume Decoding:   0%|          | 0/425 [00:00<?, ?it/s]
Volume Decoding:   0%|          | 1/425 [00:00<01:14,  5.71it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:   9%|8         | 37/425 [00:00<00:02, 162.78it/s]
Volume Decoding:  14%|#3        | 58/425 [00:01<00:09, 40.75it/s]
Volume Decoding:  17%|#6        | 71/425 [00:02<00:13, 27.23it/s]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 09:55:34"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:34"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  19%|#8        | 80/425 [00:02<00:15, 22.81it/s]
Volume Decoding:  20%|##        | 86/425 [00:03<00:16, 21.01it/s]
Volume Decoding:  21%|##1       | 91/425 [00:03<00:17, 19.44it/s]
Volume Decoding:  22%|##2       | 95/425 [00:03<00:17, 18.54it/s]
Volume Decoding:  23%|##3       | 98/425 [00:03<00:18, 17.79it/s]
Volume Decoding:  24%|##3       | 101/425 [00:04<00:18, 17.16it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  24%|##4       | 104/425 [00:04<00:19, 16.83it/s]
Volume Decoding:  25%|##4       | 106/425 [00:04<00:19, 16.34it/s]
Volume Decoding:  25%|##5       | 108/425 [00:04<00:19, 15.94it/s]
Volume Decoding:  26%|##5       | 110/425 [00:04<00:19, 15.92it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:37"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  26%|##6       | 112/425 [00:04<00:20, 15.40it/s]
Volume Decoding:  27%|##6       | 114/425 [00:05<00:20, 15.48it/s]
Volume Decoding:  27%|##7       | 116/425 [00:05<00:20, 15.03it/s]
Volume Decoding:  28%|##7       | 118/425 [00:05<00:20, 14.83it/s]
Volume Decoding:  28%|##8       | 120/425 [00:05<00:20, 15.08it/s]
Volume Decoding:  29%|##8       | 122/425 [00:05<00:20, 14.92it/s]
Volume Decoding:  29%|##9       | 124/425 [00:05<00:20, 14.68it/s]
Volume Decoding:  30%|##9       | 126/425 [00:05<00:19, 15.01it/s]
Volume Decoding:  30%|###       | 128/425 [00:06<00:20, 14.66it/s]
Volume Decoding:  31%|###       | 130/425 [00:06<00:19, 14.98it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:38"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  31%|###1      | 132/425 [00:06<00:19, 14.67it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  32%|###1      | 134/425 [00:06<00:19, 14.91it/s]
Volume Decoding:  32%|###2      | 136/425 [00:06<00:19, 14.66it/s]
Volume Decoding:  32%|###2      | 138/425 [00:06<00:19, 14.56it/s]
Volume Decoding:  33%|###2      | 140/425 [00:06<00:19, 14.89it/s]
Volume Decoding:  33%|###3      | 142/425 [00:06<00:19, 14.60it/s]
Volume Decoding:  34%|###3      | 144/425 [00:07<00:18, 14.92it/s]
Volume Decoding:  34%|###4      | 146/425 [00:07<00:19, 14.56it/s]
Volume Decoding:  35%|###4      | 148/425 [00:07<00:18, 14.93it/s]
Volume Decoding:  35%|###5      | 150/425 [00:07<00:18, 14.60it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:39"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  36%|###5      | 152/425 [00:07<00:18, 14.93it/s]
Volume Decoding:  36%|###6      | 154/425 [00:07<00:18, 14.73it/s]
Volume Decoding:  37%|###6      | 156/425 [00:07<00:18, 14.60it/s]
Volume Decoding:  37%|###7      | 158/425 [00:08<00:17, 14.93it/s]
Volume Decoding:  38%|###7      | 160/425 [00:08<00:18, 14.71it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  38%|###8      | 162/425 [00:08<00:18, 14.48it/s]
Volume Decoding:  39%|###8      | 164/425 [00:08<00:17, 14.93it/s]
Volume Decoding:  39%|###9      | 166/425 [00:08<00:17, 14.70it/s]
Volume Decoding:  40%|###9      | 168/425 [00:08<00:17, 14.56it/s]
Volume Decoding:  40%|####      | 170/425 [00:08<00:17, 14.92it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:41"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  40%|####      | 172/425 [00:09<00:17, 14.59it/s]
Volume Decoding:  41%|####      | 174/425 [00:09<00:16, 14.93it/s]
Volume Decoding:  41%|####1     | 176/425 [00:09<00:17, 14.60it/s]
Volume Decoding:  42%|####1     | 178/425 [00:09<00:16, 14.93it/s]
Volume Decoding:  42%|####2     | 180/425 [00:09<00:16, 14.60it/s]
Volume Decoding:  43%|####2     | 182/425 [00:09<00:16, 15.00it/s]
Volume Decoding:  43%|####3     | 184/425 [00:09<00:16, 14.76it/s]
Volume Decoding:  44%|####3     | 186/425 [00:09<00:16, 14.63it/s]
Volume Decoding:  44%|####4     | 188/425 [00:10<00:15, 14.95it/s]
Volume Decoding:  45%|####4     | 190/425 [00:10<00:15, 14.82it/s]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 09:55:42"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:42"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  45%|####5     | 192/425 [00:10<00:15, 14.63it/s]
Volume Decoding:  46%|####5     | 194/425 [00:10<00:16, 14.40it/s]
Volume Decoding:  46%|####6     | 196/425 [00:10<00:15, 14.75it/s]
Volume Decoding:  47%|####6     | 198/425 [00:10<00:15, 14.57it/s]
Volume Decoding:  47%|####7     | 200/425 [00:10<00:15, 14.87it/s]
Volume Decoding:  48%|####7     | 202/425 [00:11<00:15, 14.56it/s]
Volume Decoding:  48%|####8     | 204/425 [00:11<00:14, 14.97it/s]
Volume Decoding:  48%|####8     | 206/425 [00:11<00:14, 14.70it/s]
Volume Decoding:  49%|####8     | 208/425 [00:11<00:14, 14.55it/s]
Volume Decoding:  49%|####9     | 210/425 [00:11<00:14, 14.89it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:43"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  50%|####9     | 212/425 [00:11<00:14, 14.64it/s]
Volume Decoding:  50%|#####     | 214/425 [00:11<00:14, 14.89it/s]
Volume Decoding:  51%|#####     | 216/425 [00:11<00:14, 14.61it/s]
Volume Decoding:  51%|#####1    | 218/425 [00:12<00:13, 14.97it/s]
Volume Decoding:  52%|#####1    | 220/425 [00:12<00:14, 14.64it/s]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 09:55:44"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  52%|#####2    | 222/425 [00:12<00:13, 14.89it/s]
Volume Decoding:  53%|#####2    | 224/425 [00:12<00:13, 14.61it/s]
Volume Decoding:  53%|#####3    | 226/425 [00:12<00:13, 14.56it/s]
Volume Decoding:  54%|#####3    | 228/425 [00:12<00:13, 14.97it/s]
Volume Decoding:  54%|#####4    | 230/425 [00:12<00:13, 14.66it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:45"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  55%|#####4    | 232/425 [00:13<00:12, 15.00it/s]
Volume Decoding:  55%|#####5    | 234/425 [00:13<00:13, 14.65it/s]
Volume Decoding:  56%|#####5    | 236/425 [00:13<00:12, 14.96it/s]
Volume Decoding:  56%|#####6    | 238/425 [00:13<00:12, 14.62it/s]
Volume Decoding:  56%|#####6    | 240/425 [00:13<00:12, 14.97it/s]
Volume Decoding:  57%|#####6    | 242/425 [00:13<00:12, 14.74it/s]
Volume Decoding:  57%|#####7    | 244/425 [00:13<00:12, 14.61it/s]
Volume Decoding:  58%|#####7    | 246/425 [00:14<00:11, 14.92it/s]
Volume Decoding:  58%|#####8    | 248/425 [00:14<00:12, 14.59it/s]
Volume Decoding:  59%|#####8    | 250/425 [00:14<00:11, 14.85it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:46"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  59%|#####9    | 252/425 [00:14<00:11, 14.60it/s]
Volume Decoding:  60%|#####9    | 254/425 [00:14<00:11, 14.96it/s]
Volume Decoding:  60%|######    | 256/425 [00:14<00:11, 14.74it/s]
Volume Decoding:  61%|######    | 258/425 [00:14<00:11, 14.49it/s]
Volume Decoding:  61%|######1   | 260/425 [00:14<00:11, 14.85it/s]
Volume Decoding:  62%|######1   | 262/425 [00:15<00:11, 14.70it/s]
Volume Decoding:  62%|######2   | 264/425 [00:15<00:11, 14.53it/s]
Volume Decoding:  63%|######2   | 266/425 [00:15<00:10, 14.84it/s]
Volume Decoding:  63%|######3   | 268/425 [00:15<00:10, 14.58it/s]
Volume Decoding:  64%|######3   | 270/425 [00:15<00:10, 14.48it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:48"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  64%|######4   | 272/425 [00:15<00:10, 14.93it/s]
Volume Decoding:  64%|######4   | 274/425 [00:15<00:10, 14.77it/s]
Volume Decoding:  65%|######4   | 276/425 [00:16<00:10, 14.55it/s]
Volume Decoding:  65%|######5   | 278/425 [00:16<00:09, 14.86it/s]
Volume Decoding:  66%|######5   | 280/425 [00:16<00:09, 14.55it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  66%|######6   | 282/425 [00:16<00:09, 14.89it/s]
Volume Decoding:  67%|######6   | 284/425 [00:16<00:09, 14.58it/s]
Volume Decoding:  67%|######7   | 286/425 [00:16<00:09, 14.97it/s]
Volume Decoding:  68%|######7   | 288/425 [00:16<00:09, 14.69it/s]
Volume Decoding:  68%|######8   | 290/425 [00:16<00:09, 14.99it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:49"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  69%|######8   | 292/425 [00:17<00:09, 14.64it/s]
Volume Decoding:  69%|######9   | 294/425 [00:17<00:08, 14.92it/s]
Volume Decoding:  70%|######9   | 296/425 [00:17<00:08, 14.65it/s]
Volume Decoding:  70%|#######   | 298/425 [00:17<00:08, 14.53it/s]
Volume Decoding:  71%|#######   | 300/425 [00:17<00:08, 14.98it/s]
Volume Decoding:  71%|#######1  | 302/425 [00:17<00:08, 14.80it/s]
Volume Decoding:  72%|#######1  | 304/425 [00:17<00:08, 14.63it/s]
Volume Decoding:  72%|#######2  | 306/425 [00:18<00:07, 14.88it/s]
Volume Decoding:  72%|#######2  | 308/425 [00:18<00:08, 14.62it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  73%|#######2  | 310/425 [00:18<00:07, 14.48it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:50"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  73%|#######3  | 312/425 [00:18<00:07, 14.79it/s]
Volume Decoding:  74%|#######3  | 314/425 [00:18<00:07, 14.57it/s]
Volume Decoding:  74%|#######4  | 316/425 [00:18<00:07, 14.94it/s]
Volume Decoding:  75%|#######4  | 318/425 [00:18<00:07, 14.74it/s]
Volume Decoding:  75%|#######5  | 320/425 [00:19<00:06, 15.02it/s]
Volume Decoding:  76%|#######5  | 322/425 [00:19<00:07, 14.71it/s]
Volume Decoding:  76%|#######6  | 324/425 [00:19<00:06, 14.48it/s]
Volume Decoding:  77%|#######6  | 326/425 [00:19<00:06, 14.82it/s]
Volume Decoding:  77%|#######7  | 328/425 [00:19<00:06, 14.56it/s]
Volume Decoding:  78%|#######7  | 330/425 [00:19<00:06, 14.89it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:52"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  78%|#######8  | 332/425 [00:19<00:06, 14.60it/s]
Volume Decoding:  79%|#######8  | 334/425 [00:19<00:06, 15.03it/s]
Volume Decoding:  79%|#######9  | 336/425 [00:20<00:06, 14.71it/s]
Volume Decoding:  80%|#######9  | 338/425 [00:20<00:05, 14.51it/s]
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  80%|########  | 340/425 [00:20<00:05, 14.80it/s]
Volume Decoding:  80%|########  | 342/425 [00:20<00:05, 15.00it/s]
Volume Decoding:  81%|########  | 344/425 [00:20<00:05, 14.70it/s]
Volume Decoding:  81%|########1 | 346/425 [00:20<00:05, 14.55it/s]
Volume Decoding:  82%|########1 | 348/425 [00:20<00:05, 14.89it/s]
Volume Decoding:  82%|########2 | 350/425 [00:21<00:05, 14.58it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:53"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  83%|########2 | 352/425 [00:21<00:04, 14.90it/s]
Volume Decoding:  83%|########3 | 354/425 [00:21<00:04, 14.61it/s]
Volume Decoding:  84%|########3 | 356/425 [00:21<00:04, 14.96it/s]
Volume Decoding:  84%|########4 | 358/425 [00:21<00:04, 14.71it/s]
Volume Decoding:  85%|########4 | 360/425 [00:21<00:04, 14.61it/s]
Volume Decoding:  85%|########5 | 362/425 [00:21<00:04, 14.97it/s]
Volume Decoding:  86%|########5 | 364/425 [00:22<00:04, 14.74it/s]
Volume Decoding:  86%|########6 | 366/425 [00:22<00:04, 14.63it/s]
Volume Decoding:  87%|########6 | 368/425 [00:22<00:03, 14.49it/s]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 09:55:54"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  87%|########7 | 370/425 [00:22<00:03, 14.91it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:54"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  88%|########7 | 372/425 [00:22<00:03, 14.61it/s]
Volume Decoding:  88%|########8 | 374/425 [00:22<00:03, 14.93it/s]
Volume Decoding:  88%|########8 | 376/425 [00:22<00:03, 14.68it/s]
Volume Decoding:  89%|########8 | 378/425 [00:22<00:03, 14.95it/s]
Volume Decoding:  89%|########9 | 380/425 [00:23<00:03, 14.67it/s]
Volume Decoding:  90%|########9 | 382/425 [00:23<00:02, 14.46it/s]
Volume Decoding:  90%|######### | 384/425 [00:23<00:02, 14.82it/s]
Volume Decoding:  91%|######### | 386/425 [00:23<00:02, 14.59it/s]
Volume Decoding:  91%|#########1| 388/425 [00:23<00:02, 14.99it/s]
Volume Decoding:  92%|#########1| 390/425 [00:23<00:02, 14.76it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:56"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  92%|#########2| 392/425 [00:23<00:02, 14.57it/s]
Volume Decoding:  93%|#########2| 394/425 [00:24<00:02, 14.91it/s]
Volume Decoding:  93%|#########3| 396/425 [00:24<00:01, 14.62it/s]
Volume Decoding:  94%|#########3| 398/425 [00:24<00:01, 14.91it/s]
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 09:55:56"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
Volume Decoding:  94%|#########4| 400/425 [00:24<00:01, 14.59it/s]
Volume Decoding:  95%|#########4| 402/425 [00:24<00:01, 14.88it/s]
Volume Decoding:  95%|#########5| 404/425 [00:24<00:01, 14.60it/s]
Volume Decoding:  96%|#########5| 406/425 [00:24<00:01, 14.56it/s]
Volume Decoding:  96%|#########6| 408/425 [00:25<00:01, 14.96it/s]
Volume Decoding:  96%|#########6| 410/425 [00:25<00:01, 14.68it/s]
info: [3D Progress] hunyaun_generation: 0% (Overall: 0%) - Hunyaun3D Generation: Creating 3D structure from image {"service":"user-service","timestamp":"2025-07-18 09:55:57"}
[IPC Handler] Sending status: hunyaun_generation - 0% - Hunyaun3D Generation: Creating 3D structure from image
Volume Decoding:  97%|#########6| 412/425 [00:25<00:00, 14.96it/s]
Volume Decoding:  97%|#########7| 414/425 [00:25<00:00, 14.63it/s]
Volume Decoding:  98%|#########7| 416/425 [00:25<00:00, 14.46it/s]
Volume Decoding:  98%|#########8| 418/425 [00:25<00:00, 14.83it/s]
Volume Decoding:  99%|#########8| 420/425 [00:25<00:00, 14.63it/s]
Volume Decoding:  99%|#########9| 422/425 [00:25<00:00, 15.01it/s]
Volume Decoding: 100%|#########9| 424/425 [00:26<00:00, 14.71it/s]
Volume Decoding: 100%|##########| 425/425 [00:26<00:00, 16.24it/s]

[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 09:56:00"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
info: [3D Progress] hunyaun: 10% (Overall: 10%) - Generating 3D structure... {"service":"user-service","timestamp":"2025-07-18 09:56:06"}
[IPC Handler] Sending status: hunyaun - 10% - Generating 3D structure...
[HunyaunServer] [STDOUT] 2025-07-18 09:56:07 | INFO | hunyuan3d_api | Processing mesh (faces: 902250)
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 09:56:11 | INFO | hunyuan3d_api | Reducing faces to 857137
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 09:56:12"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 09:56:16 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 09:56:18"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 09:56:20 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
info: [3D Progress] hunyaun_export: 0% (Overall: 0%) - GLB Export: Finalizing 3D model file {"service":"user-service","timestamp":"2025-07-18 09:56:20"}
[IPC Handler] Sending status: hunyaun_export - 0% - GLB Export: Finalizing 3D model file
[HunyaunServer] [STDOUT] 2025-07-18 09:56:20 | INFO | hunyuan3d_api | Texture memory optimization applied
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 09:56:22"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 09:56:28"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 09:56:34"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 09:56:40"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] Error polling /status endpoint (attempt 1/5): socket hang up
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 10:05:42"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 10:05:42 | INFO | hunyuan3d_api | Using chunked image encoding
[HunyaunServer] [STDOUT] 2025-07-18 10:05:43 | INFO | hunyuan3d_api | Using chunked image encoding
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 10:05:44 | INFO | hunyuan3d_api | Using chunked image encoding
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 10:05:48"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 10:05:54"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 10:06:00"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 10:06:06"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 10:06:12"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 10:06:18"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 10:06:24"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 10:06:29 | INFO | hunyuan3d_api | Using chunked VAE decoding
info: [3D Progress] hunyaun_export: 0% (Overall: 0%) - GLB Export: Finalizing 3D model file {"service":"user-service","timestamp":"2025-07-18 10:06:29"}
[IPC Handler] Sending status: hunyaun_export - 0% - GLB Export: Finalizing 3D model file
[HunyaunServer] [STDOUT] 2025-07-18 10:06:29 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 10:06:34"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] Error polling /status endpoint (attempt 1/5): socket hang up
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
info: [3D Progress] hunyaun: 70% (Overall: 70%) - Generating GLB file... {"service":"user-service","timestamp":"2025-07-18 10:07:00"}
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 10:07:00 | INFO | hunyuan3d_api | Applied texture to mesh successfully
[IPC Handler] Sending status: hunyaun - 70% - Generating GLB file...
[HunyaunServer] [STDOUT] 2025-07-18 10:07:04 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 656.71 seconds
[HunyaunServer] [STDOUT] 2025-07-18 10:07:04 | INFO | hunyuan3d_api | Generation completed in 730.78 seconds
info: [3D Progress] hunyaun: 100% (Overall: 100%) - Generation complete {"service":"user-service","timestamp":"2025-07-18 10:07:04"}
[IPC Handler] Sending status: hunyaun - 100% - Generation complete
[HunyaunServer] Downloading generated model...
[HunyaunServer] [STDOUT] 2025-07-18 10:07:04 | INFO | hunyuan3d_api | Client is downloading a model.
[HunyaunServer] Model saved to: N:\3D AI Studio\output\hunyaun_model_2025-07-18T15-07-04-682Z.glb
info: [3D Progress] hunyaun_export: 100% (Overall: 0%) - GLB Export: Finalizing 3D model file {"service":"user-service","timestamp":"2025-07-18 10:07:06"}
[IPC Handler] Sending status: hunyaun_export - 100% - GLB Export: Finalizing 3D model file
info: [load-file] Received request for: output\hunyaun_model_2025-07-18T15-07-04-682Z.glb {"service":"user-service","timestamp":"2025-07-18 10:07:06"}
info: [load-file] Reading absolute path: N:\3D AI Studio\output\hunyaun_model_2025-07-18T15-07-04-682Z.glb {"service":"user-service","timestamp":"2025-07-18 10:07:06"}
warn: [load-file] Received invalid request for: undefined {"service":"user-service","timestamp":"2025-07-18 10:07:07"}
info: All windows closed. {"service":"user-service","timestamp":"2025-07-18 10:07:54"}
info: Quitting app. {"service":"user-service","timestamp":"2025-07-18 10:07:54"}
Press any key to continue . . .