========================================
         3D AI Studio Launcher
========================================

[1/3] Checking Node.js installation...
Γ£ô Node.js found
[2/3] Checking for node_modules...
Γ£ô Dependencies ready
[3/3] Building frontend for production...
The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
vite v5.4.19 building for production...

(!) outDir N:\3D AI Studio\dist\renderer is not inside project root and will not be emptied.
Use --emptyOutDir to override.

✓ 2114 modules transformed.
../../dist/renderer/index.html                     0.21 kB │ gzip:   0.16 kB
../../dist/renderer/assets/index-CN-azzHY.css     44.66 kB │ gzip:   7.35 kB
../../dist/renderer/assets/index-E2ArCN4p.js      11.52 kB │ gzip:   4.64 kB
../../dist/renderer/assets/index-BH9szeq9.js   1,354.41 kB │ gzip: 368.87 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 13.95s
Γ£ô Frontend build complete

========================================
    Starting 3D AI Studio (Production)...
========================================


> 3d-ai-studio@1.0.0 start
> electron .


info: Logger initialized. {"service":"user-service","timestamp":"2025-07-18 17:09:06"}
info: App is ready. {"service":"user-service","timestamp":"2025-07-18 17:09:06"}
info: Console window hidden at startup {"service":"user-service","timestamp":"2025-07-18 17:09:06"}
[Trellis Server] Checking for lingering Python processes...
[Trellis Server] No Python processes found
info: Python process cleanup completed on startup {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Production mode - Loading file: N:\3D AI Studio\dist\renderer\index.html {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Main window web contents started loading {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Splash screen initialization complete {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Splash Status: Loading pipeline configurations... {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Splash Status: Scanning pipelines... {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Loading pipelines from embedded configurations... {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Loaded pipeline: Core {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Loaded pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Loaded pipeline: ImageUpscaling {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Loaded pipeline: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Loaded pipeline: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Ensured pipeline directory exists: Core {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Ensured pipeline directory exists: ImageGeneration {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Ensured pipeline directory exists: ImageUpscaling {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Skipping directory creation for system package pipeline: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Skipping directory creation for system package pipeline: hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Registering pipelines... {"service":"user-service","timestamp":"2025-07-18 17:09:08"}
info: Splash screen displayed {"service":"user-service","timestamp":"2025-07-18 17:09:09"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-18 17:09:09"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-18 17:09:09"}
info: Main window web contents finished loading {"service":"user-service","timestamp":"2025-07-18 17:09:09"}
info: Main window finished loading {"service":"user-service","timestamp":"2025-07-18 17:09:09"}
info: IPC: get-console-visibility called {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loading 21 sample images {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 1/21: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 2/21: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 3/21: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 4/21: Green Flower Pot {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 5/21: Blue Couch {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 6/21: Little Girl {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 7/21: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 8/21: Lantern {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 9/21: Wooden Barrel {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 10/21: Cut Log Piece {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 11/21: White Chair {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 12/21: Greek Statue {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 13/21: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 14/21: Stylized Tree {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 15/21: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 16/21: White Backpack {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 17/21: Potted Plant {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 18/21: Red Myan Pot {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 19/21: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 20/21: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: Loaded sample 21/21: Brown Pot {"service":"user-service","timestamp":"2025-07-18 17:09:10"}
info: IPC: get-config called for key: huggingface-token {"service":"user-service","timestamp":"2025-07-18 17:09:11"}
info: [DEBUG] Found pipeline config.json files: {"0":"N:\\3D AI Studio\\pipelines\\3DPipelines\\gen3d\\hunyuan2-spz-101\\config.json","1":"N:\\3D AI Studio\\pipelines\\3DPipelines\\gen3d\\trellis-stable-projectorz-101\\config.json","2":"N:\\3D AI Studio\\pipelines\\Core\\config.json","3":"N:\\3D AI Studio\\pipelines\\ImageGeneration\\config.json","4":"N:\\3D AI Studio\\pipelines\\ImageUpscaling\\config.json","service":"user-service","timestamp":"2025-07-18 17:09:18"}
info: Registered pipeline: hunyuan2-spz-101 (from N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\config.json) {"service":"user-service","timestamp":"2025-07-18 17:09:18"}
info: Registered pipeline: trellis-stable-projectorz-101 (from N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\config.json) {"service":"user-service","timestamp":"2025-07-18 17:09:18"}
info: Registered pipeline: Core (from N:\3D AI Studio\pipelines\Core\config.json) {"service":"user-service","timestamp":"2025-07-18 17:09:18"}
info: Registered pipeline: ImageGeneration (from N:\3D AI Studio\pipelines\ImageGeneration\config.json) {"service":"user-service","timestamp":"2025-07-18 17:09:18"}
info: Registered pipeline: ImageUpscaling (from N:\3D AI Studio\pipelines\ImageUpscaling\config.json) {"service":"user-service","timestamp":"2025-07-18 17:09:18"}
info: [DEBUG] Final registered pipelines: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-18 17:09:18"}
info: PipelineLoader: Final pipelines after merge: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-18 17:09:18"}
info: Splash Status: Checking core dependencies... {"service":"user-service","timestamp":"2025-07-18 17:09:18"}
info: Checking dependencies for Core {"service":"user-service","timestamp":"2025-07-18 17:09:18"}
warn: Python dependencies not satisfied for Core {"service":"user-service","timestamp":"2025-07-18 17:09:18"}
info: Splash Status: Core dependencies verified {"service":"user-service","timestamp":"2025-07-18 17:09:18"}
info: Splash Status: Finalizing startup... {"service":"user-service","timestamp":"2025-07-18 17:09:18"}
info: Startup complete - showing main window {"service":"user-service","timestamp":"2025-07-18 17:09:21"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-18 17:09:21"}
info: Initialization complete signal sent to renderer {"service":"user-service","timestamp":"2025-07-18 17:09:21"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-18 17:09:21"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-07-18 17:09:21"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"hunyuan2-spz-101","1":"trellis-stable-projectorz-101","2":"Core","3":"ImageGeneration","4":"ImageUpscaling","service":"user-service","timestamp":"2025-07-18 17:09:21"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model sdxl-turbo directory does not exist: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
warn: Required model sdxl-turbo is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model dependency check for ImageGeneration: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:09:31"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model sdxl-turbo directory does not exist: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
warn: Required model sdxl-turbo is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model dependency check for ImageGeneration: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: IPC: install-dependencies called for Core -> python -> all {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Installing dependencies for Core (python:all) {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Component type: string, Component value: 'python' {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Name type: string, Name value: 'all' {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Pipeline found. Dependencies available: python=true, models=true {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Checking component branch - component === 'python': true {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Checking component branch - component === 'models': false {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Entering Python installation branch {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Installing Python 3.11.8 for pipeline Core... {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Setting up Python from embedded distribution for Core... {"service":"user-service","timestamp":"2025-07-18 17:09:36"}
info: Python 3.11.8 successfully installed for pipeline Core {"service":"user-service","timestamp":"2025-07-18 17:11:25"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model sdxl-turbo directory does not exist: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
warn: Required model sdxl-turbo is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model dependency check for ImageGeneration: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:11:27"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:11:30"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:11:30"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:11:30"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:11:30"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:11:30"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:11:30"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model sdxl-turbo directory does not exist: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
warn: Required model sdxl-turbo is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model dependency check for ImageGeneration: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model sdxl-turbo directory does not exist: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
warn: Required model sdxl-turbo is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model dependency check for ImageGeneration: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:11:33"}
info: No models configured for Core {"service":"user-service","timestamp":"2025-07-18 17:19:02"}
info: Checking dependencies for Core (during installation) {"service":"user-service","timestamp":"2025-07-18 17:19:02"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:19:04"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:19:04"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:04"}
info: Dependency check completed during installation for Core {"service":"user-service","timestamp":"2025-07-18 17:19:05"}
info: IPC: install-dependencies called for Core -> models -> all {"service":"user-service","timestamp":"2025-07-18 17:19:05"}
info: Installing dependencies for Core (models:all) {"service":"user-service","timestamp":"2025-07-18 17:19:05"}
info: Component type: string, Component value: 'models' {"service":"user-service","timestamp":"2025-07-18 17:19:05"}
info: Name type: string, Name value: 'all' {"service":"user-service","timestamp":"2025-07-18 17:19:05"}
info: Pipeline found. Dependencies available: python=true, models=true {"service":"user-service","timestamp":"2025-07-18 17:19:05"}
info: Checking component branch - component === 'python': false {"service":"user-service","timestamp":"2025-07-18 17:19:05"}
info: Checking component branch - component === 'models': true {"service":"user-service","timestamp":"2025-07-18 17:19:05"}
info: Entering Models installation branch {"service":"user-service","timestamp":"2025-07-18 17:19:05"}
info: Found 0 models in config:  {"service":"user-service","timestamp":"2025-07-18 17:19:05"}
info: Attempting to install 0 models for Core {"service":"user-service","timestamp":"2025-07-18 17:19:05"}
info: Starting model installation for Core - 0 models to install {"service":"user-service","timestamp":"2025-07-18 17:19:05"}
info: Verifying huggingface_hub is available for Core... {"service":"user-service","timestamp":"2025-07-18 17:19:05"}
info: Checking dependencies for Core (during installation) {"service":"user-service","timestamp":"2025-07-18 17:19:05"}
info: Sending dependency-status-changed event for Core (python) {"service":"user-service","timestamp":"2025-07-18 17:19:06"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model sdxl-turbo directory does not exist: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
warn: Required model sdxl-turbo is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model dependency check for ImageGeneration: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:19:07"}
info: Dependency check completed during installation for Core {"service":"user-service","timestamp":"2025-07-18 17:19:08"}
info: IPC: install-dependencies called for ImageGeneration -> python -> all {"service":"user-service","timestamp":"2025-07-18 17:19:08"}
info: Installing dependencies for ImageGeneration (python:all) {"service":"user-service","timestamp":"2025-07-18 17:19:08"}
info: Component type: string, Component value: 'python' {"service":"user-service","timestamp":"2025-07-18 17:19:08"}
info: Name type: string, Name value: 'all' {"service":"user-service","timestamp":"2025-07-18 17:19:08"}
info: Pipeline found. Dependencies available: python=true, models=true {"service":"user-service","timestamp":"2025-07-18 17:19:08"}
info: Checking component branch - component === 'python': true {"service":"user-service","timestamp":"2025-07-18 17:19:08"}
info: Checking component branch - component === 'models': false {"service":"user-service","timestamp":"2025-07-18 17:19:08"}
info: Entering Python installation branch {"service":"user-service","timestamp":"2025-07-18 17:19:08"}
info: Installing Python 3.11.8 for pipeline ImageGeneration... {"service":"user-service","timestamp":"2025-07-18 17:19:08"}
info: Setting up Python from embedded distribution for ImageGeneration... {"service":"user-service","timestamp":"2025-07-18 17:19:08"}
info: Sending dependency-status-changed event for Core (models) {"service":"user-service","timestamp":"2025-07-18 17:19:09"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model sdxl-turbo directory does not exist: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
warn: Required model sdxl-turbo is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model dependency check for ImageGeneration: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model sdxl-turbo directory does not exist: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
warn: Required model sdxl-turbo is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model dependency check for ImageGeneration: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model sdxl-turbo directory does not exist: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
warn: Required model sdxl-turbo is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model dependency check for ImageGeneration: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:19:10"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Model sdxl-turbo directory does not exist: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
warn: Required model sdxl-turbo is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Model dependency check for ImageGeneration: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:19:15"}
info: Python 3.11.8 successfully installed for pipeline ImageGeneration {"service":"user-service","timestamp":"2025-07-18 17:20:57"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:20:59"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:20:59"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:20:59"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Model sdxl-turbo directory does not exist: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
warn: Required model sdxl-turbo is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Model dependency check for ImageGeneration: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:21:02"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:21:04"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Model sdxl-turbo directory does not exist: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
warn: Required model sdxl-turbo is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Model sdxl-turbo directory does not exist: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
warn: Required model sdxl-turbo is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Model dependency check for ImageGeneration: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Model dependency check for ImageGeneration: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:21:05"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:21:07"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:22:06"}
info: Installing 6 models for ImageGeneration: sdxl-turbo, stable-diffusion-xl-base-1.0, stable-diffusion-xl-refiner-1.0, flux-dev, stable-diffusion-v1-5, stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:44:54"}
info: Model details: [
  {
    "name": "sdxl-turbo",
    "repo_id": "stabilityai/sdxl-turbo"
  },
  {
    "name": "stable-diffusion-xl-base-1.0",
    "repo_id": "stabilityai/stable-diffusion-xl-base-1.0"
  },
  {
    "name": "stable-diffusion-xl-refiner-1.0",
    "repo_id": "stabilityai/stable-diffusion-xl-refiner-1.0"
  },
  {
    "name": "flux-dev",
    "repo_id": "black-forest-labs/FLUX.1-dev"
  },
  {
    "name": "stable-diffusion-v1-5",
    "repo_id": "runwayml/stable-diffusion-v1-5"
  },
  {
    "name": "stable-diffusion-2-1",
    "repo_id": "stabilityai/stable-diffusion-2-1"
  }
] {"service":"user-service","timestamp":"2025-07-18 17:44:54"}
info: Starting model installation for ImageGeneration - 6 models to install {"service":"user-service","timestamp":"2025-07-18 17:44:54"}
info: Using enhanced model repair script for ImageGeneration {"service":"user-service","timestamp":"2025-07-18 17:44:54"}
info: Executing enhanced model repair script: N:\3D AI Studio\pipelines\ImageGeneration\env\Scripts\python.exe utils\helpers\model_repair.py --models-path N:\3D AI Studio\models\ImageGeneration {"service":"user-service","timestamp":"2025-07-18 17:44:54"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:44:56"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:44:56"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:44:56"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Model sdxl-turbo directory does not exist: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
warn: Required model sdxl-turbo is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Model dependency check for ImageGeneration: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:44:59"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:45:01"}
warn: Model repair stderr: N:\3D AI Studio\pipelines\ImageGeneration\env\Lib\site-packages\huggingface_hub\file_download.py:980: UserWarning: `local_dir_use_symlinks` parameter is deprecated and will be ignored. The process to download files to a local folder has been updated and do not rely on symlinks anymore. You only need to pass a destination folder as`local_dir`.
For more details, check out https://huggingface.co/docs/huggingface_hub/main/en/guides/download#download-files-to-local-folder.
  warnings.warn( {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Model repair output: === DOWNLOADING ALL MODELS AND REPAIRING EXISTING ===
Starting model download in: N:\3D AI Studio\models\ImageGeneration
Downloading all models


--- Processing sdxl-turbo ---
Description: SDXL Turbo - Ultra-fast 1-step generation
Downloading sdxl-turbo...
Downloading complete model: sdxl-turbo from stabilityai/sdxl-turbo... {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
warn: Model repair stderr: Fetching 39 files:   0%|          | 0/39 [00:00<?, ?it/s] {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
warn: Model repair stderr: Fetching 39 files:   3%|2         | 1/39 [00:00<00:16,  2.28it/s] {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
warn: Model repair stderr: Fetching 39 files:   8%|7         | 3/39 [00:00<00:05,  6.28it/s] {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Model dependency check for ImageGeneration: satisfied {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: ImageGeneration: Checking 4 key dependencies instead of all 23 for faster loading {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Checking model sdxl-turbo at path: N:\3D AI Studio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Model stable-diffusion-xl-base-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Model stable-diffusion-xl-refiner-1.0 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Checking model flux-dev at path: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Model flux-dev directory does not exist: N:\3D AI Studio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Checking model stable-diffusion-v1-5 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Model stable-diffusion-v1-5 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Checking model stable-diffusion-2-1 at path: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Model stable-diffusion-2-1 directory does not exist: N:\3D AI Studio\models\ImageGeneration\stable-diffusion-2-1 {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
info: Model dependency check for ImageGeneration: satisfied {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
warn: Model repair stderr: Fetching 39 files:  15%|#5        | 6/39 [00:00<00:03,  8.38it/s] {"service":"user-service","timestamp":"2025-07-18 17:45:02"}
warn: Model repair stderr: Fetching 39 files:  21%|##        | 8/39 [00:00<00:03,  9.62it/s] {"service":"user-service","timestamp":"2025-07-18 17:45:03"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Checking model swinir-real-sr-x4 at path: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model swinir-real-sr-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
warn: Required model swinir-real-sr-x4 is not properly installed {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Checking model realesrgan-x4plus at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model realesrgan-x4plus directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Checking model realesrgan-x4plus-anime at path: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model realesrgan-x4plus-anime directory does not exist: N:\3D AI Studio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Checking model swinir-m-x4 at path: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model swinir-m-x4 directory does not exist: N:\3D AI Studio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Checking model 4xlsdir at path: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model 4xlsdir directory does not exist: N:\3D AI Studio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model dependency check for ImageUpscaling: not satisfied {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model dependency check for trellis-stable-projectorz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Microsoft_TRELLIS dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Hunyuan3D-2 validation paths: {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info:   Server path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101 {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info:   Venv path: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info:   Python exe: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info:   ENV folder: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\ENV {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Checking if Python executable exists: N:\3D AI Studio\pipelines\3DPipelines\gen3d\hunyuan2-spz-101\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model dependency check for hunyuan2-spz-101: satisfied {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Hunyuan3D-2 Python executable found {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Hunyuan3D-2 ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Hunyuan3D-2 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Hunyuan3D-2 dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-18 17:45:04"}
info: Model repair output: {"timestamp":"2025-07-18T22:45:17.481959Z","level":"WARN","fields":{"message":"Reqwest(reqwest::Error { kind: Request, source: hyper_util::client::legacy::Error(SendRequest, hyper::Error(Io, Os { code: 10054, kind: ConnectionReset, message: \"An existing connection was forcibly closed by the remote host.\" })) }). Retrying..."},"filename":"D:\\a\\xet-core\\xet-core\\cas_client\\src\\http_client.rs","line_number":242}
{"timestamp":"2025-07-18T22:45:17.482447Z","level":"WARN","fields":{"message":"Retry attempt #0. Sleeping 1.2692175s before the next attempt"},"filename":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\reqwest-retry-0.7.0\\src\\middleware.rs","line_number":171} {"service":"user-service","timestamp":"2025-07-18 17:45:17"}
info: Model repair output: {"timestamp":"2025-07-18T22:45:18.815255Z","level":"WARN","fields":{"message":"Reqwest(reqwest::Error { kind: Request, source: hyper_util::client::legacy::Error(SendRequest, hyper::Error(Io, Os { code: 10054, kind: ConnectionReset, message: \"An existing connection was forcibly closed by the remote host.\" })) }). Retrying..."},"filename":"D:\\a\\xet-core\\xet-core\\cas_client\\src\\http_client.rs","line_number":242}
{"timestamp":"2025-07-18T22:45:18.815413Z","level":"WARN","fields":{"message":"Retry attempt #1. Sleeping 395.6399ms before the next attempt"},"filename":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\reqwest-retry-0.7.0\\src\\middleware.rs","line_number":171} {"service":"user-service","timestamp":"2025-07-18 17:45:18"}
info: Model repair output: {"timestamp":"2025-07-18T22:45:18.964046Z","level":"WARN","fields":{"message":"Reqwest(reqwest::Error { kind: Request, source: hyper_util::client::legacy::Error(SendRequest, hyper::Error(Io, Os { code: 10054, kind: ConnectionReset, message: \"An existing connection was forcibly closed by the remote host.\" })) }). Retrying..."},"filename":"D:\\a\\xet-core\\xet-core\\cas_client\\src\\http_client.rs","line_number":242}
{"timestamp":"2025-07-18T22:45:18.964265Z","level":"WARN","fields":{"message":"Retry attempt #0. Sleeping 681.69ms before the next attempt"},"filename":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\reqwest-retry-0.7.0\\src\\middleware.rs","line_number":171} {"service":"user-service","timestamp":"2025-07-18 17:45:18"}
info: Model repair output: {"timestamp":"2025-07-18T22:45:18.978270Z","level":"WARN","fields":{"message":"Reqwest(reqwest::Error { kind: Request, source: hyper_util::client::legacy::Error(SendRequest, hyper::Error(Io, Os { code: 10054, kind: ConnectionReset, message: \"An existing connection was forcibly closed by the remote host.\" })) }). Retrying..."},"filename":"D:\\a\\xet-core\\xet-core\\cas_client\\src\\http_client.rs","line_number":242}
{"timestamp":"2025-07-18T22:45:18.978501Z","level":"WARN","fields":{"message":"Retry attempt #0. Sleeping 2.2419086s before the next attempt"},"filename":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\reqwest-retry-0.7.0\\src\\middleware.rs","line_number":171} {"service":"user-service","timestamp":"2025-07-18 17:45:18"}
info: Model repair output: {"timestamp":"2025-07-18T22:45:19.660786Z","level":"WARN","fields":{"message":"Reqwest(reqwest::Error { kind: Request, source: hyper_util::client::legacy::Error(SendRequest, hyper::Error(Io, Os { code: 10054, kind: ConnectionReset, message: \"An existing connection was forcibly closed by the remote host.\" })) }). Retrying..."},"filename":"D:\\a\\xet-core\\xet-core\\cas_client\\src\\http_client.rs","line_number":242}
{"timestamp":"2025-07-18T22:45:19.661030Z","level":"WARN","fields":{"message":"Retry attempt #1. Sleeping 5.0430039s before the next attempt"},"filename":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\reqwest-retry-0.7.0\\src\\middleware.rs","line_number":171} {"service":"user-service","timestamp":"2025-07-18 17:45:19"}
info: Model repair output: {"timestamp":"2025-07-18T22:45:21.231884Z","level":"WARN","fields":{"message":"Reqwest(reqwest::Error { kind: Request, source: hyper_util::client::legacy::Error(SendRequest, hyper::Error(Io, Os { code: 10054, kind: ConnectionReset, message: \"An existing connection was forcibly closed by the remote host.\" })) }). Retrying..."},"filename":"D:\\a\\xet-core\\xet-core\\cas_client\\src\\http_client.rs","line_number":242}
{"timestamp":"2025-07-18T22:45:21.232087Z","level":"WARN","fields":{"message":"Retry attempt #1. Sleeping 4.1345105s before the next attempt"},"filename":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\reqwest-retry-0.7.0\\src\\middleware.rs","line_number":171} {"service":"user-service","timestamp":"2025-07-18 17:45:21"}
warn: Model repair stderr: Fetching 39 files:  21%|##        | 8/39 [00:20<00:03,  9.62it/s] {"service":"user-service","timestamp":"2025-07-18 17:45:22"}
