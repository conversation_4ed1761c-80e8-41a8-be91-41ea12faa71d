{"_class_name": "StableDiffusionPipeline", "_diffusers_version": "0.21.0", "_name_or_path": "runwayml/stable-diffusion-v1-5", "requires_safety_checker": true, "feature_extractor": ["transformers", "CLIPImageProcessor"], "safety_checker": ["stable_diffusion_safety_checker", "StableDiffusionSafetyChecker"], "scheduler": ["diffusers", "PNDMScheduler"], "text_encoder": ["transformers", "CLIPTextModel"], "tokenizer": ["transformers", "CLIPTokenizer"], "unet": ["diffusers", "UNet2DConditionModel"], "vae": ["diffusers", "AutoencoderKL"]}