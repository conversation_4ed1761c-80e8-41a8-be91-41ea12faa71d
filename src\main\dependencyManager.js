const path = require('path');
const fs = require('fs').promises;
const fsSync = require('fs');
const { spawn, exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);
const logger = require('./logger');
const { sendStatusToSplash } = require('./splashScreen');
const { dialog, ipcMain } = require('electron');
const semver = require('semver');
const https = require('https');
const AdmZip = require('adm-zip');

// --- Configuration ---
// TODO: This needs a robust way to discover the correct python env for each pipeline
const WORKSPACE_ROOT = path.resolve(__dirname, '../..');
const PIPELINES_DIR = path.join(WORKSPACE_ROOT, 'pipelines');
const MODELS_DIR = path.join(WORKSPACE_ROOT, 'models');
const HELPERS_DIR = path.join(WORKSPACE_ROOT, 'utils', 'helpers');
const TEMPLATE_DIR = path.join(WORKSPACE_ROOT, 'pipeline_templates');
const PYTHON_INSTALL_DIR = path.join(WORKSPACE_ROOT, '_InstallFirst');

// Remove legacy hardcoded paths - now using dynamic paths for all pipelines

// Pipeline Configurations - Single Source of Truth
const PIPELINE_CONFIGS = {
  Core: {
    name: "Core",
    description: "Core utilities and base functionality",
    dependencies: {
      python: [
        "huggingface_hub>=0.20.3",
        "tqdm>=4.66.1", 
        "requests>=2.31.0",
        "rembg>=2.0.50",
        "onnxruntime>=1.16.0",
        "numpy>=1.24.3",
        "pillow>=10.0.0"
      ],
      models: []
    }
  },
  
  ImageGeneration: {
    name: "ImageGeneration", 
    description: "Advanced image generation using multiple Stable Diffusion models",
    dependencies: {
      python: [
        "torch>=2.7.1+cu128",
        "torchvision>=0.18.1+cu128",
        "diffusers>=0.25.0",
        "transformers>=4.36.2",
        "accelerate>=0.26.1",
        "bitsandbytes>=0.37.0",
        "safetensors>=0.4.1",
        "xformers>=0.0.23.post1",
        "optimum-quanto>=0.2.0",
        "einops>=0.8.0",
        "compel>=2.0.0",
        "opencv-python>=********",
        "pillow>=10.0.0",
        "huggingface_hub>=0.20.3",
        "hf_transfer>=0.1.4",
        "huggingface_hub[hf_xet]>=0.20.3",
        "tqdm>=4.66.1",
        "requests>=2.31.0",
        "numpy>=1.24.3",
        "matplotlib>=3.7.1",
        "protobuf>=3.20.0",
        "sentencepiece>=0.1.97",
        "scipy>=1.10.0"
      ],
      models: [
        {
          name: "sdxl-turbo",
          repo_id: "stabilityai/sdxl-turbo", 
          required: true,
          description: "SDXL Turbo - Ultra-fast 1-step generation",
          local_path: "ImageGeneration/sdxl-turbo"
        },
        {
          name: "stable-diffusion-xl-base-1.0",
          repo_id: "stabilityai/stable-diffusion-xl-base-1.0",
          required: false,
          description: "SDXL Base 1.0 - High quality generation",
          local_path: "ImageGeneration/stable-diffusion-xl-base-1.0"
        },
        {
          name: "stable-diffusion-xl-refiner-1.0", 
          repo_id: "stabilityai/stable-diffusion-xl-refiner-1.0",
          required: false,
          description: "SDXL Refiner 1.0 - Quality enhancement",
          local_path: "ImageGeneration/stable-diffusion-xl-refiner-1.0"
        },
        {
          name: "flux-dev",
          repo_id: "black-forest-labs/FLUX.1-dev",
          required: false,
          description: "FLUX Dev - High quality advanced generation with guidance scale support",
          local_path: "ImageGeneration/fluxDev"
        },
        {
          name: "stable-diffusion-v1-5",
          repo_id: "runwayml/stable-diffusion-v1-5", 
          required: false,
          description: "Stable Diffusion v1.5 - Classic model",
          local_path: "ImageGeneration/stable-diffusion-v1-5"
        },
        {
          name: "stable-diffusion-2-1",
          repo_id: "stabilityai/stable-diffusion-2-1",
          required: false,
          description: "Stable Diffusion v2.1 - Improved model", 
          local_path: "ImageGeneration/stable-diffusion-2-1"
        }
      ]
    }
  },

  ImageUpscaling: {
    name: "ImageUpscaling",
    description: "Image upscaling using multiple state-of-the-art models (SwinIR, Real-ESRGAN, UltraSharp, etc.)",
    dependencies: {
      python: [
        "torch>=2.0.0",
        "torchvision>=0.15.0",
        "pillow>=10.0.0",
        "numpy<2.0.0",
        "opencv-python>=4.8.0",
        "timm>=0.9.0",
        "einops>=0.6.0",
        "requests>=2.25.0",
        "tqdm>=4.64.0",
        "hf_transfer>=0.1.4"
      ],
      models: [
        {
          name: "swinir-real-sr-x4",
          repo_id: "valhalla/SwinIR-real-sr-L-x4-GAN",
          required: true,
          description: "SwinIR Real-World Super-Resolution x4 - High quality upscaler",
          local_path: "upscaling/swinir-real-sr-x4",
          files: ["003_realSR_BSRGAN_DFOWMFC_s64w8_SwinIR-L_x4_GAN.pth"]
        },
        {
          name: "realesrgan-x4plus",
          repo_id: "xinntao/Real-ESRGAN",
          required: false,
          description: "Real-ESRGAN x4plus - General purpose upscaler (photo, art, etc.)",
          local_path: "upscaling/realesrgan-x4plus",
          files: ["RealESRGAN_x4plus.pth"]
        },
        {
          name: "realesrgan-x4plus-anime",
          repo_id: "xinntao/Real-ESRGAN",
          required: false,
          description: "Real-ESRGAN x4plus anime - Anime/illustration upscaler",
          local_path: "upscaling/realesrgan-x4plus-anime",
          files: ["RealESRGAN_x4plus_anime_6B.pth"]
        },
        {
          name: "swinir-m-x4",
          repo_id: "JingyunLiang/SwinIR",
          required: false,
          description: "SwinIR-M x4 - Medium SwinIR model for classical super-resolution",
          local_path: "upscaling/swinir-m-x4",
          files: ["001_classicalSR_DF2K_s64w8_SwinIR-M_x4.pth"]
        },
        {
          name: "4xlsdir",
          repo_id: "Phhofm/models",
          required: false,
          description: "4xLSDIR - High-quality photo upscaler trained on LSDIR dataset",
          local_path: "upscaling/4xlsdir",
          files: ["4xLSDIR.pth"]
        }
      ]
    }
  },

  "trellis-stable-projectorz-101": {
    name: "Microsoft TRELLIS",
    description: "Microsoft TRELLIS - Advanced 3D generation from images",
    dependencies: {
      python: [
        {
          name: "Trellis Server System Package",
          description: "Complete Trellis server environment with all dependencies and models",
          required: true,
          system_package: true // Flag to indicate this is a system package managed by run-fp16.bat
        }
      ],
      models: [] // No individual models - all handled by run-fp16.bat
    }
  },

  "hunyuan2-spz-101": {
    name: "Hunyuan3D-2",
    description: "Tencent Hunyuan3D-2 - Advanced 3D generation system",
    dependencies: {
      python: [
        {
          name: "Hunyuan3D-2 Server System Package",
          description: "Complete Hunyuan3D-2 server environment with all dependencies and models",
          required: true,
          system_package: true // Flag to indicate this is a system package managed by run-stableprojectorz-full-multiview.bat
        }
      ],
      models: [] // No individual models - all handled by run-stableprojectorz-full-multiview.bat
    }
  }
};

class DependencyManager {
  constructor(store) {
    this.pipelines = {};
    this.dependencyStatus = {}; 
    this.store = store;
    this.huggingFaceToken = null;
    this.mainWindow = null;

    // Initialize helpers
    this._initializeHelpers().catch(error => {
      logger.error('Failed to initialize helpers:', error);
    });
  }

  setMainWindow(win) {
    this.mainWindow = win;
  }

  setStore(store) {
    this.store = store;
  }

  setHuggingFaceToken(token) {
    if (this.store) {
      this.store.set('huggingface-token', token);
      logger.info('Hugging Face token updated.');
      return { success: true, message: 'Token saved successfully!' };
    } else {
      logger.error('Store not available in DependencyManager.');
      return { success: false, error: 'Internal error: Store not configured.' };
    }
  }

  async scanAndLoadPipelines() {
    logger.info('Loading pipelines from embedded configurations...');
    
    try {
      // Load pipelines from embedded configurations
      for (const [pipelineName, config] of Object.entries(PIPELINE_CONFIGS)) {
        this.pipelines[pipelineName] = config;
        this.dependencyStatus[pipelineName] = {
          name: pipelineName,
          python: { installed: false, details: {} },
          models: { installed: false, details: {} }
        };
        logger.info(`Loaded pipeline: ${pipelineName}`);
      }
      
      // Ensure pipeline directories exist
      for (const pipelineName of Object.keys(PIPELINE_CONFIGS)) {
        await this._ensurePipelineDirectoryExists(pipelineName);
      }
      
    } catch (error) {
      logger.error(`Error loading pipelines: ${error.message}`);
    }
  }

  async _ensurePipelineDirectoryExists(pipelineName) {
    try {
      // Skip creating directories for system package pipelines that use bat files
      const systemPackagePipelines = ['trellis-stable-projectorz-101', 'hunyuan2-spz-101'];
      if (systemPackagePipelines.includes(pipelineName)) {
        logger.info(`Skipping directory creation for system package pipeline: ${pipelineName}`);
        return;
      }

      const pipelineDir = path.join(PIPELINES_DIR, pipelineName);
      await fs.mkdir(pipelineDir, { recursive: true });

      // Write config.json to the pipeline directory for reference
      const config = PIPELINE_CONFIGS[pipelineName];
      if (config) {
        const configPath = path.join(pipelineDir, 'config.json');
        await fs.writeFile(configPath, JSON.stringify(config, null, 2));
      }

      logger.info(`Ensured pipeline directory exists: ${pipelineName}`);
    } catch (error) {
      logger.error(`Failed to create pipeline directory for ${pipelineName}:`, error);
      throw error;
    }
  }

  async getDependencyStatus() {
    logger.info('Getting detailed dependency status for all pipelines...');

    const total = Object.keys(this.pipelines).length;
    let done = 0;

    const emitProgress = () => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('status-progress', {
          done,
          total,
          percent: Math.round((done / total) * 100)
        });
      }
    };

    emitProgress();

    const status = [];
    for (const [name, pipeline] of Object.entries(this.pipelines)) {
      const pythonDeps = pipeline.dependencies?.python || [];
      const modelDeps = pipeline.dependencies?.models || [];
      
      try {
        // Add timeout for each pipeline to prevent hanging
        // Use longer timeout for ImageGeneration due to many dependencies
        const timeoutMs = name === 'ImageGeneration' ? 30000 : 10000;
        const [pythonStatus, modelStatus] = await Promise.race([
          Promise.all([
            this._checkPythonDependencies(name, pythonDeps),
            this._checkModelDependencies(name, modelDeps)
          ]),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error(`Timeout checking ${name} dependencies`)), timeoutMs)
          )
        ]);

        status.push({
          name,
          description: pipeline.description || '',
          python: {
            installed: pythonStatus.satisfied,
            details: pythonStatus.details,
            dependencies: pythonDeps
          },
          models: {
            installed: modelStatus.installed,
            details: modelStatus.details,
            dependencies: modelDeps
          }
        });
      } catch (error) {
        const errorMessage = error?.message || error?.toString() || String(error);
        logger.warn(`Failed to check dependencies for ${name}: ${errorMessage}`);
        // Add pipeline with error status instead of failing completely
        status.push({
          name,
          description: pipeline.description || '',
          python: {
            installed: false,
            details: { error: errorMessage },
            dependencies: pythonDeps
          },
          models: {
            installed: false,
            details: { error: errorMessage },
            dependencies: modelDeps
          }
        });
      }

      done += 1;
      emitProgress();
    }

    return status;
  }

  async installDependencies(pipelineName, component = 'python', name = 'all') {
    logger.info(`Installing dependencies for ${pipelineName} (${component}:${name})`);
    logger.info(`Component type: ${typeof component}, Component value: '${component}'`);
    logger.info(`Name type: ${typeof name}, Name value: '${name}'`);
    


    // --- Custom Microsoft_TRELLIS install ---
    if (pipelineName === 'trellis-stable-projectorz-101' || pipelineName.toLowerCase().includes('trellis')) {
      return await this._installTrellisModule(pipelineName, component, name);
    }

    // --- Custom Hunyuan3D-2 install ---
    if (pipelineName === 'hunyuan2-spz-101' || pipelineName.toLowerCase().includes('hunyuan')) {
      return await this._installHunyuanModule(pipelineName, component, name);
    }

    try {
      // Ensure pipeline exists and is properly configured
      await this._ensurePipelineExists(pipelineName);
      
      const pipeline = this.pipelines[pipelineName];
      if (!pipeline) {
        throw new Error(`Pipeline ${pipelineName} not found`);
      }
      
      logger.info(`Pipeline found. Dependencies available: python=${!!pipeline.dependencies?.python}, models=${!!pipeline.dependencies?.models}`);

      logger.info(`Checking component branch - component === 'python': ${component === 'python'}`);
      logger.info(`Checking component branch - component === 'models': ${component === 'models'}`);
      
      if (component === 'python') {
        logger.info(`Entering Python installation branch`);
        const allDeps = pipeline.dependencies?.python || [];
        const specialPackages = pipeline.dependencies?.special_packages || [];
        const allModels = pipeline.dependencies?.models || [];
        
        if (name === 'all') {
          // Install both Python dependencies AND special packages AND models when installing "all"
          await this._installPythonDependencies(pipelineName, allDeps, true);
          
          // Install special packages after Python dependencies
          if (specialPackages.length > 0) {
            logger.info(`Installing special packages for ${pipelineName}...`);
            await this._installSpecialPackages(pipelineName, specialPackages);
          }
          
          // Install models after special packages are complete
          if (allModels.length > 0) {
            logger.info(`Installing ${allModels.length} models for ${pipelineName}: ${allModels.map(m => m.name).join(', ')}`);
            logger.info(`Model details: ${JSON.stringify(allModels.map(m => ({name: m.name, repo_id: m.repo_id})), null, 2)}`);
            try {
              await this._installModelDependencies(pipelineName, allModels);
              logger.info(`Successfully completed model installation for ${pipelineName}`);
            } catch (modelError) {
              logger.error(`Model installation failed for ${pipelineName}:`, modelError);
              throw modelError;
            }
          } else {
            logger.info(`No models configured for ${pipelineName}`);
          }
        } else {
          // Check if it's a special package first
          const specialPkg = specialPackages.find(p => p.name === name);
          if (specialPkg) {
            await this._installSpecialPackages(pipelineName, [specialPkg]);
          } else {
            // Install individual dependency
            const dep = allDeps.find(d => d.startsWith(name));
            if (!dep) {
              throw new Error(`Dependency ${name} not found in pipeline ${pipelineName}`);
            }
            await this._installPythonDependencies(pipelineName, [dep], false);
          }
        }
      } else if (component === 'models') {
        logger.info(`Entering Models installation branch`);
        const allModels = pipeline.dependencies?.models || [];
        logger.info(`Found ${allModels.length} models in config: ${allModels.map(m => m.name).join(', ')}`);
        
        if (name === 'all') {
          logger.info(`Attempting to install ${allModels.length} models for ${pipelineName}`);
          await this._installModelDependencies(pipelineName, allModels);
        } else {
          // Install individual model
          logger.info(`Looking for individual model: ${name}`);
          const model = allModels.find(m => m.name === name);
          if (!model) {
            logger.error(`Model ${name} not found in pipeline ${pipelineName}. Available models: ${allModels.map(m => m.name).join(', ')}`);
            throw new Error(`Model ${name} not found in pipeline ${pipelineName}`);
          }
          logger.info(`Attempting to install model ${model.name} (repo: ${model.repo_id}) for ${pipelineName}`);
          await this._installModelDependencies(pipelineName, [model]);
        }
      } else {
        logger.error(`Unknown component: ${component}. Expected 'python' or 'models'`);
        throw new Error(`Unknown component: ${component}`);
      }

      // Update dependency status after installation
      const status = await this.checkDependencies(pipelineName, true);
      return status;
      
    } catch (error) {
      logger.error(`Failed to install dependencies for ${pipelineName} (${component}:${name}):`, error);
      
      // Send error progress update for models
      if (component === 'models') {
        this._sendProgress(pipelineName, 'models', name, {
          status: 'Error',
          message: error.message,
          progress: 0
        });
      }
      
      throw error;
    }
  }

  // --- Modified Checkers ---
  async checkDependencies(pipelineName, duringInstallation = false) {
    logger.info(`Checking dependencies for ${pipelineName}${duringInstallation ? ' (during installation)' : ''}`);

    if (!this.pipelines[pipelineName]) {
      throw new Error(`Pipeline ${pipelineName} not found`);
    }

    const config = this.pipelines[pipelineName];
    const pythonDeps = config.dependencies?.python || [];
    const specialPackages = config.dependencies?.special_packages || [];
    const modelDeps = config.dependencies?.models || [];

    try {
      // Check Python dependencies
      if (pythonDeps.length > 0) {
        const pythonStatus = await this._checkPythonDependencies(pipelineName, pythonDeps);
        this.dependencyStatus[pipelineName].python = {
          installed: pythonStatus.satisfied,
          details: pythonStatus.details
        };

        if (!pythonStatus.satisfied) {
          logger.warn(`Python dependencies not satisfied for ${pipelineName}`);
          if (!duringInstallation) return { satisfied: false };
        }
      }

      // Check special packages
      if (specialPackages.length > 0) {
        const requiredSpecialPackages = specialPackages.filter(pkg => pkg.required !== false);
        const optionalSpecialPackages = specialPackages.filter(pkg => pkg.required === false);
        
        // Check required special packages
        if (requiredSpecialPackages.length > 0) {
          const requiredPackageNames = requiredSpecialPackages.map(pkg => `${pkg.name}==${pkg.version}`);
          const requiredStatus = await this._checkPythonDependencies(pipelineName, requiredPackageNames);
          
          // Merge required special package status into python dependencies
          if (!this.dependencyStatus[pipelineName].python) {
            this.dependencyStatus[pipelineName].python = { installed: true, details: {} };
          }
          
          Object.assign(this.dependencyStatus[pipelineName].python.details, requiredStatus.details);
          this.dependencyStatus[pipelineName].python.installed = 
            this.dependencyStatus[pipelineName].python.installed && requiredStatus.satisfied;

          if (!requiredStatus.satisfied) {
            logger.warn(`Required special package dependencies not satisfied for ${pipelineName}`);
            if (!duringInstallation) return { satisfied: false };
          }
        }
        
        // Check optional special packages (don't fail if missing)
        if (optionalSpecialPackages.length > 0) {
          const optionalPackageNames = optionalSpecialPackages.map(pkg => `${pkg.name}==${pkg.version}`);
          const optionalStatus = await this._checkPythonDependencies(pipelineName, optionalPackageNames);
          
          // Merge optional special package status into python dependencies (but don't affect overall status)
          if (!this.dependencyStatus[pipelineName].python) {
            this.dependencyStatus[pipelineName].python = { installed: true, details: {} };
          }
          
          // Mark optional packages as satisfied even if not installed (for UI purposes)
          for (const pkg of optionalSpecialPackages) {
            const pkgName = `${pkg.name}==${pkg.version}`;
            if (optionalStatus.details[pkgName] && !optionalStatus.details[pkgName].satisfied) {
              optionalStatus.details[pkgName].satisfied = true;
              optionalStatus.details[pkgName].installed = `Optional (using fallback: ${pkg.fallback_backend || 'none'})`;
            }
          }
          
          Object.assign(this.dependencyStatus[pipelineName].python.details, optionalStatus.details);
          
          logger.info(`Optional special packages for ${pipelineName}: ${optionalSpecialPackages.map(p => p.name).join(', ')}`);
        }
      }

      // Check model dependencies
      if (modelDeps.length > 0) {
        const modelStatus = await this._checkModelDependencies(pipelineName, modelDeps);
        this.dependencyStatus[pipelineName].models = {
          installed: modelStatus.satisfied,
          details: modelStatus.details
        };

        if (!modelStatus.satisfied) {
          logger.warn(`Model dependencies not satisfied for ${pipelineName}`);
          if (!duringInstallation) return { satisfied: false };
        }
      }

      // During installation, always return true to allow the process to continue
      // Outside of installation, return true only if all dependencies are satisfied
      if (duringInstallation) {
        logger.info(`Dependency check completed during installation for ${pipelineName}`);
        return { satisfied: true };
      }
      
      // Check if all dependency types are satisfied
      const pythonSatisfied = !pythonDeps.length || this.dependencyStatus[pipelineName].python?.installed;
      const modelsSatisfied = !modelDeps.length || this.dependencyStatus[pipelineName].models?.installed;
      
      const allSatisfied = pythonSatisfied && modelsSatisfied;
      logger.info(`Final dependency check for ${pipelineName}: Python=${pythonSatisfied}, Models=${modelsSatisfied}, Overall=${allSatisfied}`);
      
      return { satisfied: allSatisfied };
    } catch (error) {
      logger.error(`Error checking dependencies for ${pipelineName}:`, error);
      return { satisfied: false };
    }
  }

  // --- Installation Logic ---
  async _installPythonDependencies(pipelineName, depsToInstall, installAll = false) {
    // Special case: Microsoft_TRELLIS uses the new module installation approach
    if (pipelineName === 'Microsoft_TRELLIS' || pipelineName.toLowerCase().includes('trellis')) {
      return await this._installTrellisModule(pipelineName, 'python', 'all');
    }

    // Special case: Hunyuan3D-2 uses the new module installation approach
    if (pipelineName === 'Hunyaun3d-2' || pipelineName.toLowerCase().includes('hunyuan')) {
      return await this._installHunyuanModule(pipelineName, 'python', 'all');
    }

    try {
      // Ensure virtual environment exists
      await this._ensureVenvExists(pipelineName);
    } catch (error) {
      logger.error(`Failed to create/verify virtual environment for ${pipelineName}:`, error);
      throw new Error(`Failed to create/verify virtual environment: ${error.message}`);
    }

    const SKIP_PKGS = new Set(['nvidia-index-url']); // Skip packages that are not actual dependencies
    depsToInstall = depsToInstall.filter(pkg => {
      // Ensure pkg is a string
      if (typeof pkg !== 'string') {
        logger.warn(`Skipping non-string dependency: ${JSON.stringify(pkg)}`);
        return false;
      }
      const match = pkg.match(/^[a-zA-Z0-9_-]+/);
      return match && !SKIP_PKGS.has(match[0]);
    });

    const pythonExe = this._getPythonExe(pipelineName);
    const progressName = installAll ? 'all' : depsToInstall.join(' ');

    // Initialize progress
    this._sendProgress(pipelineName, 'python', progressName, {
      status: 'Running',
      message: 'Starting installation...',
      progress: 0,
    });

    // --- Ensure pip, setuptools & wheel are current ---
    this._sendProgress(pipelineName, 'python', progressName, {
      status: 'Running',
      message: 'Updating pip and core packages...',
      progress: 5,
    });

    await new Promise((resolve) => {
      const env = { ...process.env };
      const scriptsDir = path.dirname(pythonExe);
      delete env.PYTHONHOME;
      delete env.PYTHONPATH;
      env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;

      const upgrader = spawn(pythonExe, ['-m', 'pip', 'install', '--upgrade', 'pip', 'setuptools', 'wheel'], { env });
      upgrader.on('error', () => resolve());
      upgrader.on('close', () => resolve());
    });

    this._sendProgress(pipelineName, 'python', progressName, {
      status: 'Running',
      message: 'Checking existing packages...',
      progress: 10,
    });

    // Filter out packages that are already satisfied so we don't reinstall.
    try {
      const check = await this._checkPythonDependencies(pipelineName, depsToInstall);
      const unsatisfied = [];
      for (const dep of depsToInstall) {
        const detail = check.details[dep];
        if (detail && detail.satisfied) {
          // Emit instant complete event for this dep if installing individually.
          if (!installAll) {
            this._sendProgress(pipelineName, 'python', dep, {
              status: 'Complete',
              message: 'Already installed',
              progress: 100,
            });
          }
        } else {
          unsatisfied.push(dep);
        }
      }
      depsToInstall = unsatisfied;
      if (depsToInstall.length === 0) {
        this._sendProgress(pipelineName, 'python', progressName, {
          status: 'Complete',
          message: 'All selected packages already installed.',
          progress: 100,
        });
        return;
      }
    } catch (e) {
      logger.warn('Unable to pre-check python deps:', e.message);
    }

    if (depsToInstall.length === 0) {
      this._sendProgress(pipelineName, 'python', progressName, { 
        status: 'Complete', 
        message: 'No dependencies to install.', 
        progress: 100 
      });
      return;
    }
    
    // Check if any packages need CUDA support (before normalization)
    const originalHasCudaPackages = depsToInstall.some(pkg => 
      pkg.includes('+cu') || pkg.includes('torch') || pkg.includes('torchvision')
    );
    
    // Determine CUDA version from packages
    const detectedCudaVersion = depsToInstall.some(pkg => pkg.includes('+cu121')) ? 'cu121' : 'cu128';

    // Normalize CUDA package requirements (remove +cu128 from version ranges)
    const normalizeCudaPackage = (pkg) => {
      // If package has CUDA label with version range operators, strip the CUDA part
      // pip doesn't allow ranges like >=2.7.1+cu128, only exact versions like ==2.7.1+cu128
      if (pkg.includes('+cu') && /[><=]/.test(pkg) && !/==/.test(pkg)) {
        return pkg.replace(/\+cu\d+/, '');
      }
      return pkg;
    };
    
    depsToInstall = depsToInstall.map(normalizeCudaPackage);

    // Calculate progress weights for each phase
    const priorityPkgs = depsToInstall.filter((d) => d.startsWith('torch'));
    const pytorch3dPkgs = depsToInstall.filter((d) => d.startsWith('pytorch3d'));
    const remainingPkgs = depsToInstall.filter((d) => !d.startsWith('torch') && !d.startsWith('pytorch3d'));
    
    const totalPkgs = depsToInstall.length;
    const progressWeights = {
      priority: priorityPkgs.length / totalPkgs * 0.4,  // 40% for torch packages
      pytorch3d: pytorch3dPkgs.length / totalPkgs * 0.3, // 30% for pytorch3d
      remaining: remainingPkgs.length / totalPkgs * 0.3  // 30% for remaining
    };

    let currentPhase = 0;
    const updateProgress = (phase, phaseProgress) => {
      const baseProgress = {
        0: 10,  // After initial checks
        1: progressWeights.priority * 100,
        2: (progressWeights.priority + progressWeights.pytorch3d) * 100,
        3: 100
      }[currentPhase];

      const phaseWeight = {
        0: progressWeights.priority,
        1: progressWeights.pytorch3d,
        2: progressWeights.remaining
      }[phase];

      let progress;
      if (phase === 2) {
        // For remaining packages, calculate progress based on current phase completion
        progress = Math.min(
          Math.round((progressWeights.priority + progressWeights.pytorch3d) * 100 + (phaseProgress * progressWeights.remaining * 100)),
          100
        );
      } else {
        progress = Math.min(
          Math.round(baseProgress + (phaseProgress * phaseWeight * 100)),
          phase === 2 ? 100 : baseProgress + (phaseWeight * 100)
        );
      }

      return progress;
    };

            const installBatch = (pkgs, batchLabel, extraArgs = [], phase) => {
          // Use CUDA detection from original packages (before normalization)
          const cudaIndexUrl = originalHasCudaPackages ? ['--index-url', 'https://download.pytorch.org/whl/cu128'] : [];
      return new Promise((resolve, reject) => {
        if (pkgs.length === 0) return resolve();

        let stderr = '';  // Initialize stderr variable
        let installationStarted = false;
        let lastProgress = 0;

        this._sendProgress(pipelineName, 'python', progressName, {
          status: 'Running',
          message: `Installing ${batchLabel}: ${pkgs.join(', ')}`,
          progress: updateProgress(phase, 0),
        });

        const env = { ...process.env };
        const scriptsDir = path.dirname(pythonExe);
        delete env.PYTHONHOME;
        delete env.PYTHONPATH;
        env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;
        
        // Add pipeline-specific environment variables
        const pipeline = this.pipelines[pipelineName];
        if (pipeline?.environment) {
          Object.assign(env, pipeline.environment);
        }

        let args = ['-m', 'pip', 'install', '--no-cache-dir', '--progress-bar', 'on', ...extraArgs];
        if (originalHasCudaPackages) {
          args.push('--extra-index-url', `https://download.pytorch.org/whl/${detectedCudaVersion}`);
        }
        args = [...args, ...pkgs];

        const installer = spawn(pythonExe, args, { env });

        const handleChunk = (chunk) => {
          const text = chunk.toString();
          stderr += text;

          // Look for pip installation progress indicators
          if (text.includes('Installing collected packages')) {
            installationStarted = true;
          }

          if (installationStarted) {
            // Update progress based on package installation status
            const newProgress = text.includes('Successfully installed') ? 1 : 0.5;
            if (newProgress > lastProgress) {
              lastProgress = newProgress;
            this._sendProgress(pipelineName, 'python', progressName, {
              status: 'Running',
                message: `Installing ${batchLabel}...`,
                progress: updateProgress(phase, newProgress),
            });
            }
          }
        };

        installer.stderr.on('data', handleChunk);
        installer.stdout.on('data', handleChunk);

        installer.on('error', (err) => {
          stderr += err.message;
        });

        installer.on('close', (code) => {
          if (code !== 0) {
            const fullErrorMessage = `Installation failed for ${batchLabel}. Stderr: ${stderr}`;
            this._sendProgress(pipelineName, 'python', progressName, {
              status: 'Error',
              message: `Installation failed for ${batchLabel}. See main logs for details.`,
              progress: updateProgress(phase, 1),
            });
            logger.error(fullErrorMessage);
            reject(new Error(fullErrorMessage));
          } else {
            currentPhase++;
            this._sendProgress(pipelineName, 'python', progressName, {
              status: phase === 2 ? 'Complete' : 'Running',
              message: phase === 2 ? 'Installation complete.' : `${batchLabel} installed successfully.`,
              progress: phase === 2 ? 100 : updateProgress(phase, 1), // Always 100% when complete
            });
            resolve();
          }
        });
      });
    };

    try {
      // Install priority packages first (torch, etc.)
      if (priorityPkgs.length > 0) {
        await installBatch(priorityPkgs, 'Priority packages', [], 0);
      } else {
        currentPhase++;
      }

      // Install PyTorch3D if needed
      if (pytorch3dPkgs.length > 0) {
        // Determine Python and Torch versions
        let pyMajor = 3, pyMinor = 11;
        try {
          const res = await new Promise((resolve) => {
            const proc = spawn(pythonExe, ['-c', 'import sys; print(f"{sys.version_info.major},{sys.version_info.minor}")']);
            let out = '';
            proc.stdout.on('data', (d) => out += d.toString());
            proc.on('close', () => resolve(out.trim()));
            proc.on('error', () => resolve(''));
          });
          const parts = res.split(',');
          if (parts.length === 2) {
            pyMajor = parts[0];
            pyMinor = parts[1];
          }
        } catch {}

        const wheelUrl = `https://dl.fbaipublicfiles.com/pytorch3d/packaging/wheels/py${pyMajor}.${pyMinor}_cu118_pyt2.0/download.html`;
        await installBatch(pytorch3dPkgs, 'PyTorch3D', ['-f', wheelUrl], 1);
         } else {
        currentPhase++;
      }

      // Install remaining packages
      if (remainingPkgs.length > 0) {
        await installBatch(remainingPkgs, 'Remaining packages', [], 2);
        } else {
        currentPhase++;
        this._sendProgress(pipelineName, 'python', progressName, {
          status: 'Complete',
          message: 'Installation complete.',
          progress: 100,
        });
      }

    } catch (error) {
      throw error;
    }
  }

  async _createVenv(pipelineName) {
    const venvPath = this._getVenvPath(pipelineName);
    
    // Ensure Python is installed for this pipeline first
    await this._ensurePythonInstalled(pipelineName);
    const pythonPath = this._getPythonPath(pipelineName);
    
    // Create the pipeline directory structure if it doesn't exist
    await fs.mkdir(path.dirname(venvPath), { recursive: true });
    
    try {
      await this._runCommand(pythonPath, ['-m', 'virtualenv', venvPath]);
      return venvPath;
    } catch (error) {
      logger.error(`Failed to create virtual environment for ${pipelineName}:`, error);
      throw error;
    }
  }

  async _installSpecialPackages(pipelineName, specialPackages) {
    try {
      // Ensure virtual environment exists
      await this._ensureVenvExists(pipelineName);
    } catch (error) {
      logger.error(`Failed to create/verify virtual environment for ${pipelineName}:`, error);
      throw new Error(`Failed to create/verify virtual environment: ${error.message}`);
    }

    const pythonExe = this._getPythonExe(pipelineName);
    const progressName = specialPackages.length === 1 ? specialPackages[0].name : 'special_packages';
    const installedPackages = [];
    const failedPackages = [];

    for (let i = 0; i < specialPackages.length; i++) {
      const pkg = specialPackages[i];
      const progress = Math.round(((i + 1) / specialPackages.length) * 100);

      this._sendProgress(pipelineName, 'python', progressName, {
        status: 'Running',
        message: `Installing special package: ${pkg.name}@${pkg.version}`,
        progress: Math.round((i / specialPackages.length) * 100),
      });

      try {
        await new Promise((resolve, reject) => {
          const env = { ...process.env };
          const scriptsDir = path.dirname(pythonExe);
          delete env.PYTHONHOME;
          delete env.PYTHONPATH;
          env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;
          
          // Add pipeline-specific environment variables
          const pipeline = this.pipelines[pipelineName];
          if (pipeline?.environment) {
            Object.assign(env, pipeline.environment);
          }

          let args;
          if (pkg.install_url.endsWith('.whl')) {
            // Direct wheel installation
            args = ['-m', 'pip', 'install', pkg.install_url];
          } else {
            // Installation with find-links
            args = ['-m', 'pip', 'install', `${pkg.name}==${pkg.version}`, '-f', pkg.install_url];
          }
          const installer = spawn(pythonExe, args, { env });

          let stderr = '';
          const handleChunk = (chunk) => {
            stderr += chunk.toString();
          };

          installer.stderr.on('data', handleChunk);
          installer.stdout.on('data', handleChunk);

          installer.on('error', (err) => {
            stderr += err.message;
          });

          installer.on('close', (code) => {
            if (code !== 0) {
              const errorMessage = `Failed to install special package ${pkg.name}. Stderr: ${stderr}`;
              logger.error(errorMessage);
              reject(new Error(errorMessage));
            } else {
              logger.info(`Successfully installed special package ${pkg.name}@${pkg.version}`);
              resolve();
            }
          });
        });
        
        installedPackages.push(pkg);
        logger.info(`Successfully installed special package ${pkg.name}@${pkg.version}`);
      } catch (error) {
        if (pkg.required === false) {
          logger.warn(`Optional special package ${pkg.name} failed to install: ${error.message}`);
          failedPackages.push(pkg);
          
          // Handle fallback behavior for optional packages
          if (pkg.fallback_backend) {
            logger.info(`Using fallback backend '${pkg.fallback_backend}' for ${pkg.name}`);
            // Update environment variables for fallback
            const pipeline = this.pipelines[pipelineName];
            if (pipeline?.environment) {
              if (pkg.name === 'flash-attn' && pkg.fallback_backend === 'math') {
                pipeline.environment.TRELLIS_ATTN_BACKEND = 'math';
                logger.info(`Set TRELLIS_ATTN_BACKEND to 'math' as fallback for flash-attn`);
              }
            }
          }
          
          this._sendProgress(pipelineName, 'python', progressName, {
            status: 'Running',
            message: `Skipped optional package ${pkg.name} (using fallback: ${pkg.fallback_backend || 'none'})`,
            progress: progress,
          });
        } else {
          this._sendProgress(pipelineName, 'python', progressName, {
            status: 'Error',
            message: `Failed to install required ${pkg.name}: ${error.message}`,
            progress: progress,
          });
          throw error;
        }
      }
    }

    const successMessage = installedPackages.length > 0 
      ? `Special packages installed: ${installedPackages.map(p => p.name).join(', ')}`
      : 'No special packages were installed';
    
    if (failedPackages.length > 0) {
      logger.info(`Failed optional packages: ${failedPackages.map(p => p.name).join(', ')}`);
    }

    this._sendProgress(pipelineName, 'python', progressName, {
      status: 'Complete',
      message: successMessage,
      progress: 100,
    });
  }

  async _checkPythonDependencies(pipelineName, requiredDeps) {
    // Special case: Microsoft_TRELLIS uses validation based on venv folder contents
    if (pipelineName === 'trellis-stable-projectorz-101' || pipelineName.toLowerCase().includes('trellis')) {
      const isValid = await this._validateTrellisInstallation();
      const result = { satisfied: isValid, details: {} };

      // Handle system package dependencies
      for (const dep of requiredDeps) {
        if (typeof dep === 'object' && dep.system_package) {
          // This is a system package - check if Trellis server environment is installed
          result.details[dep.name] = {
            satisfied: isValid,
            installed: isValid ? 'System Package Installed' : 'System Package Not Installed',
            required: dep.name,
            description: dep.description
          };
        } else {
          // Legacy string dependency format
          result.details[dep] = {
            satisfied: isValid,
            installed: isValid ? 'Installed' : 'Not installed',
            required: dep
          };
        }
      }

      logger.info(`Microsoft_TRELLIS dependency validation: ${isValid ? 'PASSED' : 'FAILED'}`);
      return result;
    }

    // Special case: Hunyuan3D-2 uses validation based on venv folder contents
    if (pipelineName === 'hunyuan2-spz-101' || pipelineName.toLowerCase().includes('hunyuan')) {
      const isValid = await this._validateHunyuanInstallation();
      const result = { satisfied: isValid, details: {} };

      // Handle system package dependencies
      for (const dep of requiredDeps) {
        if (typeof dep === 'object' && dep.system_package) {
          // This is a system package - check if Hunyuan server environment is installed
          result.details[dep.name] = {
            satisfied: isValid,
            installed: isValid ? 'System Package Installed' : 'System Package Not Installed',
            required: dep.name,
            description: dep.description
          };
        } else {
          // Legacy string dependency format
          result.details[dep] = {
            satisfied: isValid,
            installed: isValid ? 'Installed' : 'Not installed',
            required: dep
          };
        }
      }

      logger.info(`Hunyuan3D-2 dependency validation: ${isValid ? 'PASSED' : 'FAILED'}`);
      return result;
    }

    // Special case: ImageGeneration - only check key dependencies to speed up the process
    if (pipelineName === 'ImageGeneration') {
      const keyDeps = ['torch', 'diffusers', 'transformers'];
      const filteredDeps = requiredDeps.filter(dep =>
        keyDeps.some(key => dep.toLowerCase().startsWith(key))
      );
      if (filteredDeps.length < requiredDeps.length) {
        logger.info(`ImageGeneration: Checking ${filteredDeps.length} key dependencies instead of all ${requiredDeps.length} for faster loading`);
        requiredDeps = filteredDeps;
      }
    }

    const pythonExe = this._getPythonExe(pipelineName);
    const result = { satisfied: true, details: {} };

    // Check if Python executable exists first
    try {
      await fs.access(pythonExe);
    } catch {
      // Python executable doesn't exist, mark all dependencies as not installed
      for (const dep of requiredDeps) {
        result.details[dep] = { satisfied: false, installed: 'Python not found', required: dep };
        result.satisfied = false;
      }
      return result;
    }

    // Helper to check a single package with timeout
    const checkPackage = async (pkg) => {
      // Normalize CUDA package requirements for checking (same as installation)
      const normalizedPkg = pkg.includes('+cu') && /[><=]/.test(pkg) && !/==/.test(pkg) 
        ? pkg.replace(/\+cu\d+/, '') 
        : pkg;
      
      const name = normalizedPkg.match(/^[a-zA-Z0-9_-]+/)[0];
      const version = normalizedPkg.match(/[><=]=\s*([0-9.]+)/)?.[1];
      const cudaTag = pkg.match(/\+cu(\d+)/)?.[1]; // Keep original for CUDA detection

      try {
          const env = { ...process.env };
        const scriptsDir = path.dirname(pythonExe);
          delete env.PYTHONHOME;
        delete env.PYTHONPATH;
          env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;
          
          // Add pipeline-specific environment variables
          const pipeline = this.pipelines[pipelineName];
          if (pipeline?.environment) {
            Object.assign(env, pipeline.environment);
          }
          
        // Add timeout to prevent hanging
        const output = await Promise.race([
          new Promise((resolve) => {
            const proc = spawn(pythonExe, [
              '-c',
              `import importlib.metadata as m; print(m.version('${name}'))`,
            ], { env });
            let out = '';
            proc.stdout.on('data', (d) => out += d.toString());
            proc.on('close', () => resolve(out.trim()));
            proc.on('error', () => resolve(''));
          }),
          new Promise((resolve) => setTimeout(() => resolve(''), 5000)) // 5 second timeout
        ]);

        if (!output) {
          return { satisfied: false, installed: 'Not installed', required: pkg };
        }

        const installed = output.trim();
        if (version) {
          // Normalize both installed and required versions for comparison
          // Strip CUDA suffixes like +cu128 for semver comparison
          const normalizedInstalled = installed.replace(/\+cu\d+/, '');
          const normalizedRequired = version.replace(/\+cu\d+/, '');
          
          // Use custom version comparison for multi-component versions (like *********)
          const compareVersions = (installed, required) => {
            const installedParts = installed.split('.').map(Number);
            const requiredParts = required.split('.').map(Number);
            
            const maxLength = Math.max(installedParts.length, requiredParts.length);
            
            for (let i = 0; i < maxLength; i++) {
              const installedPart = installedParts[i] || 0;
              const requiredPart = requiredParts[i] || 0;
              
              if (installedPart > requiredPart) return 1;
              if (installedPart < requiredPart) return -1;
            }
            return 0;
          };
          
          const satisfied = compareVersions(normalizedInstalled, normalizedRequired) >= 0;
          
          // Debug logging for version comparison
          if (!satisfied) {
            logger.warn(`Version mismatch for ${name}: installed ${installed} (normalized: ${normalizedInstalled}) does not satisfy >=${normalizedRequired}`);
          }
          
          return {
            satisfied,
            installed,
            required: pkg
          };
        }

        return { satisfied: true, installed, required: pkg };
      } catch (e) {
        return { satisfied: false, installed: 'Not installed', required: pkg };
      }
    };

    // Check each required dependency
    for (const dep of requiredDeps) {
      try {
        const status = await checkPackage(dep);
        result.details[dep] = status;
        if (!status.satisfied) {
          result.satisfied = false;
        }
      } catch (error) {
        logger.warn(`Failed to check dependency ${dep} for ${pipelineName}:`, error);
        result.details[dep] = { satisfied: false, installed: 'Check failed', required: dep };
        result.satisfied = false;
      }
    }

    return result;
  }

  async _checkModelDependencies(pipelineName, requiredModels) {
    const result = { satisfied: true, details: {} };
    for (const model of requiredModels) {
      try {
        let localPath;
        // For upscaler models, always check models/upscaling/{model.name}/
        const upscalerNames = [
          'swinir-real-sr-x4',
          'realesrgan-x4plus',
          'realesrgan-x4plus-anime',
          'swinir-m-x4',
          '4xultrasharp'
        ];
        if (upscalerNames.includes(model.name)) {
          localPath = path.join(MODELS_DIR, 'upscaling', model.name);
        } else {
          localPath = path.isAbsolute(model.local_path)
            ? model.local_path
            : path.join(MODELS_DIR, model.local_path);
        }
        logger.info(`Checking model ${model.name} at path: ${localPath}`);
        // Check if model directory exists and contains required files
        let isInstalled = false;
        try {
          await fs.access(localPath);
          const files = await fs.readdir(localPath);
          // For upscaler models, check for required file(s)
          if (upscalerNames.includes(model.name) && model.files && model.files.length > 0) {
            isInstalled = model.files.every(f => files.includes(f));
          } else if (files.length > 0) {
            isInstalled = true;
          } else {
            logger.warn(`Model ${model.name} directory exists but is empty: ${localPath}`);
          }
        } catch (accessError) {
          logger.info(`Model ${model.name} directory does not exist: ${localPath}`);
        }
        result.details[model.name] = {
          name: model.name,
          repo_id: model.repo_id,
          installed: isInstalled,
          required: model.required,
          description: model.description,
          local_path: localPath
        };
        if (!isInstalled && model.required) {
          logger.warn(`Required model ${model.name} is not properly installed`);
          result.satisfied = false;
        }
      } catch (error) {
        logger.error(`Error checking model ${model.name}:`, error);
        result.details[model.name] = {
          name: model.name,
          repo_id: model.repo_id,
          installed: false,
          required: model.required,
          description: model.description || '',
          error: error.message
        };
        if (model.required) {
          result.satisfied = false;
        }
      }
    }
    logger.info(`Model dependency check for ${pipelineName}: ${result.satisfied ? 'satisfied' : 'not satisfied'}`);
    return result;
  }

  async _ensurePythonInstalled(pipelineName) {
    const pythonPath = this._getPythonPath(pipelineName);
    const pythonDir = path.dirname(pythonPath);
    
    try {
      // Check if Python is already installed for this pipeline
      await fs.access(pythonPath);
      logger.info(`Python detected for pipeline ${pipelineName}, checking version...`);
      
      // Check Python version to ensure it's 3.11.8
      let pythonVersion = null;
      try {
        pythonVersion = await new Promise((resolve, reject) => {
          const versionCheck = spawn(pythonPath, ['--version'], {
            stdio: 'pipe'
          });
          let output = '';
          versionCheck.stdout.on('data', (data) => {
            output += data.toString();
          });
          versionCheck.stderr.on('data', (data) => {
            output += data.toString();
          });
          versionCheck.on('close', (code) => {
            if (code === 0) {
              const match = output.match(/Python (\d+\.\d+\.\d+)/);
              resolve(match ? match[1] : null);
            } else {
              reject(new Error('Failed to get Python version'));
            }
          });
          versionCheck.on('error', () => reject(new Error('Failed to run Python version check')));
        });
      } catch (error) {
        logger.warn(`Failed to check Python version for ${pipelineName}: ${error.message}`);
        pythonVersion = null;
      }
      
      if (pythonVersion !== '3.11.8') {
        logger.warn(`Incorrect Python version detected for ${pipelineName}: ${pythonVersion || 'unknown'}, expected 3.11.8`);
        logger.info(`Removing existing Python installation and reinstalling 3.11.8 for ${pipelineName}`);
        
        this._sendProgress(pipelineName, 'python', 'setup', {
          status: 'Running',
          message: `Upgrading Python to 3.11.8...`,
          progress: 10
        });
        
        // Remove existing Python installation
        try {
          await fs.rm(pythonDir, { recursive: true, force: true });
          logger.info(`Removed existing Python installation for ${pipelineName}`);
        } catch (error) {
          logger.warn(`Failed to remove existing Python for ${pipelineName}: ${error.message}`);
        }
        
        // Fall through to reinstall Python 3.11.8
        throw new Error('Python version mismatch, reinstalling');
      }
      
      logger.info(`Correct Python version (3.11.8) confirmed for pipeline ${pipelineName}`);
      
      // Also check if virtualenv is available, install if missing
      try {
        await new Promise((resolve, reject) => {
          const checkVenv = spawn(pythonPath, ['-m', 'virtualenv', '--version'], {
            stdio: 'pipe'
          });
          checkVenv.on('close', (code) => {
            if (code === 0) {
              resolve();
            } else {
              reject(new Error('virtualenv not found'));
            }
          });
          checkVenv.on('error', () => reject(new Error('virtualenv not found')));
        });
        logger.info(`Virtualenv already available for pipeline ${pipelineName}`);
      } catch {
        // Virtualenv not available, install it
        logger.info(`Installing virtualenv for existing Python in pipeline ${pipelineName}`);
        this._sendProgress(pipelineName, 'python', 'setup', {
          status: 'Running',
          message: `Installing virtualenv...`,
          progress: 50
        });
        
        await new Promise((resolve, reject) => {
          const installer = spawn(pythonPath, ['-m', 'pip', 'install', 'virtualenv'], {
            stdio: 'pipe'
          });

          installer.on('error', (err) => {
            reject(new Error(`Failed to install virtualenv: ${err.message}`));
          });

          installer.on('close', (code) => {
            if (code === 0) {
              resolve();
            } else {
              reject(new Error(`Virtualenv installation failed with code ${code}`));
            }
          });
        });
        
        this._sendProgress(pipelineName, 'python', 'setup', {
          status: 'Complete',
          message: `Virtualenv installed for ${pipelineName}`,
          progress: 100
        });
      }
      
      return true;
    } catch {
      // Python not found, download and install it
      logger.info(`Installing Python 3.11.8 for pipeline ${pipelineName}...`);
      
      // Send progress update
      this._sendProgress(pipelineName, 'python', 'setup', {
        status: 'Running',
        message: `Setting up Python 3.11.8 for ${pipelineName}...`,
        progress: 10
      });
      
      try {
        // Create Python directory for this pipeline
        await fs.mkdir(pythonDir, { recursive: true });
        
        // Check if we have the installer in _InstallFirst
        const installerPath = path.join(PYTHON_INSTALL_DIR, 'python-3.11.9-amd64.exe');
        
                try {
          await fs.access(installerPath);
          
          // Use embedded Python distribution instead of installer for better control
          logger.info(`Setting up Python from embedded distribution for ${pipelineName}...`);
          
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Setting up Python environment...`,
            progress: 30
          });
          
          // Download Python embedded distribution (more portable and reliable)
          const embedUrl = 'https://www.python.org/ftp/python/3.11.8/python-3.11.8-embed-amd64.zip';
          const tempZip = path.join(pythonDir, 'python-embed.zip');
          
          // Download embedded Python
          await new Promise((resolve, reject) => {
            const file = fsSync.createWriteStream(tempZip);
            https.get(embedUrl, (response) => {
              response.pipe(file);
              file.on('finish', () => {
                file.close();
                resolve();
              });
            }).on('error', (err) => {
              fs.unlink(tempZip).catch(() => {});
              reject(new Error(`Failed to download Python embedded: ${err.message}`));
            });
          });
          
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Extracting Python...`,
            progress: 50
          });
          
          // Extract Python embedded
          const zip = new AdmZip(tempZip);
          zip.extractAllTo(pythonDir, true);
          
          // Clean up zip file
          await fs.unlink(tempZip).catch(() => {});
          
          // Enable pip by modifying python311._pth file
          const pthFile = path.join(pythonDir, 'python311._pth');
          try {
            let pthContent = await fs.readFile(pthFile, 'utf8');
            // Uncomment the import site line to enable pip
            pthContent = pthContent.replace('#import site', 'import site');
            await fs.writeFile(pthFile, pthContent);
          } catch (err) {
            logger.warn(`Could not modify python311._pth: ${err.message}`);
          }
          
          // Download and install pip manually for embedded Python
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Installing pip...`,
            progress: 70
          });
          
          const getpipUrl = 'https://bootstrap.pypa.io/get-pip.py';
          const getpipPath = path.join(pythonDir, 'get-pip.py');
          
          // Download get-pip.py
          await new Promise((resolve, reject) => {
            const file = fsSync.createWriteStream(getpipPath);
            https.get(getpipUrl, (response) => {
              response.pipe(file);
              file.on('finish', () => {
                file.close();
                resolve();
              });
            }).on('error', (err) => {
              fs.unlink(getpipPath).catch(() => {});
              reject(new Error(`Failed to download get-pip.py: ${err.message}`));
            });
          });
          
          // Install pip
          const currentPythonPath = this._getPythonPath(pipelineName);
          await new Promise((resolve, reject) => {
            const installer = spawn(currentPythonPath, [getpipPath], {
              cwd: pythonDir,
              stdio: 'pipe'
            });

            installer.on('error', (err) => {
              reject(new Error(`Failed to install pip: ${err.message}`));
            });

            installer.on('close', (code) => {
              // Clean up get-pip.py
              fs.unlink(getpipPath).catch(() => {});
              if (code === 0) {
                resolve();
              } else {
                reject(new Error(`Pip installation failed with code ${code}`));
              }
            });
          });
          
          // Install virtualenv since embedded Python doesn't include venv module
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Installing virtualenv...`,
            progress: 80
          });
          
          await new Promise((resolve, reject) => {
            const installer = spawn(currentPythonPath, ['-m', 'pip', 'install', 'virtualenv'], {
              cwd: pythonDir,
              stdio: 'pipe'
            });

            installer.on('error', (err) => {
              reject(new Error(`Failed to install virtualenv: ${err.message}`));
            });

            installer.on('close', (code) => {
              if (code === 0) {
                resolve();
              } else {
                reject(new Error(`Virtualenv installation failed with code ${code}`));
              }
            });
          });
          
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Python setup completed, verifying...`,
            progress: 85
          });
           
                 } catch {
          // No local installer, use embedded Python distribution directly
          logger.info(`Downloading Python embedded distribution for ${pipelineName}...`);
          
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Downloading Python 3.11.8...`,
            progress: 20
          });
          
          const embedUrl = 'https://www.python.org/ftp/python/3.11.8/python-3.11.8-embed-amd64.zip';
          const tempZip = path.join(pythonDir, 'python-embed.zip');
          
          // Download embedded Python
          await new Promise((resolve, reject) => {
            const file = fsSync.createWriteStream(tempZip);
            https.get(embedUrl, (response) => {
              response.pipe(file);
              file.on('finish', () => {
                file.close();
                resolve();
              });
            }).on('error', (err) => {
              fs.unlink(tempZip).catch(() => {});
              reject(new Error(`Failed to download Python embedded: ${err.message}`));
            });
          });
          
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Extracting Python...`,
            progress: 40
          });
          
          // Extract Python embedded
          const zip = new AdmZip(tempZip);
          zip.extractAllTo(pythonDir, true);
          
          // Clean up zip file
          await fs.unlink(tempZip).catch(() => {});
          
          // Enable pip by modifying python311._pth file
          const pthFile = path.join(pythonDir, 'python311._pth');
          try {
            let pthContent = await fs.readFile(pthFile, 'utf8');
            // Uncomment the import site line to enable pip
            pthContent = pthContent.replace('#import site', 'import site');
            await fs.writeFile(pthFile, pthContent);
          } catch (err) {
            logger.warn(`Could not modify python311._pth: ${err.message}`);
          }
          
          // Download and install pip manually for embedded Python
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Installing pip...`,
            progress: 70
          });
          
          const getpipUrl = 'https://bootstrap.pypa.io/get-pip.py';
          const getpipPath = path.join(pythonDir, 'get-pip.py');
          
          // Download get-pip.py
          await new Promise((resolve, reject) => {
            const file = fsSync.createWriteStream(getpipPath);
            https.get(getpipUrl, (response) => {
              response.pipe(file);
              file.on('finish', () => {
                file.close();
                resolve();
              });
            }).on('error', (err) => {
              fs.unlink(getpipPath).catch(() => {});
              reject(new Error(`Failed to download get-pip.py: ${err.message}`));
            });
          });
          
          // Install pip
          const currentPythonPath = this._getPythonPath(pipelineName);
          await new Promise((resolve, reject) => {
            const installer = spawn(currentPythonPath, [getpipPath], {
              cwd: pythonDir,
              stdio: 'pipe'
            });

            installer.on('error', (err) => {
              reject(new Error(`Failed to install pip: ${err.message}`));
            });

            installer.on('close', (code) => {
              // Clean up get-pip.py
              fs.unlink(getpipPath).catch(() => {});
              if (code === 0) {
                resolve();
              } else {
                reject(new Error(`Pip installation failed with code ${code}`));
              }
            });
          });
          
          // Install virtualenv since embedded Python doesn't include venv module
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Installing virtualenv...`,
            progress: 80
          });
          
          await new Promise((resolve, reject) => {
            const installer = spawn(currentPythonPath, ['-m', 'pip', 'install', 'virtualenv'], {
              cwd: pythonDir,
              stdio: 'pipe'
            });

            installer.on('error', (err) => {
              reject(new Error(`Failed to install virtualenv: ${err.message}`));
            });

            installer.on('close', (code) => {
              if (code === 0) {
                resolve();
              } else {
                reject(new Error(`Virtualenv installation failed with code ${code}`));
              }
            });
          });
          
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Python setup completed, verifying...`,
            progress: 85
          });
        }
         
         // Verify Python installation
        const finalPythonPath = this._getPythonPath(pipelineName);
        await fs.access(finalPythonPath);
        logger.info(`Python 3.11.8 successfully installed for pipeline ${pipelineName}`);
        
        this._sendProgress(pipelineName, 'python', 'setup', {
          status: 'Complete',
          message: `Python 3.11.8 ready for ${pipelineName}`,
          progress: 100
        });
        
        return true;
        
      } catch (error) {
        logger.error(`Failed to install Python for pipeline ${pipelineName}:`, error);
        throw new Error(`Failed to install Python 3.11.8: ${error.message}`);
      }
    }
  }

  async _ensureVenvExists(pipelineName) {
    const venvPath = this._getVenvPath(pipelineName);
    const pythonExe = this._getPythonExe(pipelineName);

    try {
      // Check if venv exists and is valid
      await fs.access(pythonExe);
      return true;
    } catch {
      // Create new venv if it doesn't exist or is invalid
      try {
        // Ensure Python is installed for this pipeline
        await this._ensurePythonInstalled(pipelineName);
        const pythonPath = this._getPythonPath(pipelineName);

        // Remove existing venv if it's corrupted
        try {
          await fs.rm(venvPath, { recursive: true, force: true });
        } catch {}

        // Create venv directory
        await fs.mkdir(path.dirname(venvPath), { recursive: true });

        // Create new venv using virtualenv (since embedded Python doesn't have venv module)
        await new Promise((resolve, reject) => {
          const pipelineEnv = { ...process.env };
          
          // Add pipeline-specific environment variables
          const pipeline = this.pipelines[pipelineName];
          if (pipeline?.environment) {
            Object.assign(pipelineEnv, pipeline.environment);
          }
          
          // Clear Python environment variables to avoid conflicts
          pipelineEnv.PYTHONPATH = '';
          pipelineEnv.PYTHONHOME = '';
          
          const venvProcess = spawn(pythonPath, ['-m', 'virtualenv', venvPath], {
            env: pipelineEnv
          });

          venvProcess.stderr.on('data', (data) => {
            logger.error(`Venv creation stderr: ${data}`);
          });

          venvProcess.on('error', (err) => {
            reject(new Error(`Failed to create virtual environment: ${err.message}`));
          });

          venvProcess.on('close', (code) => {
            if (code === 0) {
              resolve();
        } else {
              reject(new Error(`Virtual environment creation failed with code ${code}`));
            }
          });
        });

        // Upgrade pip in the new venv
        await new Promise((resolve, reject) => {
          const pipelineEnv = { ...process.env };
          const scriptsDir = path.dirname(pythonExe);
          
          // Add pipeline-specific environment variables
          const pipeline = this.pipelines[pipelineName];
          if (pipeline?.environment) {
            Object.assign(pipelineEnv, pipeline.environment);
          }
          
          pipelineEnv.PYTHONPATH = '';
          pipelineEnv.PYTHONHOME = '';
          pipelineEnv.PATH = `${scriptsDir}${path.delimiter}${pipelineEnv.PATH}`;
          
          const pipUpgrade = spawn(pythonExe, ['-m', 'pip', 'install', '--no-cache-dir', '--upgrade', 'pip'], {
            env: pipelineEnv
          });

          pipUpgrade.on('error', (err) => {
            reject(new Error(`Failed to upgrade pip: ${err.message}`));
          });

          pipUpgrade.on('close', (code) => {
            if (code === 0) {
              resolve();
            } else {
              reject(new Error(`Pip upgrade failed with code ${code}`));
            }
          });
        });

        return true;
            } catch (error) {
        logger.error(`Failed to create virtual environment for ${pipelineName}:`, error);
        throw error;
      }
    }
  }

  async _installFluxModel(model, localPath, pythonExe, pipelineName, progressName, hfToken) {
    logger.info(`Installing FLUX model ${model.name} using diffusers to: ${localPath}`);
    logger.info(`Token provided: ${hfToken ? 'Yes' : 'No'}`);

    const scriptContent = `
import os
import sys
import json
import time
import traceback

def report_progress(progress_type, message, progress=0, **kwargs):
    data = {
        'type': progress_type,
        'message': message,
        'progress': progress,
        'model': '${model.name}',
        'timestamp': time.time(),
        **kwargs
    }
    print(json.dumps(data), flush=True)

try:
    # Import required libraries
    from diffusers import FluxPipeline
    import torch
    
    repo_id = "${model.repo_id}"
    local_dir = "${localPath.replace(/\\/g, '/')}"

    # Get token from command line argument if provided
    token = sys.argv[1] if len(sys.argv) > 1 else None

    # Only use token if it's not empty
    if not token or token.strip() == "":
        token = None
        report_progress('warning', 'No Hugging Face token provided - using anonymous access', 3)
    else:
        report_progress('info', f'Using Hugging Face token (length: {len(token)})', 3)

    report_progress('init', f'Initializing FLUX installation for {repo_id}', 5)
    
    # Create directory if it doesn't exist
    os.makedirs(local_dir, exist_ok=True)
    
    report_progress('checking', 'Checking FLUX repository access...', 10)
    
    # Download complete FLUX model using diffusers
    report_progress('downloading', 'Downloading complete FLUX model...', 20)
    
    # Use CPU-friendly settings for download
    pipeline = FluxPipeline.from_pretrained(
        repo_id,
        torch_dtype=torch.float32,  # Use float32 for compatibility during download
        low_cpu_mem_usage=True,
        token=token
    )
    
    report_progress('saving', 'Saving FLUX model to local directory...', 70)
    
    # Save the complete pipeline to local directory
    pipeline.save_pretrained(local_dir)
    
    report_progress('verifying', 'Verifying FLUX installation...', 90)
    
    # Verify the installation
    saved_files = []
    total_size = 0
    for root, dirs, files in os.walk(local_dir):
        for file in files:
            if not file.startswith('.'):
                file_path = os.path.join(root, file)
                saved_files.append(os.path.relpath(file_path, local_dir))
                try:
                    total_size += os.path.getsize(file_path)
                except:
                    pass
    
    if len(saved_files) == 0:
        report_progress('error', 'No FLUX files were saved', 90)
        sys.exit(1)
    
    # Check for essential FLUX components
    has_model_index = any('model_index.json' in f for f in saved_files)
    has_transformer = any('transformer' in f for f in saved_files)
    has_vae = any('vae' in f for f in saved_files)
    
    if not (has_model_index and has_transformer and has_vae):
        report_progress('error', 'FLUX model appears incomplete - missing essential components', 95)
        sys.exit(1)
    
    size_mb = round(total_size / (1024 * 1024), 1)
    report_progress('complete', f'Successfully installed complete FLUX model', 100, 
        files_saved=len(saved_files), total_size_mb=size_mb)
    
except ImportError as import_error:
    report_progress('error', f'Required libraries not available: {import_error}', 0)
    print(f"Please ensure diffusers and torch are installed: {import_error}", file=sys.stderr)
    sys.exit(1)
except Exception as e:
    report_progress('error', f'FLUX installation failed: {e}', 0)
    traceback.print_exc()
    sys.exit(1)
`;

    // Write the FLUX installation script
    const scriptPath = path.join(HELPERS_DIR, `install_flux_${model.name.replace(/[^a-zA-Z0-9]/g, '_')}.py`);
    await fs.writeFile(scriptPath, scriptContent);
    logger.info(`Created FLUX installation script at: ${scriptPath}`);

    // Execute the FLUX installation script
    await new Promise((resolve, reject) => {
      const downloadTimeout = setTimeout(() => {
        logger.error(`FLUX model installation for ${model.name} timed out after 90 minutes`);
        reject(new Error(`FLUX model installation timed out after 90 minutes`));
      }, 90 * 60 * 1000); // Extended timeout for FLUX
      
      const cleanup = () => {
        clearTimeout(downloadTimeout);
      };
      
      const env = { ...process.env };
      const scriptsDir = path.dirname(pythonExe);
      delete env.PYTHONHOME;
      delete env.PYTHONPATH;
      env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;
      env.HF_HUB_ENABLE_HF_TRANSFER = '1';
      
      const args = [scriptPath];
      if (hfToken) {
        args.push(hfToken);
      }
      const installer = spawn(pythonExe, args, { env });
      
      let stdout = '';
      let stderr = '';
      
      installer.stdout.on('data', (data) => {
        const text = data.toString();
        stdout += text;
        
        // Parse JSON progress updates
        const lines = text.split('\n').filter(line => line.trim());
        
        for (const line of lines) {
          try {
            const progressData = JSON.parse(line.trim());
            
            if (progressData.type && progressData.model === model.name) {
              logger.info(`FLUX installation progress (${model.name}): ${progressData.type} - ${progressData.message} (${progressData.progress}%)`);
              
              let status = 'Running';
              let progress = progressData.progress || 0;
              let message = progressData.message || `Installing ${model.name}...`;
              
              if (progressData.type === 'complete') {
                status = 'Running';
                progress = 100;
                message = `${model.name} installed successfully`;
              } else if (progressData.type === 'error') {
                status = 'Error';
                progress = 100;
                message = progressData.message || `Failed to install ${model.name}`;
              }
              
              this._sendProgress(pipelineName, 'models', progressName, {
                status,
                message,
                progress: Math.round(progress)
              });
            }
          } catch (parseError) {
            // Not JSON, treat as regular log output
            if (line.trim()) {
              logger.info(`FLUX installation stdout (${model.name}): ${line.trim()}`);
            }
          }
        }
      });

      installer.stderr.on('data', (data) => {
        const text = data.toString();
        stderr += text;
        logger.info(`FLUX installation stderr (${model.name}): ${text.trim()}`);
      });

      installer.on('error', (err) => {
        logger.error(`Failed to spawn FLUX installation process for ${model.name}:`, err);
        cleanup();
        reject(new Error(`Failed to install FLUX model ${model.name}: ${err.message}`));
      });

      installer.on('close', async (code) => {
        cleanup();
        logger.info(`FLUX installation script for ${model.name} completed with code ${code}`);
        
        if (code !== 0) {
          logger.error(`FLUX installation failed for ${model.name}. Stdout: ${stdout}`);
          logger.error(`FLUX installation failed for ${model.name}. Stderr: ${stderr}`);

          // Clean up on failure
          try {
            const isEmpty = (await fs.readdir(localPath)).length === 0;
            if (isEmpty) {
              await fs.rmdir(localPath);
              logger.info(`Cleaned up empty FLUX directory: ${localPath}`);
            }
          } catch (cleanupError) {
            logger.warn(`Could not clean up FLUX directory: ${cleanupError.message}`);
          }

          // Provide more specific error messages based on the stderr content and exit code
          let errorMessage = `FLUX installation failed with code ${code}.`;
          if (code === 3221225477 || code === -1073741819) {
            // Windows access violation error
            errorMessage = `FLUX installation crashed (access violation). This may be due to insufficient memory, GPU driver issues, or corrupted installation. Try restarting the application and ensure you have enough free RAM and VRAM.`;
          } else if (stderr.includes('401') && stderr.includes('Unauthorized')) {
            errorMessage = `FLUX installation failed: Invalid or missing Hugging Face token. Please check your token in Settings.`;
          } else if (stderr.includes('ModuleNotFoundError')) {
            errorMessage = `FLUX installation failed: Missing Python dependencies. Please install Python dependencies first.`;
          } else if (stderr.includes('CUDA') || stderr.includes('GPU')) {
            errorMessage = `FLUX installation failed: GPU/CUDA related issue. Check your GPU drivers and CUDA installation.`;
          } else if (stderr.includes('OutOfMemoryError') || stderr.includes('out of memory')) {
            errorMessage = `FLUX installation failed: Out of memory. FLUX requires significant RAM/VRAM. Try closing other applications or use a machine with more memory.`;
          }

          reject(new Error(errorMessage));
        } else {
          // Verify the installation
          try {
            const files = await fs.readdir(localPath);
            if (files.length === 0) {
              logger.error(`FLUX directory is empty after installation: ${localPath}`);
              reject(new Error(`FLUX installation appeared to succeed but no files were saved`));
            } else {
              logger.info(`Successfully installed FLUX model ${model.name}, ${files.length} files/folders created`);
              
              // Send success progress
              this._sendProgress(pipelineName, 'models', progressName, {
                status: 'Complete',
                message: `${model.name} installed successfully`,
                progress: 100
              });
              
              resolve();
            }
          } catch (verifyError) {
            logger.error(`Could not verify FLUX installation: ${verifyError.message}`);
            reject(new Error(`Could not verify FLUX installation: ${verifyError.message}`));
          }
        }
      });
    });

    // Clean up the installation script
    try {
      await fs.unlink(scriptPath);
      logger.info(`Cleaned up FLUX installation script: ${scriptPath}`);
    } catch (cleanupError) {
      logger.warn(`Could not clean up FLUX installation script: ${cleanupError.message}`);
    }
  }

  async _installUpscalerModel(model, localPath, pythonExe, pipelineName, progressName, hfToken) {
    logger.info(`Installing upscaler model ${model.name} to: ${localPath}`);

    // Define the specific download URLs for upscaler model weights
    const upscalerDownloads = {
      'realesrgan-x4plus': {
        url: 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth',
        filename: 'RealESRGAN_x4plus.pth'
      },
      'realesrgan-x4plus-anime': {
        url: 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.2.4/RealESRGAN_x4plus_anime_6B.pth',
        filename: 'RealESRGAN_x4plus_anime_6B.pth'
      },
      'swinir-m-x4': {
        url: 'https://github.com/JingyunLiang/SwinIR/releases/download/v0.0/001_classicalSR_DF2K_s64w8_SwinIR-M_x4.pth',
        filename: '001_classicalSR_DF2K_s64w8_SwinIR-M_x4.pth'
      },
      '4xlsdir': {
        url: 'https://github.com/Phhofm/models/raw/main/4xLSDIR/4xLSDIR.pth',
        filename: '4xLSDIR.pth'
      },
      'swinir-real-sr-x4': {
        url: 'https://github.com/JingyunLiang/SwinIR/releases/download/v0.0/003_realSR_BSRGAN_DFOWMFC_s64w8_SwinIR-L_x4_GAN.pth',
        filename: '003_realSR_BSRGAN_DFOWMFC_s64w8_SwinIR-L_x4_GAN.pth'
      }
    };

    const downloadInfo = upscalerDownloads[model.name];
    if (!downloadInfo) {
      throw new Error(`Unknown upscaler model: ${model.name}`);
    }

    // Clean up any existing empty directory
    try {
      const stats = await fs.stat(localPath);
      if (stats.isDirectory()) {
        const files = await fs.readdir(localPath);
        if (files.length === 0) {
          await fs.rmdir(localPath);
          logger.info(`Cleaned up empty directory: ${localPath}`);
        }
      }
    } catch {
      // Directory doesn't exist, which is fine
    }

    // Create directory if it doesn't exist
    await fs.mkdir(localPath, { recursive: true });

    const targetPath = path.join(localPath, downloadInfo.filename);

    // Check if already downloaded
    try {
      const stats = await fs.stat(targetPath);
      if (stats.isFile() && stats.size > 0) {
        logger.info(`Upscaler model ${model.name} already exists at ${targetPath}`);

        this._sendProgress(pipelineName, 'models', progressName, {
          status: 'Complete',
          message: `${model.name} already installed`,
          progress: 100
        });
        return;
      }
    } catch {
      // File doesn't exist, proceed with download
    }

    this._sendProgress(pipelineName, 'models', progressName, {
      status: 'Running',
      message: `Downloading ${model.name} weights...`,
      progress: 10
    });

    // Use the more robust _downloadFile method
    try {
      await this._downloadFile(downloadInfo.url, targetPath, hfToken, (progressData) => {
        const progress = Math.round(progressData.progress * 0.8) + 10; // 10-90%
        this._sendProgress(pipelineName, 'models', progressName, {
          status: 'Running',
          message: `Downloading ${model.name}... ${Math.round(progressData.downloaded / 1024 / 1024)}MB${progressData.total ? ` / ${Math.round(progressData.total / 1024 / 1024)}MB` : ''}`,
          progress: progress
        });
      });

      logger.info(`Successfully downloaded upscaler model ${model.name}`);
      this._sendProgress(pipelineName, 'models', progressName, {
        status: 'Complete',
        message: `${model.name} installed successfully`,
        progress: 100
      });
    } catch (error) {
      logger.error(`Failed to download upscaler model ${model.name}:`, error);
      throw new Error(`Download failed: ${error.message}`);
    }
  }

  async _installFlux4BitModel(model, localPath, pythonExe, pipelineName, progressName, hfToken) {
    const gitRepo = 'https://github.com/HighCWu/flux-4bit.git';
    const targetDir = path.join(MODELS_DIR, 'ImageGeneration', 'flux-dev-4bit-git');
    logger.info(`[FLUX 4-bit install] Cloning ${gitRepo} to ${targetDir}`);

    // Remove old directory if it exists
    if (fsSync.existsSync(targetDir)) {
      logger.info(`[FLUX 4-bit install] Removing old directory: ${targetDir}`);
      await fs.rm(targetDir, { recursive: true, force: true });
    }

    // Clone the repo
    await new Promise((resolve, reject) => {
      const git = spawn('git', ['clone', '--depth', '1', gitRepo, targetDir]);
      git.stdout.on('data', d => logger.info(`[FLUX 4-bit install][git] ${d}`));
      git.stderr.on('data', d => logger.info(`[FLUX 4-bit install][git] ${d}`));
      git.on('close', code => {
        if (code === 0) resolve();
        else reject(new Error(`git clone failed with code ${code}`));
      });
    });
    logger.info(`[FLUX 4-bit install] Repo cloned.`);

    // Install requirements.txt
    const reqPath = path.join(targetDir, 'requirements.txt');
    if (fsSync.existsSync(reqPath)) {
      logger.info(`[FLUX 4-bit install] Installing requirements.txt in venv for ${pipelineName}`);
      await new Promise((resolve, reject) => {
        const pip = spawn(pythonExe, ['-m', 'pip', 'install', '-r', reqPath]);
        pip.stdout.on('data', d => logger.info(`[FLUX 4-bit install][pip] ${d}`));
        pip.stderr.on('data', d => logger.info(`[FLUX 4-bit install][pip] ${d}`));
        pip.on('close', code => {
          if (code === 0) resolve();
          else reject(new Error(`pip install failed with code ${code}`));
        });
      });
      logger.info(`[FLUX 4-bit install] requirements.txt installed.`);
    } else {
      logger.warn(`[FLUX 4-bit install] No requirements.txt found in repo!`);
    }

    // Send progress
    this._sendProgress(pipelineName, 'models', progressName, {
      status: 'Complete',
      message: `FLUX 4-bit model installed from GitHub repo`,
      progress: 100
    });
  }

  async _installModelDependencies(pipelineName, modelsToInstall) {
    logger.info(`Starting model installation for ${pipelineName} - ${modelsToInstall.length} models to install`);
    await this._ensureVenvExists(pipelineName);
    const pythonExe = this._getPythonExe(pipelineName);
    await fs.mkdir(HELPERS_DIR, { recursive: true });

    // Special handling for ImageGeneration: use enhanced model repair script
    if (pipelineName === 'ImageGeneration') {
      logger.info(`Using enhanced model repair script for ImageGeneration`);

      this._sendProgress(pipelineName, 'models', 'all', {
        status: 'Running',
        message: 'Starting enhanced model download and repair...',
        progress: 0
      });

      try {
        const modelsPath = path.join(MODELS_DIR, 'ImageGeneration');
        const modelRepairScript = path.join('utils', 'helpers', 'model_repair.py');

        // Ensure models directory exists
        await fs.mkdir(modelsPath, { recursive: true });

        // Use the enhanced model repair script (default: download all + repair + create configs)
        const args = [
          modelRepairScript,
          '--models-path', modelsPath
        ];

        logger.info(`Executing enhanced model repair script: ${pythonExe} ${args.join(' ')}`);

        await new Promise((resolve, reject) => {
          const env = { ...process.env };
          const scriptsDir = path.dirname(pythonExe);
          delete env.PYTHONHOME;
          delete env.PYTHONPATH;
          env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;

          const downloadProcess = spawn(pythonExe, args, {
            env,
            cwd: process.cwd()
          });

          let progressCount = 0;
          const totalModels = modelsToInstall.length;

          downloadProcess.stdout.on('data', (data) => {
            const output = data.toString();
            logger.info(`Model repair output: ${output.trim()}`);

            // Parse progress from output
            if (output.includes('--- Processing')) {
              progressCount++;
              const progress = Math.min((progressCount / totalModels) * 80, 80);
              const modelMatch = output.match(/--- Processing (.+) ---/);
              const modelName = modelMatch ? modelMatch[1] : 'model';
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Running',
                message: `Processing ${modelName}...`,
                progress: progress
              });
            } else if (output.includes('Downloading complete model')) {
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Running',
                message: output.trim(),
                progress: Math.min((progressCount / totalModels) * 80, 80)
              });
            } else if (output.includes('Successfully downloaded')) {
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Running',
                message: output.trim(),
                progress: Math.min((progressCount / totalModels) * 80, 80)
              });
            } else if (output.includes('CREATING MISSING CONFIG FILES')) {
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Running',
                message: 'Creating missing configuration files...',
                progress: 90
              });
            } else if (output.includes('Download Summary') || output.includes('Repair Summary')) {
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Running',
                message: 'Finalizing model installation...',
                progress: 95
              });
            }
          });

          downloadProcess.stderr.on('data', (data) => {
            const error = data.toString();
            logger.warn(`Model repair stderr: ${error.trim()}`);
          });

          downloadProcess.on('close', (code) => {
            if (code === 0) {
              logger.info(`Enhanced model repair completed successfully for ${pipelineName}`);
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Complete',
                message: 'All models downloaded and configured successfully!',
                progress: 100
              });
              resolve();
            } else {
              const errorMsg = `Enhanced model repair failed with exit code ${code}`;
              logger.error(errorMsg);
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Error',
                message: errorMsg,
                progress: 100
              });
              reject(new Error(errorMsg));
            }
          });

          downloadProcess.on('error', (error) => {
            logger.error(`Enhanced model repair process error:`, error);
            this._sendProgress(pipelineName, 'models', 'all', {
              status: 'Error',
              message: `Process error: ${error.message}`,
              progress: 100
            });
            reject(error);
          });
        });

        return; // Exit early for ImageGeneration
      } catch (error) {
        logger.error(`Enhanced model repair failed for ${pipelineName}:`, error);
        this._sendProgress(pipelineName, 'models', 'all', {
          status: 'Error',
          message: `Enhanced model repair failed: ${error.message}`,
          progress: 100
        });
        throw error;
      }
    }

    // Special handling for ImageUpscaling: install all models into models/upscaling/{model.name}/
    if (pipelineName === 'ImageUpscaling') {
      for (const model of modelsToInstall) {
        const progressName = model.name;
        logger.info(`Installing upscaler model ${model.name} for Upscayl compatibility`);
        // Target directory: models/upscaling/{model.name}/
        const upscaylDir = path.join(MODELS_DIR, 'upscaling', model.name);
        await fs.mkdir(upscaylDir, { recursive: true });
        // For each file in model.files, download to upscaylDir
        if (model.files && Array.isArray(model.files)) {
          for (const file of model.files) {
            // If the file is a known URL, download it; otherwise, try to fetch from repo_id
            let url = null;
            if (model.repo_id && model.repo_id.includes('SwinIR')) {
              url = 'https://github.com/JingyunLiang/SwinIR/releases/download/v0.0/' + file;
            } else if (model.repo_id && model.repo_id.includes('Real-ESRGAN')) {
              // Use correct version for each Real-ESRGAN model
              if (file.includes('anime')) {
                url = 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.2.4/' + file;
              } else {
                url = 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/' + file;
              }
            } else if (model.repo_id && model.repo_id.includes('Phhofm/models')) {
              // Handle Phhofm models
              if (file.includes('4xLSDIR')) {
                url = 'https://github.com/Phhofm/models/raw/main/4xLSDIR/' + file;
              }
            }
            // Add more model-specific URLs as needed
            if (url) {
              const targetPath = path.join(upscaylDir, file);
              logger.info(`Downloading upscaler model file: ${url} -> ${targetPath}`);
              await this._downloadFile(url, targetPath, null, (progress) => {
                this._sendProgress(pipelineName, 'models', progressName, {
                  status: 'Running',
                  message: `Downloading ${file}...`,
                  progress: progress
                });
              });
              this._sendProgress(pipelineName, 'models', progressName, {
                status: 'Complete',
                message: `${file} installed successfully`,
                progress: 100
              });
            } else {
              logger.warn(`No download URL for model file: ${file} (model: ${model.name})`);
            }
          }
        }
      }
      return;
    }

    // Verify huggingface_hub is installed before attempting model downloads
    logger.info(`Verifying huggingface_hub is available for ${pipelineName}...`);
    try {
      await new Promise((resolve, reject) => {
        const env = { ...process.env };
        const scriptsDir = path.dirname(pythonExe);
        delete env.PYTHONHOME;
        delete env.PYTHONPATH;
        env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;
        
        const checkHF = spawn(pythonExe, ['-c', 'import huggingface_hub; print("huggingface_hub available")'], { env });
        checkHF.on('close', (code) => {
          if (code === 0) {
            resolve();
          } else {
            reject(new Error('huggingface_hub not available'));
          }
        });
        checkHF.on('error', () => reject(new Error('Failed to check huggingface_hub')));
      });
    } catch (error) {
      logger.error(`huggingface_hub not available for ${pipelineName}:`, error);
      throw new Error(`Cannot download models: huggingface_hub not installed. Please install Python dependencies first.`);
    }

    for (const model of modelsToInstall) {
      const progressName = model.name;
      logger.info(`Starting download of model ${model.name} for ${pipelineName}`);
      
      this._sendProgress(pipelineName, 'models', progressName, {
        status: 'Running',
        message: `Downloading ${model.name}...`,
        progress: 0
      });

      try {
        const localPath = path.isAbsolute(model.local_path)
          ? model.local_path
          : path.join(MODELS_DIR, model.local_path);

        logger.info(`Model ${model.name} will be downloaded to: ${localPath}`);

        // Create directory if it doesn't exist
        await fs.mkdir(path.dirname(localPath), { recursive: true });
        await fs.mkdir(localPath, { recursive: true });

        // Clean up any existing script first
        let scriptPath = path.join(HELPERS_DIR, `download_${model.name.replace(/[^a-zA-Z0-9]/g, '_')}.py`);
        try {
          await fs.unlink(scriptPath);
          logger.info(`Removed old download script: ${scriptPath}`);
        } catch (error) {
          // Ignore if file doesn't exist
        }

        // Get the Hugging Face token from settings
        const hfToken = this.store.get('huggingface-token');
        logger.info(`Retrieved HF token from store: ${hfToken ? `[${hfToken.length} chars]` : 'null/undefined'}`);

        // Special handling for FLUX models - use diffusers to download complete model
        if (model.repo_id === 'black-forest-labs/FLUX.1-dev') {
          logger.info(`Using special FLUX installation for ${model.name}`);

          // Check if Hugging Face token is available
          if (!hfToken || hfToken.trim() === '') {
            const errorMsg = `FLUX model requires a valid Hugging Face token. Please set your token in Settings before installing FLUX models.`;
            logger.error(errorMsg);
            this._sendProgress(pipelineName, 'models', progressName, {
              status: 'Error',
              message: errorMsg,
              progress: 100
            });
            throw new Error(errorMsg);
          }

          try {
            await this._installFluxModel(model, localPath, pythonExe, pipelineName, progressName, hfToken);
          } catch (fluxError) {
            logger.error(`FLUX installation failed: ${fluxError.message}`);

            // If FLUX installation fails, we can continue with other models
            // but mark this specific model as failed
            this._sendProgress(pipelineName, 'models', progressName, {
              status: 'Error',
              message: `FLUX installation failed: ${fluxError.message}. You can try installing other models first.`,
              progress: 100
            });

            // Don't throw the error - continue with other models
            logger.warn(`Continuing with other model installations despite FLUX failure`);
          }
          continue; // Skip normal download process for FLUX
        }
        
        // Special handling for FLUX 4-bit model (official HighCWu repo)
        if (model.repo_id === 'HighCWu/FLUX.1-dev-4bit') {
          logger.info(`Using special FLUX 4-bit installation for ${model.name}`);
          await this._installFlux4BitModel(model, localPath, pythonExe, pipelineName, progressName, hfToken);
          continue; // Skip normal download process for FLUX 4-bit
        }

        // Special handling for upscaler models - these need specific weight files
        const upscalerModels = ['realesrgan-x4plus', 'realesrgan-x4plus-anime', 'swinir-m-x4', '4xlsdir', 'swinir-real-sr-x4'];
        if (upscalerModels.includes(model.name)) {
          logger.info(`Using special upscaler installation for ${model.name}`);
          await this._installUpscalerModel(model, localPath, pythonExe, pipelineName, progressName, hfToken);
          continue; // Skip normal download process for upscaler models
        }
        
        // Use the enhanced download script with proper progress tracking
        const enhancedScriptPath = path.join(__dirname, 'python_helpers', 'download_hf_model_with_progress.py');
        
        // Check if enhanced script exists, if not use fallback
        let useEnhancedScript = true;
        try {
          await fs.access(enhancedScriptPath);
          logger.info(`Using enhanced download script: ${enhancedScriptPath}`);
        } catch {
          useEnhancedScript = false;
          logger.warn(`Enhanced script not found, falling back to basic script`);
        }
        
        let args; // <-- Always define args in this scope
        if (useEnhancedScript) {
          // Use enhanced script with JSON progress reporting
          scriptPath = enhancedScriptPath;
          // Debug: Log the model object to see what properties it has
          logger.info(`Model object for ${model.name}:`, JSON.stringify(model, null, 2));
          
          // Fix argument order: repo_id, local_dir, model_name, token
          args = [scriptPath, model.repo_id, localPath, model.name];
          if (hfToken && hfToken.trim() !== '') {
            args.push(hfToken);
            logger.info(`Using HF token for ${model.name} (length: ${hfToken.length})`);
          } else {
            args.push(''); // Empty token
            logger.info(`No HF token provided for ${model.name} - using anonymous access`);
          }
          // Add include_patterns after token to maintain correct argument order
          if (model.files && Array.isArray(model.files) && model.files.length > 0) {
            args.push('--include_patterns');
            args.push(model.files.join(','));
          }
          // Final fix: ensure token is always the 4th argument for sdxl-turbo
          if (model.name === 'sdxl-turbo') {
            // Reconstruct args to ensure correct order: script, repo_id, local_path, model_name, token
            args = [scriptPath, model.repo_id, localPath, model.name, hfToken || ''];
          }
          logger.info(`Executing enhanced model download script for ${model.name}: ${pythonExe} ${args.join(' ')}`);
          logger.info(`Full args array for ${model.name}: [${args.map(arg => `"${arg}"`).join(', ')}]`);
        } else {
          // Fallback: Create a basic script
          // Escape the token properly for Python
          const escapedToken = hfToken ? hfToken.replace(/\\/g, '\\\\').replace(/"/g, '\\"') : '';
          logger.info(`Using fallback script for ${model.name}, token: ${hfToken ? `[${hfToken.length} chars]` : 'none'}`);

          const scriptContent = `
import os
import sys
import traceback
import json
import time

try:
    from huggingface_hub import snapshot_download, HfApi
    
    def report_progress(progress_type, message, progress=0, **kwargs):
        data = {
            'type': progress_type,
            'message': message,
            'progress': progress,
            'model': '${model.name}',
            'timestamp': time.time(),
            **kwargs
        }
        print(json.dumps(data), flush=True)
    
    def download_model():
        repo_id = "${model.repo_id}"
        local_dir = "${localPath.replace(/\\/g, '/')}"
        token = "${escapedToken}"

        # Only use token if it's not empty
        if not token or token.strip() == "":
            token = None
            report_progress('warning', 'No Hugging Face token provided - using anonymous access', 3)
        else:
            report_progress('info', f'Using Hugging Face token (length: {len(token)})', 3)
        
        report_progress('init', f'Initializing download for {repo_id}', 0)
        
        # Create directory if it doesn't exist
        os.makedirs(local_dir, exist_ok=True)
        
        # Check repository access
        try:
            report_progress('checking', 'Checking repository access...', 5)
            api = HfApi(token=token)
            repo_info = api.repo_info(repo_id=repo_id, token=token)
            report_progress('info', f'Repository found: {repo_id}', 10)
        except Exception as e:
            error_msg = str(e).lower()
            if ("gated" in error_msg or "access" in error_msg) and ${model.requires_auth ? 'True' : 'False'}:
                report_progress('error', "${model.auth_error_message || 'Repository requires authentication. Please check your Hugging Face token.'}", 0)
                sys.exit(1)
            else:
                report_progress('warning', f'Could not check repository info: {e}', 10)
        
        # Download model
        try:
            report_progress('downloading', 'Starting model download...', 25)
            snapshot_download(
                repo_id=repo_id,
                local_dir=local_dir${model.include_pattern ? `,
                include_patterns=["${model.include_pattern}"]` : ''}${model.exclude_pattern ? `,
                ignore_patterns=["${model.exclude_pattern}"]` : ''}${hfToken ? `,
                token=token` : ''},
                resume_download=True  # Enable resuming interrupted downloads
            )
            
            report_progress('verifying', 'Verifying download...', 90)
            
            # Verify download completed successfully
            downloaded_files = []
            total_size = 0
            for root, dirs, files in os.walk(local_dir):
                for file in files:
                    if not file.startswith('.'):
                        file_path = os.path.join(root, file)
                        downloaded_files.append(os.path.relpath(file_path, local_dir))
                        try:
                            total_size += os.path.getsize(file_path)
                        except:
                            pass
            
            if len(downloaded_files) == 0:
                report_progress('error', 'No files were downloaded', 90)
                sys.exit(1)
            
            size_mb = round(total_size / (1024 * 1024), 1)
            report_progress('complete', f'Successfully downloaded {repo_id}', 100, 
                files_downloaded=len(downloaded_files), total_size_mb=size_mb)
            
        except Exception as download_error:
            if ${model.requires_auth ? 'True' : 'False'} and ("gated" in str(download_error).lower() or "access" in str(download_error).lower()):
                report_progress('error', "${model.auth_error_message || 'Repository requires authentication. Please check your Hugging Face token.'}", 0)
            else:
                report_progress('error', f'Download failed: {download_error}', 0)
            traceback.print_exc()
            sys.exit(1)

    if __name__ == "__main__":
        download_model()
        
except Exception as e:
    report_progress('error', f'Script error: {e}', 0)
    traceback.print_exc()
    sys.exit(1)
`;

          await fs.writeFile(scriptPath, scriptContent);
          logger.info(`Created fallback download script at: ${scriptPath}`);
          args = [scriptPath]; // fallback script only needs the script path
        }

        // Run the download script with timeout
        await new Promise((resolve, reject) => {
          // Set a timeout for the download (60 minutes for large models)
          const downloadTimeout = setTimeout(() => {
            logger.error(`Model download for ${model.name} timed out after 60 minutes`);
            reject(new Error(`Model download timed out after 60 minutes`));
          }, 60 * 60 * 1000);
          
          const cleanup = () => {
            clearTimeout(downloadTimeout);
          };
          const env = { ...process.env };
          const scriptsDir = path.dirname(pythonExe);
          delete env.PYTHONHOME;
          delete env.PYTHONPATH;
          env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;
          env.HF_HUB_ENABLE_HF_TRANSFER = '1';  // Enable high-speed file transfer
          
          // Add pipeline-specific environment variables
          const pipeline = this.pipelines[pipelineName];
          if (pipeline?.environment) {
            Object.assign(env, pipeline.environment);
          }

          // Use the args that were already constructed above with proper token handling
          // The args variable is already set from the earlier construction
          
          const installer = spawn(pythonExe, args, {
            env,
            windowsHide: true
          });
          
          let stdout = '';
          let stderr = '';
          
          installer.stdout.on('data', (data) => {
            const text = data.toString();
            stdout += text;
            
            // Try to parse JSON progress updates
            const lines = text.split('\n').filter(line => line.trim());
            
            for (const line of lines) {
              try {
                const progressData = JSON.parse(line.trim());
                
                if (progressData.type && progressData.model === model.name) {
                  logger.info(`Model download progress (${model.name}): ${progressData.type} - ${progressData.message} (${progressData.progress}%)`);
                  
                  let status = 'Running';
                  let progress = progressData.progress || 0;
                  let message = progressData.message || `Downloading ${model.name}...`;
                  
                  // Map progress types to status
                  if (progressData.type === 'complete') {
                    status = 'Running'; // Keep as Running until we verify
                    progress = 100;
                    message = `${model.name} downloaded successfully`;
                  } else if (progressData.type === 'error') {
                    status = 'Error';
                    progress = 100;
                    message = progressData.message || `Failed to download ${model.name}`;
                  } else if (progressData.type === 'file_progress') {
                    // Use overall progress if available, otherwise file progress
                    progress = progressData.overall_progress || progressData.progress || 0;
                    
                    // Show download speed and size info
                    if (progressData.downloaded_mb && progressData.total_mb) {
                      message = `Downloading ${model.name}... ${progressData.downloaded_mb}MB / ${progressData.total_mb}MB`;
                    } else {
                      message = `Downloading ${model.name}... ${Math.round(progress)}%`;
                    }
                  } else if (['init', 'checking', 'scanning', 'downloading', 'verifying'].includes(progressData.type)) {
                    message = progressData.message || `Downloading ${model.name}...`;
                  }
                  
                  this._sendProgress(pipelineName, 'models', progressName, {
                    status,
                    message,
                    progress: Math.round(progress)
                  });
                }
              } catch (parseError) {
                // Not JSON, treat as regular log output
                if (line.trim()) {
                  logger.info(`Model download stdout (${model.name}): ${line.trim()}`);
                  
                  // Fallback progress detection for non-JSON output
                  const lowerLine = line.toLowerCase();
                  if (lowerLine.includes('downloading') || lowerLine.includes('fetching')) {
                    this._sendProgress(pipelineName, 'models', progressName, {
                      status: 'Running',
                      message: `Downloading ${model.name}...`,
                      progress: 25
                    });
                  } else if (lowerLine.includes('successfully downloaded') || lowerLine.includes('download completed')) {
                    this._sendProgress(pipelineName, 'models', progressName, {
                      status: 'Running',
                      message: `Finalizing ${model.name}...`,
                      progress: 90
                    });
                  }
                }
              }
            }
          });

          installer.stderr.on('data', (data) => {
            const text = data.toString();
            stderr += text;
            logger.info(`Model download stderr (${model.name}): ${text.trim()}`);
          });

          installer.on('error', (err) => {
            logger.error(`Failed to spawn model download process for ${model.name}:`, err);
            cleanup();
            reject(new Error(`Failed to download model ${model.name}: ${err.message}`));
          });

          installer.on('close', async (code) => {
            cleanup();
            logger.info(`Model download script for ${model.name} completed with code ${code}`);
            
            if (code !== 0) {
              logger.error(`Model download failed for ${model.name}. Stdout: ${stdout}`);
              logger.error(`Model download failed for ${model.name}. Stderr: ${stderr}`);
              
              // Clean up empty directory on failure
              try {
                const isEmpty = (await fs.readdir(localPath)).length === 0;
                if (isEmpty) {
                  await fs.rmdir(localPath);
                  logger.info(`Cleaned up empty model directory: ${localPath}`);
                }
              } catch (cleanupError) {
                logger.warn(`Could not clean up model directory: ${cleanupError.message}`);
              }
              
              reject(new Error(`Model download failed with code ${code}. Check logs for details.`));
            } else {
              // Verify the download actually completed
              try {
                const files = await fs.readdir(localPath);
                if (files.length === 0) {
                  logger.error(`Model directory is empty after download: ${localPath}`);
                  reject(new Error(`Model download appeared to succeed but no files were downloaded`));
                } else {
                  logger.info(`Successfully downloaded model ${model.name}, ${files.length} files/folders created`);
                  // Always send a final Complete event for all models (fix for Trellis-Large progress bar)
                  this._sendProgress(pipelineName, 'models', progressName, {
                    status: 'Complete',
                    message: `${model.name} downloaded successfully`,
                    progress: 100
                  });
                  resolve();
                }
              } catch (verifyError) {
                logger.error(`Could not verify model download: ${verifyError.message}`);
                reject(new Error(`Could not verify model download: ${verifyError.message}`));
              }
            }
          });
        });

      } catch (error) {
        logger.error(`Failed to download model ${model.name}:`, error);
        this._sendProgress(pipelineName, 'models', progressName, {
          status: 'Error',
          message: `Failed to download ${model.name}: ${error.message}`,
          progress: 100
        });
        throw error;
      }
    }
  }

  _downloadFile(url, dest, token, onProgress) {
    return new Promise((resolve, reject) => {
      const urlObject = new URL(url);
      const options = {
        hostname: urlObject.hostname,
        port: urlObject.port,
        path: urlObject.pathname + urlObject.search,
        headers: {}
      };

      if (token && options.hostname.includes('huggingface.co')) {
        logger.info('Attaching Hugging Face token to download request.');
        options.headers['Authorization'] = `Bearer ${token}`;
      }

      const request = https.get(options, (response) => {
        if (response.statusCode >= 300 && response.statusCode < 400 && response.headers.location) {
            // Follow redirect, passing the token along
            logger.info(`Redirected to ${response.headers.location}`);
            this._downloadFile(response.headers.location, dest, token, onProgress)
                .then(resolve)
                .catch(reject);
            return;
        }
        
        if (response.statusCode !== 200) {
          return reject(new Error(`Failed to get '${url}' (${response.statusCode})`));
        }

        const file = fsSync.createWriteStream(dest);
        const total = parseInt(response.headers['content-length'], 10);
        let downloaded = 0;

        response.on('data', (chunk) => {
          downloaded += chunk.length;
          const progress = !isNaN(total) ? (downloaded / total) * 100 : -1;
          onProgress({ progress, downloaded, total });
        });

        response.pipe(file);

        file.on('finish', () => {
          file.close(resolve);
        });
        
        file.on('error', (err) => {
             fs.unlink(dest).catch(()=>{/*ignore*/}).finally(()=>reject(err));
        });
      });

      request.on('error', (err) => {
        fs.unlink(dest).catch(()=>{/*ignore*/}).finally(()=>reject(err));
      });
    });
  }

  _sendProgress(pipelineName, component, name, data) {
    if (this.mainWindow) {
      this.mainWindow.webContents.send('installation-progress', {
        pipeline: pipelineName,
        component, // 'python' or 'model'
        name,      // specific package/model name or 'all'
        ...data,   // { status, message, progress }
    });
  }
}

  // --- Private Helpers ---
  _getPythonPath(pipelineName) {
    // Microsoft_TRELLIS uses the actual Trellis server's Python installation
    if (pipelineName === 'trellis-stable-projectorz-101' || pipelineName.toLowerCase().includes('trellis')) {
      return path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'trellis-stable-projectorz-101', 'tools', 'python', 'python.exe');
    }
    // Hunyuan3D-2 uses the actual Hunyuan server's Python installation
    if (pipelineName === 'hunyuan2-spz-101' || pipelineName.toLowerCase().includes('hunyuan')) {
      return path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'hunyuan2-spz-101', 'tools', 'python', 'python.exe');
    }
    // Each pipeline gets its own Python installation
    return path.join(PIPELINES_DIR, pipelineName, 'python', 'python.exe');
  }

  _getVenvPath(pipelineName) {
    // Microsoft_TRELLIS uses the actual Trellis server's venv path
    if (pipelineName === 'trellis-stable-projectorz-101' || pipelineName.toLowerCase().includes('trellis')) {
      return path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'trellis-stable-projectorz-101', 'code', 'venv');
    }
    // Hunyuan3D-2 uses the actual Hunyuan server's venv path
    if (pipelineName === 'hunyuan2-spz-101' || pipelineName.toLowerCase().includes('hunyuan')) {
      return path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'hunyuan2-spz-101', 'code', 'venv');
    }
    return path.join(PIPELINES_DIR, pipelineName, 'env');
  }

  _getPythonExe(pipelineName) {
    const venvPath = this._getVenvPath(pipelineName);
    return path.join(venvPath, 'Scripts', 'python.exe');
  }

  async _runCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
      const proc = spawn(command, args, {
        windowsHide: true,
        ...options
      });
      let stderr = '';

      proc.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      proc.on('error', (err) => {
        reject(new Error(`Failed to run command ${command}: ${err.message}`));
      });

      proc.on('close', (code) => {
        if (code !== 0) {
          reject(new Error(`Command ${command} failed with code ${code}. Stderr: ${stderr}`));
        } else {
          resolve();
        }
      });
    });
  }

  async _ensurePipelineExists(pipelineName) {
    const pipeline = this.pipelines[pipelineName];
    if (!pipeline) {
      // Check if pipeline exists in embedded configs
      const embeddedConfig = PIPELINE_CONFIGS[pipelineName];
      if (!embeddedConfig) {
        throw new Error(`Pipeline ${pipelineName} not found in embedded configurations`);
      }
      
      // Load from embedded config
      this.pipelines[pipelineName] = embeddedConfig;
      this.dependencyStatus[pipelineName] = {
        name: pipelineName,
        python: { installed: false, details: {} },
        models: { installed: false, details: {} }
      };
      
      // Ensure directory exists (skip for system package pipelines)
      const systemPackagePipelines = ['trellis-stable-projectorz-101', 'hunyuan2-spz-101'];
      if (!systemPackagePipelines.includes(pipelineName)) {
        await this._ensurePipelineDirectoryExists(pipelineName);
      }
      
      logger.info(`Loaded pipeline ${pipelineName} from embedded configuration`);
    }
  }

  async _initializeHelpers() {
    try {
      await fs.mkdir(HELPERS_DIR, { recursive: true });
    } catch (error) {
      logger.error('Failed to create helpers directory:', error);
    }
  }





  async _validateHunyuanInstallation() {
    const hunyuanServerPath = path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'hunyuan2-spz-101');
    const venvPath = path.join(hunyuanServerPath, 'code', 'venv');
    const pythonExe = path.join(venvPath, 'Scripts', 'python.exe');
    const envFolderPath = path.join(hunyuanServerPath, 'ENV');

    logger.info(`Hunyuan3D-2 validation paths:`);
    logger.info(`  Server path: ${hunyuanServerPath}`);
    logger.info(`  Venv path: ${venvPath}`);
    logger.info(`  Python exe: ${pythonExe}`);
    logger.info(`  ENV folder: ${envFolderPath}`);

    try {
      // Check if Python executable exists in venv (primary indicator of successful installation)
      logger.info(`Checking if Python executable exists: ${pythonExe}`);
      await fs.access(pythonExe);
      logger.info('Hunyuan3D-2 Python executable found');

      // Check if ENV folder exists (created by run-stableprojectorz-full-multiview.bat after successful installation)
      try {
        await fs.access(envFolderPath);
        logger.info('Hunyuan3D-2 ENV folder found');
      } catch {
        // ENV folder is optional - venv existence is the main indicator
        logger.info('Hunyuan3D-2 ENV folder not found, but venv exists');
      }

      // Additional validation: check if key packages are installed
      const sitePackagesPath = path.join(venvPath, 'Lib', 'site-packages');
      const requiredPackages = ['torch', 'transformers', 'diffusers'];

      let packagesFound = 0;
      for (const pkg of requiredPackages) {
        const packagePaths = [
          path.join(sitePackagesPath, pkg),
          path.join(sitePackagesPath, pkg.replace('-', '_'))
        ];

        for (const pkgPath of packagePaths) {
          try {
            await fs.access(pkgPath);
            packagesFound++;
            break;
          } catch {}
        }
      }

      // Consider installation valid if ENV folder exists and at least some key packages are found
      const isValid = packagesFound >= 2; // At least 2 out of 3 key packages
      if (isValid) {
        logger.info(`Hunyuan3D-2 System Package validation successful (${packagesFound}/${requiredPackages.length} key packages found)`);
      } else {
        logger.warn(`Hunyuan3D-2 System Package validation failed (only ${packagesFound}/${requiredPackages.length} key packages found)`);
      }

      return isValid;
    } catch (error) {
      // If Python executable doesn't exist, installation is not complete
      if (error.code === 'ENOENT' && error.path && error.path.includes('python.exe')) {
        logger.info('Hunyuan3D-2 Python executable not found - installation not complete');
        return false; // Installation needed
      }

      logger.error('Hunyuan3D-2 System Package validation failed:', error);
      return false;
    }
  }

  async _validateTrellisInstallation() {
    const trellisServerPath = path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'trellis-stable-projectorz-101');
    const venvPath = path.join(trellisServerPath, 'code', 'venv');
    const pythonExe = path.join(venvPath, 'Scripts', 'python.exe');
    const envFolderPath = path.join(trellisServerPath, 'ENV');

    try {
      // Check if Python executable exists in venv (primary indicator of successful installation)
      await fs.access(pythonExe);
      logger.info('Microsoft_TRELLIS Python executable found');

      // Check if ENV folder exists (created by run-fp16.bat after successful installation)
      try {
        await fs.access(envFolderPath);
        logger.info('Microsoft_TRELLIS ENV folder found');
      } catch {
        // ENV folder is optional - venv existence is the main indicator
        logger.info('Microsoft_TRELLIS ENV folder not found, but venv exists');
      }

      // Additional validation: check if key packages are installed
      const sitePackagesPath = path.join(venvPath, 'Lib', 'site-packages');
      const requiredPackages = ['torch', 'transformers', 'huggingface_hub'];

      let packagesFound = 0;
      for (const pkg of requiredPackages) {
        const packagePaths = [
          path.join(sitePackagesPath, pkg),
          path.join(sitePackagesPath, pkg.replace('-', '_'))
        ];

        for (const pkgPath of packagePaths) {
          try {
            await fs.access(pkgPath);
            packagesFound++;
            break;
          } catch {}
        }
      }

      // Consider installation valid if at least some key packages are found
      const isValid = packagesFound >= 2; // At least 2 out of 3 key packages
      if (isValid) {
        logger.info(`Microsoft_TRELLIS System Package validation successful (${packagesFound}/${requiredPackages.length} key packages found)`);
      } else {
        logger.warn(`Microsoft_TRELLIS System Package validation failed (only ${packagesFound}/${requiredPackages.length} key packages found)`);
      }

      return isValid;
    } catch (error) {
      // If Python executable doesn't exist, installation is not complete
      if (error.code === 'ENOENT' && error.path && error.path.includes('python.exe')) {
        logger.info('Microsoft_TRELLIS Python executable not found - installation not complete');
        return false; // Installation needed
      }

      logger.error('Microsoft_TRELLIS System Package validation failed:', error);
      return false;
    }
  }

  // --- New Microsoft_TRELLIS Module Installation ---
  async _installTrellisModule(pipelineName, component, name) {
    const progressName = name || 'all';
    const trellisServerPath = path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'trellis-stable-projectorz-101');
    const runBatPath = path.join(trellisServerPath, 'run.bat');

    // Only handle python component (system package), models are handled by run.bat
    if (component === 'models') {
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Complete',
        message: 'Models are automatically managed by Trellis Server System Package',
        progress: 100,
      });
      return;
    }

    // Check if already installed
    const isAlreadyInstalled = await this._validateTrellisInstallation();
    if (isAlreadyInstalled) {
      logger.info('Trellis Server System Package already installed');

      // Send a brief "checking" status first
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: 'Checking Trellis installation...',
        progress: 50,
      });

      // Wait a moment then send completion
      setTimeout(() => {
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Complete',
          message: 'Trellis Server System Package already installed',
          progress: 100,
        });
      }, 1000);

      return;
    }

    // Initialize progress
    this._sendProgress(pipelineName, component, progressName, {
      status: 'Running',
      message: 'Installing Trellis Server System Package...',
      progress: 0,
    });

    // Clean up any corrupted installation files before starting
    const trellisCodePath = path.join(trellisServerPath, 'code');
    const initDoneFile = path.join(trellisCodePath, 'trellis_init_done.txt');
    const venvPath = path.join(trellisCodePath, 'venv');

    try {
      // Remove init done file if it exists (forces reinstallation)
      if (await fs.access(initDoneFile).then(() => true).catch(() => false)) {
        await fs.unlink(initDoneFile);
        logger.info('Removed existing trellis_init_done.txt to force clean installation');
      }

      // Remove venv directory if it exists (forces clean venv creation)
      if (await fs.access(venvPath).then(() => true).catch(() => false)) {
        await fs.rm(venvPath, { recursive: true, force: true });
        logger.info('Removed existing venv directory to force clean installation');
      }

      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: 'Cleaned up previous installation, starting fresh install...',
        progress: 5,
      });
    } catch (error) {
      logger.warn('Error during cleanup (continuing anyway):', error);
    }

    // Check if run.bat exists
    try {
      await fs.access(runBatPath);
    } catch (error) {
      logger.error(`Trellis run.bat not found at: ${runBatPath}`);
      throw new Error(`Trellis installation file not found: ${runBatPath}`);
    }

    return new Promise((resolve, reject) => {
      const proc = spawn('cmd.exe', ['/c', runBatPath], {
        cwd: trellisServerPath,
        windowsHide: true,
        stdio: ['ignore', 'pipe', 'pipe']
      });

      let progress = 0;
      let stderr = '';

      proc.stdout.on('data', (data) => {
        const text = data.toString();

        // Update progress based on output patterns for system package installation
        if (text.includes('Creating virtual environment') || text.includes('venv')) progress = Math.max(progress, 15);
        if (text.includes('Installing dependencies') || text.includes('pip install')) progress = Math.max(progress, 35);
        if (text.includes('Installing PyTorch') || text.includes('torch')) progress = Math.max(progress, 55);
        if (text.includes('Installing requirements') || text.includes('requirements.txt')) progress = Math.max(progress, 75);
        if (text.includes('Downloading models') || text.includes('huggingface')) progress = Math.max(progress, 85);
        if (text.includes('Installation completed') || text.includes('Server starting')) progress = Math.max(progress, 95);
        if (text.includes('Server is running') || text.includes('Gradio app running') || text.includes('Running on')) progress = 100;

        this._sendProgress(pipelineName, component, progressName, {
          status: 'Running',
          message: text.trim(),
          progress,
        });
      });

      proc.stderr.on('data', (data) => {
        stderr += data.toString();
        // Don't send stderr as error status during installation - it's often just warnings
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Running',
          message: data.toString().trim(),
          progress,
        });
      });

      proc.on('close', (code) => {
        // Check if environment folder exists to verify successful installation
        const venvPath = path.join(trellisServerPath, 'code', 'venv');
        const envExists = require('fs').existsSync(venvPath);

        if (code === 0 || progress >= 90 || envExists) {
          this._sendProgress(pipelineName, component, progressName, {
            status: 'Complete',
            message: 'Trellis Server System Package installed successfully',
            progress: 100,
          });
          resolve();
        } else {
          const errorMsg = `Trellis Server System Package installation failed with code ${code}. Check if dependencies are properly installed.`;
          logger.error(errorMsg);
          this._sendProgress(pipelineName, component, progressName, {
            status: 'Error',
            message: errorMsg,
            progress: 0,
          });
          reject(new Error(errorMsg));
        }
      });

      proc.on('error', (error) => {
        const errorMsg = `Failed to start Trellis Module installation: ${error.message}`;
        logger.error(errorMsg);
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Error',
          message: errorMsg,
          progress: 0,
        });
        reject(new Error(errorMsg));
      });
    });
  }

  // --- New Hunyuan3D-2 Module Installation ---
  async _installHunyuanModule(pipelineName, component, name) {
    const progressName = name || 'all';
    const hunyuanServerPath = path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'hunyuan2-spz-101', 'run-projectorz_(faster)');
    const runBatPath = path.join(hunyuanServerPath, 'run-stableprojectorz-full-multiview.bat');

    // Only handle python component (system package), models are handled by run-stableprojectorz-full-multiview.bat
    if (component === 'models') {
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Complete',
        message: 'Models are automatically managed by Hunyuan3D-2 Server System Package',
        progress: 100,
      });
      return;
    }

    // Check if already installed
    const isAlreadyInstalled = await this._validateHunyuanInstallation();
    if (isAlreadyInstalled) {
      logger.info('Hunyuan3D-2 Server System Package already installed');

      // Send a brief "checking" status first
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: 'Checking Hunyuan3D-2 installation...',
        progress: 50,
      });

      // Wait a moment then send completion
      setTimeout(() => {
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Complete',
          message: 'Hunyuan3D-2 Server System Package already installed',
          progress: 100,
        });
      }, 1000);

      return;
    }

    // Initialize progress
    this._sendProgress(pipelineName, component, progressName, {
      status: 'Running',
      message: 'Installing Hunyuan3D-2 Server System Package...',
      progress: 0,
    });

    // Clean up any corrupted installation files before starting
    const hunyuanCodePath = path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'hunyuan2-spz-101', 'code');
    const initDoneFile = path.join(hunyuanCodePath, 'hunyuan_init_done.txt');
    const venvPath = path.join(hunyuanCodePath, 'venv');

    try {
      // Remove init done file if it exists (forces reinstallation)
      if (await fs.access(initDoneFile).then(() => true).catch(() => false)) {
        await fs.unlink(initDoneFile);
        logger.info('Removed existing hunyuan_init_done.txt to force clean installation');
      }

      // Remove venv directory if it exists (forces clean venv creation)
      if (await fs.access(venvPath).then(() => true).catch(() => false)) {
        await fs.rm(venvPath, { recursive: true, force: true });
        logger.info('Removed existing venv directory to force clean installation');
      }

      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: 'Cleaned up previous installation, starting fresh install...',
        progress: 5,
      });
    } catch (error) {
      logger.warn('Error during cleanup (continuing anyway):', error);
    }

    // Check if run-stableprojectorz-full-multiview.bat exists
    try {
      await fs.access(runBatPath);
    } catch (error) {
      logger.error(`Hunyuan3D-2 batch file not found at: ${runBatPath}`);
      throw new Error(`Hunyuan3D-2 installation file not found: ${runBatPath}`);
    }

    return new Promise((resolve, reject) => {
      const proc = spawn('cmd.exe', ['/c', runBatPath], {
        cwd: hunyuanServerPath,
        windowsHide: true,
        stdio: ['ignore', 'pipe', 'pipe']
      });

      let progress = 0;
      let stderr = '';

      proc.stdout.on('data', (data) => {
        const text = data.toString();

        // Update progress based on output patterns for system package installation
        if (text.includes('Creating virtual environment') || text.includes('venv')) progress = Math.max(progress, 12);
        if (text.includes('Installing dependencies') || text.includes('pip install')) progress = Math.max(progress, 30);
        if (text.includes('Installing PyTorch') || text.includes('torch')) progress = Math.max(progress, 50);
        if (text.includes('Installing requirements') || text.includes('requirements.txt')) progress = Math.max(progress, 70);
        if (text.includes('Downloading models') || text.includes('huggingface')) progress = Math.max(progress, 85);
        if (text.includes('Installation completed') || text.includes('Server starting')) progress = Math.max(progress, 95);
        if (text.includes('Server is running') || text.includes('Gradio app running') || text.includes('Running on')) progress = 100;

        this._sendProgress(pipelineName, component, progressName, {
          status: 'Running',
          message: text.trim(),
          progress,
        });
      });

      proc.stderr.on('data', (data) => {
        stderr += data.toString();
        // Don't send stderr as error status during installation - it's often just warnings
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Running',
          message: data.toString().trim(),
          progress,
        });
      });

      proc.on('close', (code) => {
        // Check if environment folder exists to verify successful installation
        const venvPath = path.join(path.dirname(hunyuanServerPath), 'venv');
        const envExists = require('fs').existsSync(venvPath);

        if (code === 0 || progress >= 90 || envExists) {
          this._sendProgress(pipelineName, component, progressName, {
            status: 'Complete',
            message: 'Hunyuan3D-2 Server System Package installed successfully',
            progress: 100,
          });
          resolve();
        } else {
          const errorMsg = `Hunyuan3D-2 Server System Package installation failed with code ${code}. Check if dependencies are properly installed.`;
          logger.error(errorMsg);
          this._sendProgress(pipelineName, component, progressName, {
            status: 'Error',
            message: errorMsg,
            progress: 0,
          });
          reject(new Error(errorMsg));
        }
      });

      proc.on('error', (error) => {
        const errorMsg = `Failed to start Hunyuan3D-2 Module installation: ${error.message}`;
        logger.error(errorMsg);
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Error',
          message: errorMsg,
          progress: 0,
        });
        reject(new Error(errorMsg));
      });
    });
  }
}

// Export the class directly
module.exports = DependencyManager;