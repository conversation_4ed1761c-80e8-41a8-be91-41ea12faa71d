{"_class_name": "StableDiffusionXLPipeline", "_diffusers_version": "0.21.0", "_name_or_path": "stabilityai/sdxl-turbo", "requires_safety_checker": false, "scheduler": ["diffusers", "EulerAncestralDiscreteScheduler"], "text_encoder": ["transformers", "CLIPTextModel"], "text_encoder_2": ["transformers", "CLIPTextModelWithProjection"], "tokenizer": ["transformers", "CLIPTokenizer"], "tokenizer_2": ["transformers", "CLIPTokenizer"], "unet": ["diffusers", "UNet2DConditionModel"], "vae": ["diffusers", "AutoencoderKL"]}