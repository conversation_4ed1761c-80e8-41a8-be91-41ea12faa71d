{"_class_name": "StableDiffusionXLPipeline", "_diffusers_version": "0.24.0.dev0", "feature_extractor": [null, null], "force_zeros_for_empty_prompt": true, "image_encoder": [null, null], "scheduler": ["diffusers", "EulerAncestralDiscreteScheduler"], "text_encoder": ["transformers", "CLIPTextModel"], "text_encoder_2": ["transformers", "CLIPTextModelWithProjection"], "tokenizer": ["transformers", "CLIPTokenizer"], "tokenizer_2": ["transformers", "CLIPTokenizer"], "unet": ["diffusers", "UNet2DConditionModel"], "vae": ["diffusers", "AutoencoderKL"]}