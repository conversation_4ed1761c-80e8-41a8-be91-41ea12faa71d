"""
Unified Preview System for Image Generation
Handles preview generation for all models with consistent quality and fallback mechanisms.
"""

import torch
import numpy as np
from PIL import Image, ImageEnhance
import io
import base64
from typing import Optional, Union

class UnifiedPreviewSystem:
    """Unified preview system that works with all image generation models."""
    
    def __init__(self, preview_quality: str = "high"):
        self.preview_quality = preview_quality
        self.taesd_decoder = None  # Will be loaded if available
        
    def set_preview_quality(self, quality: str):
        """Set preview quality: 'fast' or 'high'."""
        self.preview_quality = quality
        
    def load_taesd_decoder(self, device: str = "cuda"):
        """Load TAESD decoder for fast previews if available."""
        try:
            from diffusers import AutoencoderTiny
            self.taesd_decoder = AutoencoderTiny.from_pretrained(
                "madebyollin/taesd", 
                torch_dtype=torch.float16
            ).to(device)
            print("TAESD decoder loaded for fast previews")
        except Exception as e:
            print(f"Could not load TAESD decoder: {e}")
            self.taesd_decoder = None
            
    def get_scaling_factor(self, model_name: str, pipeline=None) -> float:
        """Get the correct scaling factor for the model."""
        # Model-specific scaling factors
        scaling_factors = {
            "flux-dev": 0.3611,
            "flux-dev-quantized": 0.3611,
            "stable-diffusion-xl-base-1.0": 0.13025,
            "sdxl-turbo": 0.13025,
            "stable-diffusion-v1-5": 0.18215,
            "stable-diffusion-2-1": 0.18215
        }
        
        if model_name in scaling_factors:
            return scaling_factors[model_name]
            
        # Try to get from pipeline VAE config
        if pipeline and hasattr(pipeline, 'vae') and pipeline.vae:
            return getattr(pipeline.vae.config, "scaling_factor", 0.18215)
            
        # Default fallback
        return 0.18215
        
    def validate_latents(self, latents) -> bool:
        """Validate latents input."""
        if latents is None:
            print("[Preview] Latents is None")
            return False
            
        if not hasattr(latents, 'shape'):
            print("[Preview] Latents has no shape attribute")
            return False
            
        if len(latents.shape) < 3:
            print(f"[Preview] Latents shape {latents.shape} is too small")
            return False
            
        return True
        
    def decode_latents_with_vae(self, latents, pipeline, model_name: str) -> Optional[str]:
        """Decode latents using the pipeline's VAE."""
        try:
            if not pipeline or not hasattr(pipeline, 'vae') or pipeline.vae is None:
                print("[Preview VAE] No VAE available in pipeline")
                return None
                
            print(f"[Preview VAE] Starting decode with latents shape: {latents.shape}")
            
            with torch.no_grad():
                vae = pipeline.vae
                latent_tensor = latents if latents.dim() == 4 else latents.unsqueeze(0)
                
                # Get correct scaling factor
                scaling_factor = self.get_scaling_factor(model_name, pipeline)
                print(f"[Preview VAE] Using scaling factor: {scaling_factor}")
                
                # Prepare latents for VAE
                vae_dtype = next(vae.parameters()).dtype
                vae_device = next(vae.parameters()).device
                print(f"[Preview VAE] VAE device: {vae_device}, dtype: {vae_dtype}")
                
                # Scale and move latents
                scaled_latents = (latent_tensor / scaling_factor).to(dtype=vae_dtype, device=vae_device)
                print(f"[Preview VAE] Scaled latents shape: {scaled_latents.shape}")
                
                # Decode
                decoded = vae.decode(scaled_latents).sample[0].cpu()
                print(f"[Preview VAE] Decoded shape: {decoded.shape}")
                
                # Normalize to [0,1]
                decoded = torch.clamp((decoded + 1) / 2, 0, 1)
                
                # Convert to PIL Image
                np_img = (decoded.permute(1, 2, 0).numpy() * 255).astype('uint8')
                pil_image = Image.fromarray(np_img)
                print(f"[Preview VAE] PIL image size: {pil_image.size}")
                
                return self._encode_image_to_base64(pil_image)
                
        except Exception as e:
            print(f"[Preview VAE] Failed to decode with VAE: {e}")
            import traceback
            traceback.print_exc()
            return None
            
    def decode_latents_with_taesd(self, latents) -> Optional[str]:
        """Decode latents using TAESD for fast preview."""
        if self.taesd_decoder is None:
            return None
            
        try:
            print("[Preview TAESD] Using TAESD decoder")
            
            with torch.no_grad():
                latent_tensor = latents if latents.dim() == 4 else latents.unsqueeze(0)
                decoded = self.taesd_decoder(latent_tensor)[0].cpu()
                decoded = torch.clamp((decoded + 1) / 2, 0, 1)
                
                np_img = (decoded.permute(1, 2, 0).numpy() * 255).astype('uint8')
                pil_image = Image.fromarray(np_img)
                
                return self._encode_image_to_base64(pil_image)
                
        except Exception as e:
            print(f"[Preview TAESD] Failed to decode with TAESD: {e}")
            return None
            
    def create_latent_visualization(self, latents, model_name: str) -> Optional[str]:
        """Create a visualization from raw latents."""
        try:
            print(f"[Preview Latent] Creating visualization for {model_name}")
            print(f"[Preview Latent] Input latents shape: {latents.shape}")
            
            with torch.no_grad():
                # Handle different latent shapes
                if latents.dim() == 4:  # [batch, channels, height, width]
                    latent_batch = latents[0].cpu().float()
                elif latents.dim() == 3:  # [channels, height, width]
                    latent_batch = latents.cpu().float()
                else:
                    print(f"[Preview Latent] Unexpected latent dimensions: {latents.dim()}")
                    return None
                    
                print(f"[Preview Latent] Processing latents shape: {latent_batch.shape}")
                
                # Model-specific channel handling
                if model_name.startswith("flux"):
                    # FLUX has 16 channels, use weighted combination
                    if latent_batch.shape[0] >= 16:
                        weights = torch.tensor([0.3, 0.25, 0.2, 0.15, 0.1] + [0.05] * 11)
                        weights = weights[:latent_batch.shape[0]].view(-1, 1, 1)
                        latent_image = (latent_batch * weights).sum(dim=0)
                    else:
                        latent_image = latent_batch.mean(dim=0)
                elif latent_batch.shape[0] >= 4:
                    # Standard diffusion models (4+ channels)
                    weights = torch.tensor([0.4, 0.3, 0.2, 0.1] + [0.0] * (latent_batch.shape[0] - 4))
                    weights = weights[:latent_batch.shape[0]].view(-1, 1, 1)
                    latent_image = (latent_batch * weights).sum(dim=0)
                else:
                    # Fallback for models with fewer channels
                    latent_image = latent_batch.mean(dim=0)
                    
                print(f"[Preview Latent] Combined latent image shape: {latent_image.shape}")
                
                # Robust normalization using percentiles
                latent_flat = latent_image.flatten()
                p5, p95 = torch.quantile(latent_flat, torch.tensor([0.05, 0.95]))
                latent_image = torch.clamp(latent_image, p5, p95)
                latent_image = (latent_image - p5) / (p95 - p5 + 1e-8)
                
                # Apply gamma correction for better contrast
                latent_image = torch.pow(latent_image, 0.7)
                
                # Create color-mapped visualization
                gray_array = (latent_image * 255).byte().numpy()
                height, width = gray_array.shape
                rgb_array = np.zeros((height, width, 3), dtype=np.uint8)
                
                # Blue-to-yellow color mapping
                normalized = gray_array.astype(np.float32) / 255.0
                rgb_array[:, :, 0] = (normalized * 255).astype(np.uint8)  # Red
                rgb_array[:, :, 1] = (normalized * 255).astype(np.uint8)  # Green
                rgb_array[:, :, 2] = (255 - normalized * 128).astype(np.uint8)  # Blue
                
                # Create PIL image
                pil_image = Image.fromarray(rgb_array)
                print(f"[Preview Latent] Created PIL image size: {pil_image.size}")
                
                # Enhance contrast
                enhancer = ImageEnhance.Contrast(pil_image)
                pil_image = enhancer.enhance(1.3)
                
                return self._encode_image_to_base64(pil_image)
                
        except Exception as e:
            print(f"[Preview Latent] Failed to create latent visualization: {e}")
            import traceback
            traceback.print_exc()
            return None
            
    def _encode_image_to_base64(self, pil_image: Image.Image, max_size: int = 512) -> str:
        """Encode PIL image to base64 with optional resizing."""
        try:
            # Resize if needed
            w, h = pil_image.size
            scale = min(max_size / w, max_size / h, 1.0)
            if scale < 1.0:
                new_size = (int(w * scale), int(h * scale))
                pil_image = pil_image.resize(new_size, Image.Resampling.LANCZOS)
                print(f"[Preview Encode] Resized to: {pil_image.size}")
                
            # Encode as JPEG
            buffer = io.BytesIO()
            pil_image.save(buffer, format='JPEG', quality=90)
            buffer.seek(0)
            base64_string = base64.b64encode(buffer.getvalue()).decode('utf-8')
            print(f"[Preview Encode] Encoded to base64, length: {len(base64_string)}")
            return base64_string
            
        except Exception as e:
            print(f"[Preview Encode] Failed to encode image: {e}")
            return None
            
    def generate_preview(self, latents, pipeline=None, model_name: str = "unknown") -> Optional[str]:
        """Generate preview using the best available method."""
        print(f"[Preview] Generating preview for {model_name}, quality: {self.preview_quality}")
        
        # Validate input
        if not self.validate_latents(latents):
            return None
            
        # Try high-quality decode first if requested
        if self.preview_quality == "high":
            # Try VAE decode
            result = self.decode_latents_with_vae(latents, pipeline, model_name)
            if result:
                print(f"[Preview] High-quality VAE decode successful")
                return result
                
            # Try TAESD decode
            result = self.decode_latents_with_taesd(latents)
            if result:
                print(f"[Preview] TAESD decode successful")
                return result
                
        # Fallback to latent visualization
        result = self.create_latent_visualization(latents, model_name)
        if result:
            print(f"[Preview] Latent visualization successful")
            return result
            
        print("[Preview] All preview methods failed")
        return None
