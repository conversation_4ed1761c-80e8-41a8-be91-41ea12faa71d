{"name": "Hunyuan3D-2", "description": "Tencent Hunyuan3D-2.0 - Advanced 3D generation with multi-view support and high-quality texture generation", "features": ["Single image to 3D", "Multi-view to 3D", "Text to 3D", "High-quality mesh generation", "Advanced texture generation", "GLB export", "Multiple quality presets", "Octree-based generation"], "dependencies": {"system": [{"name": "Hunyuan3D-2 Server", "description": "Hunyuan3D-2 API server with integrated virtual environment and models", "check_command": "run-stableprojectorz-full-multiview.bat", "install_path": "pipelines/3DPipelines/gen3d/hunyuan2-spz-101/run-projectorz_(faster)/run-stableprojectorz-full-multiview.bat", "required": true, "system_package": true}]}, "api": {"host": "127.0.0.1", "port": 7960, "endpoints": {"ping": "/ping", "generate": "/generate", "generate_multi": "/generate_multi", "status": "/status"}}, "settings": {"default": {"seed": 1234, "guidance_scale": 5.5, "num_inference_steps": 25, "octree_resolution": 256, "texture_size": 1024, "mesh_simplify_ratio": 0.25, "num_chunks": 30}, "presets": {"low": {"name": "Low (Fast)", "guidance_scale": 3.0, "num_inference_steps": 15, "octree_resolution": 128, "texture_size": 512, "mesh_simplify_ratio": 0.35, "num_chunks": 15}, "medium": {"name": "Medium (Average Wait)", "guidance_scale": 5.5, "num_inference_steps": 25, "octree_resolution": 256, "texture_size": 1024, "mesh_simplify_ratio": 0.25, "num_chunks": 30}, "high": {"name": "High (Extended Wait)", "guidance_scale": 7.5, "num_inference_steps": 35, "octree_resolution": 384, "texture_size": 1536, "mesh_simplify_ratio": 0.2, "num_chunks": 45}, "ultra": {"name": "Ultra (<PERSON><PERSON><PERSON>)", "guidance_scale": 10.0, "num_inference_steps": 50, "octree_resolution": 512, "texture_size": 2048, "mesh_simplify_ratio": 0.15, "num_chunks": 60}}}}