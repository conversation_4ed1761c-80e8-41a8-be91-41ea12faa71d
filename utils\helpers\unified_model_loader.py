"""
Unified Model Loader for Image Generation
Handles loading of all image generation models with proper error handling and validation.
"""

import os
import json
import torch
from pathlib import Path
from typing import Optional, Dict, Any, Tuple
from diffusers import (
    StableDiffusionPipeline, 
    StableDiffusionXLPipeline,
    FluxPipeline,
    DiffusionPipeline
)

class UnifiedModelLoader:
    """Unified model loader that handles all image generation models."""
    
    # Model configurations with proper settings
    MODEL_CONFIGS = {
        "stable-diffusion-v1-5": {
            "pipeline_class": StableDiffusionPipeline,
            "model_id": "runwayml/stable-diffusion-v1-5",
            "local_path": "stable-diffusion-v1-5",
            "scaling_factor": 0.18215,
            "default_size": (512, 512),
            "supports_refiner": False,
            "load_params": {
                "torch_dtype": torch.float16,
                "use_safetensors": True,
                "variant": "fp16"
            }
        },
        "stable-diffusion-2-1": {
            "pipeline_class": StableDiffusionPipeline,
            "model_id": "stabilityai/stable-diffusion-2-1",
            "local_path": "stable-diffusion-2-1",
            "scaling_factor": 0.18215,
            "default_size": (768, 768),
            "supports_refiner": False,
            "load_params": {
                "torch_dtype": torch.float16,
                "use_safetensors": True,
                "variant": "fp16"
            }
        },
        "stable-diffusion-xl-base-1.0": {
            "pipeline_class": StableDiffusionXLPipeline,
            "model_id": "stabilityai/stable-diffusion-xl-base-1.0",
            "local_path": "stable-diffusion-xl-base-1.0",
            "scaling_factor": 0.13025,
            "default_size": (1024, 1024),
            "supports_refiner": True,
            "load_params": {
                "torch_dtype": torch.float16,
                "use_safetensors": True,
                "variant": "fp16"
            }
        },
        "sdxl-turbo": {
            "pipeline_class": StableDiffusionXLPipeline,
            "model_id": "stabilityai/sdxl-turbo",
            "local_path": "sdxl-turbo",
            "scaling_factor": 0.13025,
            "default_size": (512, 512),
            "supports_refiner": False,
            "turbo_mode": True,
            "load_params": {
                "torch_dtype": torch.float16,
                "use_safetensors": True,
                "variant": "fp16"
            }
        },
        "flux-dev": {
            "pipeline_class": FluxPipeline,
            "model_id": "black-forest-labs/FLUX.1-dev",
            "local_path": "fluxDev",
            "scaling_factor": 0.3611,
            "default_size": (1024, 1024),
            "supports_refiner": False,
            "load_params": {
                "torch_dtype": torch.bfloat16,
                "use_safetensors": True
            }
        }
    }
    
    def __init__(self, models_base_path: str, device: str = "cuda", torch_dtype=torch.float16):
        self.models_base_path = Path(models_base_path)
        self.device = device
        self.torch_dtype = torch_dtype
        self.loaded_models = {}
        
    def validate_model_files(self, model_name: str) -> Tuple[bool, str]:
        """Validate that all required model files exist."""
        if model_name not in self.MODEL_CONFIGS:
            return False, f"Unknown model: {model_name}"
            
        config = self.MODEL_CONFIGS[model_name]
        model_path = self.models_base_path / config["local_path"]
        
        if not model_path.exists():
            return False, f"Model directory not found: {model_path}"
            
        # Check for model_index.json (required for diffusers)
        if not (model_path / "model_index.json").exists():
            return False, f"model_index.json not found in {model_path}"
            
        # Check for essential components
        required_components = ["scheduler", "tokenizer"]
        if model_name.startswith("stable-diffusion-xl") or model_name == "sdxl-turbo":
            required_components.extend(["text_encoder", "text_encoder_2", "tokenizer_2", "unet", "vae"])
        elif model_name == "flux-dev":
            required_components.extend(["text_encoder", "text_encoder_2", "tokenizer_2", "transformer", "vae"])
        else:
            required_components.extend(["text_encoder", "unet", "vae"])
            
        for component in required_components:
            component_path = model_path / component
            if not component_path.exists():
                return False, f"Missing component: {component} in {model_path}"
                
        return True, "Model validation passed"
        
    def create_missing_config(self, model_name: str) -> bool:
        """Create missing config.json file if needed."""
        config = self.MODEL_CONFIGS[model_name]
        model_path = self.models_base_path / config["local_path"]
        config_path = model_path / "config.json"
        
        if config_path.exists():
            return True
            
        # Create a basic config.json based on model_index.json
        model_index_path = model_path / "model_index.json"
        if not model_index_path.exists():
            return False
            
        try:
            with open(model_index_path, 'r') as f:
                model_index = json.load(f)
                
            # Create basic config
            basic_config = {
                "_class_name": model_index.get("_class_name", config["pipeline_class"].__name__),
                "_diffusers_version": "0.21.0",
                "_name_or_path": config["model_id"],
                "requires_safety_checker": model_name in ["stable-diffusion-v1-5"]
            }
            
            with open(config_path, 'w') as f:
                json.dump(basic_config, f, indent=2)
                
            print(f"Created missing config.json for {model_name}")
            return True
            
        except Exception as e:
            print(f"Failed to create config.json for {model_name}: {e}")
            return False
            
    def load_model(self, model_name: str, progress_callback=None) -> Optional[Any]:
        """Load a model with unified error handling."""
        if model_name in self.loaded_models:
            return self.loaded_models[model_name]
            
        if model_name not in self.MODEL_CONFIGS:
            print(f"Error: Unknown model {model_name}")
            return None
            
        config = self.MODEL_CONFIGS[model_name]
        
        # Validate model files
        is_valid, message = self.validate_model_files(model_name)
        if not is_valid:
            print(f"Model validation failed for {model_name}: {message}")
            return None
            
        # Create missing config if needed
        if not self.create_missing_config(model_name):
            print(f"Failed to create required config for {model_name}")
            return None
            
        model_path = self.models_base_path / config["local_path"]
        
        try:
            if progress_callback:
                progress_callback("loading_model", 1, 5, f"Loading {model_name}...")
                
            # Prepare load parameters
            load_params = config["load_params"].copy()
            load_params.update({
                "low_cpu_mem_usage": True,
                "device_map": None  # We'll move to device manually
            })

            # Override torch_dtype if specified
            if hasattr(self, 'torch_dtype') and self.torch_dtype:
                load_params["torch_dtype"] = self.torch_dtype

            # Remove variant if it causes issues
            try:
                # Test if variant works by checking for files
                test_load = config["pipeline_class"].from_pretrained.__func__
                if "variant" in load_params:
                    # Remove variant for problematic models
                    if model_name in ["stable-diffusion-v1-5", "stable-diffusion-2-1"]:
                        load_params.pop("variant", None)
            except:
                load_params.pop("variant", None)
                
            if progress_callback:
                progress_callback("loading_model", 2, 5, f"Initializing {model_name} pipeline...")
                
            # Load the pipeline
            pipeline = config["pipeline_class"].from_pretrained(
                str(model_path),
                **load_params
            )
            
            if progress_callback:
                progress_callback("loading_model", 4, 5, f"Moving {model_name} to device...")
                
            # Move to device
            if torch.cuda.is_available() and self.device == "cuda":
                pipeline = pipeline.to(self.device)
                # Enable memory efficient attention
                if hasattr(pipeline, 'enable_model_cpu_offload'):
                    pipeline.enable_model_cpu_offload()
                if hasattr(pipeline, 'enable_attention_slicing'):
                    pipeline.enable_attention_slicing()
                    
            if progress_callback:
                progress_callback("loading_model", 5, 5, f"{model_name} loaded successfully")
                
            # Store model info
            pipeline._model_config = config
            pipeline._model_name = model_name
            
            self.loaded_models[model_name] = pipeline
            print(f"Successfully loaded {model_name}")
            return pipeline
            
        except Exception as e:
            print(f"Error loading model {model_name}: {e}")
            import traceback
            traceback.print_exc()
            return None
            
    def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get model configuration information."""
        return self.MODEL_CONFIGS.get(model_name)
        
    def unload_model(self, model_name: str):
        """Unload a model to free memory."""
        if model_name in self.loaded_models:
            del self.loaded_models[model_name]
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            print(f"Unloaded {model_name}")
            
    def list_available_models(self) -> list:
        """List all available models."""
        return list(self.MODEL_CONFIGS.keys())
