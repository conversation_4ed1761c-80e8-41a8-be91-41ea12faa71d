2025-07-14 14:01:22 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 14:01:22 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 14:15:46 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-14 14:22:21 | ERROR | hunyuan3d_api | Failed to load texture pipeline: [WinError 1314] A required privilege is not held by the client: '..\\..\\..\\..\\blobs\\8d1412e50d46389d40b29f824034ff4ba4f973ca' -> 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--tencent--Hunyuan3D-2\\snapshots\\34e28261f71c32975727be8db0eace439a280f82\\hunyuan3d-delight-v2-0\\text_encoder\\config.json'
2025-07-14 14:22:21 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-14 14:22:21 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-14 14:22:21 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-14 14:34:09 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 14:34:09 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 14:34:23 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-14 14:40:51 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 14:40:51 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 14:40:52 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 14:40:52 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 14:40:55 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 14:40:55 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 14:41:05 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-14 14:41:05 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-14 14:57:51 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 14:57:51 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 14:58:04 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-14 14:59:18 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-14 14:59:18 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-14 14:59:18 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-14 14:59:18 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-14 14:59:20 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-14 14:59:20 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-14 14:59:20 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-14 14:59:23 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-14 15:00:33 | INFO | hunyuan3d_api | Processing mesh (faces: 354440)
2025-07-14 15:00:34 | INFO | hunyuan3d_api | Reducing faces to 336718
2025-07-14 15:00:37 | INFO | hunyuan3d_api | Skipping texture generation for this request
2025-07-14 15:00:37 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 4.24 seconds
2025-07-14 15:00:37 | INFO | hunyuan3d_api | Generation completed in 76.82 seconds
2025-07-14 15:00:37 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-14 15:13:17 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 15:13:17 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 15:13:30 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-14 15:14:44 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-14 15:14:44 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-14 15:14:44 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-14 15:14:44 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-14 15:14:45 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-14 15:14:46 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-14 15:14:46 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-14 15:14:49 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-14 15:15:49 | INFO | hunyuan3d_api | Processing mesh (faces: 352268)
2025-07-14 15:15:51 | INFO | hunyuan3d_api | Reducing faces to 334654
2025-07-14 15:15:53 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-14 15:15:56 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-14 15:15:56 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-14 15:15:56 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-14 15:15:56 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-14 15:15:56 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-14 15:19:46 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-14 15:19:46 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-14 15:19:47 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-14 15:20:31 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-14 15:20:31 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-14 15:20:40 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-14 15:20:43 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 293.99 seconds
2025-07-14 15:20:43 | INFO | hunyuan3d_api | Generation completed in 357.82 seconds
2025-07-14 15:46:08 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-14 15:46:09 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-14 15:46:21 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-14 15:47:34 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-14 15:47:34 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-14 15:47:34 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-14 15:47:34 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-14 15:47:36 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-14 15:47:36 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-14 15:47:36 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-14 15:47:40 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-14 15:48:39 | INFO | hunyuan3d_api | Processing mesh (faces: 354312)
2025-07-14 15:48:41 | INFO | hunyuan3d_api | Reducing faces to 336596
2025-07-14 15:48:43 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-14 15:48:46 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-14 15:48:46 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-14 15:48:46 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-14 15:48:46 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-14 15:48:46 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-14 15:52:03 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-14 15:52:04 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-14 15:52:05 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-14 15:52:52 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-14 15:52:52 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-14 15:53:01 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-14 15:53:04 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 264.63 seconds
2025-07-14 15:53:04 | INFO | hunyuan3d_api | Generation completed in 327.80 seconds
2025-07-14 15:53:04 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-15 07:00:24 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-15 07:00:24 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-15 07:10:24 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-15 07:11:54 | ERROR | hunyuan3d_api | Failed to load texture pipeline: [WinError 1314] A required privilege is not held by the client: '..\\..\\..\\..\\blobs\\fb1692ff11ed6b3dee0634f577193bb23c0d37ec' -> 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--tencent--Hunyuan3D-2\\snapshots\\34e28261f71c32975727be8db0eace439a280f82\\hunyuan3d-delight-v2-0\\feature_extractor\\preprocessor_config.json'
2025-07-15 07:11:54 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-15 07:11:54 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-15 07:11:54 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-15 07:11:56 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-15 07:11:56 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-15 07:11:56 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-15 07:11:59 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-15 07:13:03 | INFO | hunyuan3d_api | Processing mesh (faces: 615288)
2025-07-15 07:13:06 | INFO | hunyuan3d_api | Reducing faces to 584523
2025-07-15 07:13:09 | INFO | hunyuan3d_api | Skipping texture generation for this request
2025-07-15 07:13:09 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 6.12 seconds
2025-07-15 07:13:09 | INFO | hunyuan3d_api | Generation completed in 73.58 seconds
2025-07-15 07:13:09 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-15 07:17:36 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-15 07:17:36 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-15 07:17:36 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-15 07:32:35 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-15 07:32:35 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-15 07:32:35 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-15 07:32:35 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-15 07:32:36 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-15 07:32:36 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-15 07:32:36 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-15 07:32:39 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-15 07:33:37 | INFO | hunyuan3d_api | Processing mesh (faces: 776214)
2025-07-15 07:33:41 | INFO | hunyuan3d_api | Reducing faces to 737403
2025-07-15 07:33:45 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-15 07:33:48 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-15 07:33:48 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-15 07:33:48 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-15 07:33:48 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-15 07:33:48 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-15 07:41:56 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 07:41:56 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 07:41:57 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 07:42:39 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-15 07:42:39 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-15 07:43:09 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-15 07:43:12 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 574.62 seconds
2025-07-15 07:43:12 | INFO | hunyuan3d_api | Generation completed in 635.92 seconds
2025-07-15 07:43:12 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-15 09:31:21 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-15 09:31:21 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-15 09:31:21 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-15 09:32:44 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-15 09:32:44 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-15 09:32:44 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-15 09:32:44 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-15 09:32:44 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-15 09:32:44 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-15 09:32:44 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-15 09:32:48 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-15 09:33:55 | INFO | hunyuan3d_api | Processing mesh (faces: 791208)
2025-07-15 09:33:59 | INFO | hunyuan3d_api | Reducing faces to 751647
2025-07-15 09:34:04 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-15 09:34:08 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-15 09:34:08 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-15 09:34:08 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-15 09:34:08 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-15 09:34:08 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-15 09:42:25 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 09:42:26 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 09:42:26 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 09:43:17 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-15 09:43:17 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-15 09:44:01 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-15 09:44:04 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 609.76 seconds
2025-07-15 09:44:04 | INFO | hunyuan3d_api | Generation completed in 680.09 seconds
2025-07-15 09:44:04 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-15 10:03:04 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-15 10:03:04 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-15 10:03:04 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-15 10:04:20 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-15 10:04:20 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-15 10:04:20 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-15 10:04:20 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-15 10:04:20 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-15 10:04:21 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-15 10:04:21 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-15 10:04:24 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-15 10:05:27 | INFO | hunyuan3d_api | Processing mesh (faces: 474660)
2025-07-15 10:05:29 | INFO | hunyuan3d_api | Reducing faces to 450927
2025-07-15 10:05:32 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-15 10:05:35 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-15 10:05:35 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-15 10:05:35 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-15 10:05:35 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-15 10:05:35 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-15 10:09:01 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 10:09:02 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 10:09:02 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 10:11:43 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-15 10:11:43 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-15 10:11:44 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-15 10:12:58 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-15 10:12:58 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-15 10:12:58 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-15 10:12:58 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-15 10:12:59 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-15 10:12:59 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-15 10:12:59 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-15 10:13:03 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-15 10:14:05 | INFO | hunyuan3d_api | Processing mesh (faces: 873334)
2025-07-15 10:14:09 | INFO | hunyuan3d_api | Reducing faces to 829667
2025-07-15 10:14:14 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-15 10:14:17 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-15 10:14:17 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-15 10:14:17 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-15 10:14:17 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-15 10:14:17 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-15 10:22:09 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-15 10:22:09 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-15 10:22:10 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-15 10:23:24 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-15 10:23:24 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-15 10:23:24 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-15 10:23:24 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-15 10:23:24 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-15 10:23:24 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-15 10:23:24 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-15 10:23:27 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-15 10:24:35 | INFO | hunyuan3d_api | Processing mesh (faces: 857436)
2025-07-15 10:24:39 | INFO | hunyuan3d_api | Reducing faces to 814564
2025-07-15 10:24:45 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-15 10:24:48 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-15 10:24:48 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-15 10:24:48 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-15 10:24:48 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-15 10:24:48 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-15 10:34:26 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 10:34:27 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 10:34:27 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 10:35:17 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-15 10:35:17 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-15 10:35:43 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-15 10:35:46 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 670.84 seconds
2025-07-15 10:35:46 | INFO | hunyuan3d_api | Generation completed in 741.71 seconds
2025-07-15 10:35:46 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-15 10:37:38 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-15 10:37:38 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-15 10:37:38 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-15 10:38:50 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-15 10:38:50 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-15 10:38:50 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-15 10:38:50 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-15 10:38:52 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-15 10:38:52 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-15 10:38:52 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-15 10:38:56 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-15 10:39:58 | INFO | hunyuan3d_api | Processing mesh (faces: 153128)
2025-07-15 10:39:58 | INFO | hunyuan3d_api | Reducing faces to 145471
2025-07-15 10:39:59 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-15 10:40:02 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-15 10:40:02 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-15 10:40:02 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-15 10:40:02 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-15 10:40:02 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-15 10:42:54 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 10:42:55 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 10:42:55 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 10:43:45 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-15 10:43:45 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-15 10:43:54 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-15 10:43:57 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 238.98 seconds
2025-07-15 10:43:57 | INFO | hunyuan3d_api | Generation completed in 304.99 seconds
2025-07-15 10:43:57 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-15 11:31:49 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-15 11:31:49 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-15 11:31:50 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-15 11:33:02 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-15 11:33:02 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-15 11:33:02 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-15 11:33:02 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-15 11:33:03 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-15 11:33:03 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-15 11:33:03 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-15 11:33:07 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-15 11:34:14 | INFO | hunyuan3d_api | Processing mesh (faces: 420004)
2025-07-15 11:34:16 | INFO | hunyuan3d_api | Reducing faces to 399003
2025-07-15 11:34:19 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-15 11:34:22 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-15 11:34:22 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-15 11:34:22 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-15 11:34:22 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-15 11:34:22 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-15 11:37:35 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 11:37:36 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 11:37:36 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 11:38:26 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-15 11:38:26 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-15 11:38:37 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-15 11:38:40 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 266.07 seconds
2025-07-15 11:38:40 | INFO | hunyuan3d_api | Generation completed in 337.14 seconds
2025-07-15 11:38:41 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-15 15:13:37 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-15 15:13:37 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-15 15:13:37 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-15 15:15:24 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-15 15:15:24 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-15 15:15:24 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-15 15:15:24 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-15 15:15:26 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-15 15:15:26 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-15 15:15:26 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-15 15:15:29 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-15 15:16:44 | INFO | hunyuan3d_api | Processing mesh (faces: 923568)
2025-07-15 15:16:49 | INFO | hunyuan3d_api | Reducing faces to 877389
2025-07-15 15:16:55 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-15 15:16:59 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-15 15:16:59 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-15 15:16:59 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-15 15:16:59 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-15 15:16:59 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-15 15:30:26 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 15:30:28 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 15:30:28 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 15:31:18 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-15 15:31:18 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-15 15:32:26 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-15 15:32:29 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 944.96 seconds
2025-07-15 15:32:29 | INFO | hunyuan3d_api | Generation completed in 1023.02 seconds
2025-07-15 15:32:30 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-15 16:22:30 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-15 16:22:30 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-15 16:22:30 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-15 16:24:10 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-15 16:24:10 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-15 16:24:10 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-15 16:24:10 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-15 16:24:11 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-15 16:24:11 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-15 16:24:11 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-15 16:24:14 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-15 16:25:19 | INFO | hunyuan3d_api | Processing mesh (faces: 924408)
2025-07-15 16:25:24 | INFO | hunyuan3d_api | Reducing faces to 878187
2025-07-15 16:25:30 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-15 16:25:33 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-15 16:25:33 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-15 16:25:33 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-15 16:25:33 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-15 16:25:33 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-15 16:39:23 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 16:39:24 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 16:39:25 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 16:40:09 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-15 16:40:09 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-15 16:40:54 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-15 16:40:56 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 937.75 seconds
2025-07-15 16:40:56 | INFO | hunyuan3d_api | Generation completed in 1005.44 seconds
2025-07-15 16:40:58 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-15 17:47:14 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-15 17:47:14 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-15 17:47:14 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-15 17:48:25 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-15 17:48:25 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-15 17:48:25 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-15 17:48:25 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-15 17:48:26 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-15 17:48:27 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-15 17:48:27 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-15 17:48:29 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-15 17:49:24 | INFO | hunyuan3d_api | Processing mesh (faces: 246148)
2025-07-15 17:49:25 | INFO | hunyuan3d_api | Reducing faces to 233840
2025-07-15 17:49:26 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-15 17:49:30 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-15 17:49:30 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-15 17:49:30 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-15 17:49:30 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-15 17:49:30 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-15 17:52:31 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 17:52:32 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 17:52:33 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-15 17:53:17 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-15 17:53:17 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-15 17:53:25 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-15 17:53:28 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 244.18 seconds
2025-07-15 17:53:28 | INFO | hunyuan3d_api | Generation completed in 301.72 seconds
2025-07-15 17:53:29 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-16 22:29:08 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-16 22:29:08 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-16 22:30:59 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-16 22:30:59 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-16 22:31:00 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-16 22:31:55 | ERROR | hunyuan3d_api | Failed to load texture pipeline: [Errno 22] Invalid argument
2025-07-16 22:31:55 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-16 22:31:55 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-16 22:31:55 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-16 23:21:25 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-16 23:21:25 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-16 23:21:26 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-16 23:22:22 | ERROR | hunyuan3d_api | Failed to load texture pipeline: [Errno 22] Invalid argument
2025-07-16 23:22:22 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-16 23:22:22 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-16 23:22:22 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-16 23:53:23 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-16 23:53:23 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-16 23:53:23 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-16 23:54:07 | ERROR | hunyuan3d_api | Failed to load texture pipeline: [Errno 22] Invalid argument
2025-07-16 23:54:07 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-16 23:54:07 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-16 23:54:07 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-17 00:08:06 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-17 00:08:06 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-17 00:08:07 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-17 00:08:52 | ERROR | hunyuan3d_api | Failed to load texture pipeline: [Errno 22] Invalid argument
2025-07-17 00:08:52 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-17 00:08:52 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-17 00:08:52 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-17 00:18:00 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-17 00:18:00 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-17 00:18:00 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-17 00:18:44 | ERROR | hunyuan3d_api | Failed to load texture pipeline: [Errno 22] Invalid argument
2025-07-17 00:18:44 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-17 00:18:44 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-17 00:18:44 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-17 00:22:44 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-17 00:22:44 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-17 00:31:16 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-17 00:31:16 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-17 00:31:16 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-17 00:32:00 | ERROR | hunyuan3d_api | Failed to load texture pipeline: [Errno 22] Invalid argument
2025-07-17 00:32:00 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-17 00:32:00 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-17 00:32:00 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-17 00:40:15 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-17 00:40:15 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-17 00:40:16 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-17 00:41:33 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-17 00:41:33 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-17 00:41:33 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-17 00:41:33 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-17 00:41:33 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-17 00:41:34 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-17 00:41:34 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-17 00:41:37 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-17 00:42:49 | INFO | hunyuan3d_api | Processing mesh (faces: 934588)
2025-07-17 00:42:53 | INFO | hunyuan3d_api | Reducing faces to 887858
2025-07-17 00:42:59 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-17 00:43:02 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-17 00:43:02 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-17 00:43:02 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-17 00:43:02 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-17 00:43:02 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-17 00:53:36 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-17 00:53:37 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-17 00:53:38 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-17 00:54:23 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-17 00:54:23 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-17 00:55:03 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-17 00:55:07 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 737.63 seconds
2025-07-17 00:55:07 | INFO | hunyuan3d_api | Generation completed in 813.45 seconds
2025-07-17 00:55:07 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-17 08:07:46 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-17 08:07:46 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-17 08:07:46 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-17 08:09:28 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-17 08:09:28 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-17 08:09:28 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-17 08:09:28 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-17 08:09:29 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-17 08:09:29 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-17 08:09:29 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-17 08:09:32 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-17 08:10:32 | INFO | hunyuan3d_api | Processing mesh (faces: 271896)
2025-07-17 08:10:33 | INFO | hunyuan3d_api | Reducing faces to 258301
2025-07-17 08:10:35 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-17 08:10:38 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-17 08:10:38 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-17 08:10:38 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-17 08:10:38 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-17 08:10:38 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-17 08:12:44 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-17 08:12:45 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-17 08:12:45 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-17 08:13:30 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-17 08:13:30 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-17 08:13:41 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-17 08:13:43 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 191.06 seconds
2025-07-17 08:13:43 | INFO | hunyuan3d_api | Generation completed in 254.77 seconds
2025-07-17 08:13:44 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-17 08:17:42 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-17 08:17:42 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-17 08:17:42 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-17 08:18:52 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-17 08:18:52 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-17 08:18:52 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-17 08:18:52 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-17 08:18:54 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-17 08:18:54 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-17 08:18:54 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-17 08:18:57 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-17 08:19:54 | INFO | hunyuan3d_api | Processing mesh (faces: 297874)
2025-07-17 08:19:55 | INFO | hunyuan3d_api | Reducing faces to 282980
2025-07-17 08:19:57 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-17 08:20:00 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-17 08:20:00 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-17 08:20:00 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-17 08:20:00 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-17 08:20:00 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-17 08:22:40 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-17 08:22:41 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-17 08:22:41 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-17 08:23:25 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-17 08:23:26 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-17 08:23:47 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-17 08:23:49 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 234.80 seconds
2025-07-17 08:23:49 | INFO | hunyuan3d_api | Generation completed in 294.68 seconds
2025-07-17 08:23:49 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-17 08:31:42 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-17 08:31:42 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-17 08:31:42 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-17 08:32:49 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-17 08:32:49 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-17 08:32:49 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-17 08:32:49 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-17 08:32:49 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-17 08:32:50 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-17 08:32:50 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-17 08:32:53 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-17 08:33:51 | INFO | hunyuan3d_api | Processing mesh (faces: 201104)
2025-07-17 08:33:52 | INFO | hunyuan3d_api | Reducing faces to 191048
2025-07-17 08:33:53 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-17 08:33:56 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-17 08:33:56 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-17 08:33:56 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-17 08:33:56 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-17 08:33:56 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-17 09:24:23 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-17 09:24:23 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-17 09:24:23 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-17 09:25:34 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-17 09:25:34 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-17 09:25:34 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-17 09:25:34 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-17 10:54:44 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-17 10:54:44 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-17 10:54:44 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-17 10:55:53 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-17 10:55:53 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-17 10:55:53 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-17 10:55:53 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-17 10:55:54 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-17 10:55:54 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-17 10:55:54 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-17 10:55:57 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-17 10:57:02 | INFO | hunyuan3d_api | Processing mesh (faces: 916472)
2025-07-17 10:57:07 | INFO | hunyuan3d_api | Reducing faces to 870648
2025-07-17 10:57:12 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-17 10:57:15 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-17 10:57:15 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-17 10:57:15 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-17 10:57:15 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-17 10:57:15 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-17 11:08:40 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-17 11:08:41 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-17 11:08:41 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-17 11:09:26 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-17 11:09:26 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-17 11:10:08 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-17 11:10:11 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 788.82 seconds
2025-07-17 11:10:11 | INFO | hunyuan3d_api | Generation completed in 857.41 seconds
2025-07-17 11:10:12 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-17 11:21:49 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-17 11:21:49 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-17 11:21:49 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-17 11:23:02 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-17 11:23:02 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-17 11:23:02 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-17 11:23:02 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-17 11:24:17 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-17 11:24:17 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-17 11:24:17 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-17 11:25:22 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-17 11:25:22 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-17 11:25:22 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-17 11:25:22 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-17 11:41:12 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-17 11:41:12 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-17 11:41:12 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-17 11:42:17 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-17 11:42:17 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-17 11:42:17 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-17 11:42:17 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-17 11:42:20 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-17 11:42:20 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-17 11:42:20 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-17 11:42:23 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-17 11:43:28 | INFO | hunyuan3d_api | Processing mesh (faces: 897616)
2025-07-17 11:43:32 | INFO | hunyuan3d_api | Reducing faces to 852735
2025-07-17 11:43:37 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-17 11:43:40 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-17 11:43:40 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-17 11:43:40 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-17 11:43:40 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-17 11:43:40 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-18 00:09:43 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-18 00:09:43 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-18 00:09:55 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-18 00:11:40 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-18 00:11:40 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-18 00:11:40 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-18 00:11:40 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-18 00:11:41 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-18 00:11:42 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-18 00:11:42 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-18 00:11:45 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-18 00:12:53 | INFO | hunyuan3d_api | Processing mesh (faces: 910208)
2025-07-18 00:12:58 | INFO | hunyuan3d_api | Reducing faces to 864697
2025-07-18 00:13:03 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-18 00:13:07 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-18 00:13:07 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-18 00:13:07 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-18 00:13:07 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-18 00:13:07 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-18 00:23:30 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-18 00:23:31 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-18 00:23:32 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-18 00:24:15 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-18 00:24:15 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-18 00:24:49 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-18 00:24:53 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 719.42 seconds
2025-07-18 00:24:53 | INFO | hunyuan3d_api | Generation completed in 791.86 seconds
2025-07-18 00:24:54 | INFO | hunyuan3d_api | Client is downloading a model.
2025-07-18 09:53:04 | INFO | hy3dgen.shapgen | Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2mv\hunyuan3d-dit-v2-mv
2025-07-18 09:53:04 | INFO | hy3dgen.shapgen | Model path not exists, try to download from huggingface
2025-07-18 09:53:15 | INFO | hy3dgen.shapgen | Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2mv\snapshots\ea1415a196ba61f465e923072172713aa023e6b0\hunyuan3d-dit-v2-mv\model.fp16.safetensors
2025-07-18 09:54:52 | INFO | hunyuan3d_api | Texture pipeline loaded with CPU offloading (low VRAM mode)
2025-07-18 09:54:52 | INFO | hunyuan3d_api | VRAM allocated at startup: 0.0MB
2025-07-18 09:54:52 | INFO | hunyuan3d_api | Initialized Hunyuan3D with model tencent/Hunyuan3D-2mv/hunyuan3d-dit-v2-mv on cuda
2025-07-18 09:54:53 | INFO | hunyuan3d_api | Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
2025-07-18 09:54:53 | INFO | hunyuan3d_api | Client asked to multi-view-generate
2025-07-18 09:54:54 | INFO | hunyuan3d_api | Shape generation model is on CPU, moving back to GPU
2025-07-18 09:54:54 | INFO | hunyuan3d_api | Moving shape generation model back to cuda
2025-07-18 09:54:56 | INFO | hunyuan3d_api | Shape generation model moved to cuda
2025-07-18 09:56:07 | INFO | hunyuan3d_api | Processing mesh (faces: 902250)
2025-07-18 09:56:11 | INFO | hunyuan3d_api | Reducing faces to 857137
2025-07-18 09:56:16 | INFO | hunyuan3d_api | Moving shape generation model to CPU to free ~6GB VRAM
2025-07-18 09:56:20 | INFO | hunyuan3d_api | Applying memory optimization (chunk size: 3)
2025-07-18 09:56:20 | INFO | hunyuan3d_api | Applied chunked processing to VAE decoder (chunk size: 3)
2025-07-18 09:56:20 | INFO | hunyuan3d_api | Applied chunked processing to image encoding (chunk size: 3)
2025-07-18 09:56:20 | INFO | hunyuan3d_api | Texture memory optimization applied
2025-07-18 09:56:20 | INFO | hunyuan3d_api | Starting texture generation...
2025-07-18 10:05:42 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-18 10:05:43 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-18 10:05:44 | INFO | hunyuan3d_api | Using chunked image encoding
2025-07-18 10:06:29 | INFO | hunyuan3d_api | Using chunked VAE decoding
2025-07-18 10:06:29 | INFO | hunyuan3d_api | Processing VAE decode in 6 items with chunk size 3
2025-07-18 10:07:00 | INFO | hunyuan3d_api | Applied texture to mesh successfully
2025-07-18 10:07:04 | INFO | hunyuan3d_api | Exported GLB model to temp\current_generation\model.glb in 656.71 seconds
2025-07-18 10:07:04 | INFO | hunyuan3d_api | Generation completed in 730.78 seconds
2025-07-18 10:07:04 | INFO | hunyuan3d_api | Client is downloading a model.
