import React from 'react';
import { Settings as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>liders, ChevronDown, ChevronUp, Cpu, Zap } from 'lucide-react';

interface SettingsProps {
  onSettingsChange: (settings: ModelSettings) => void;
  isDarkMode: boolean;
  initialSettings?: ModelSettings;
  selectedPipeline?: string;
  onPipelineChange?: (pipeline: string) => void;
  availablePipelines?: Pipeline[];
  selectedModel?: string;
  onModelChange?: (model: string) => void;
}

interface Pipeline {
  id: string;
  name: string;
  description: string;
  available: boolean;
  features: string[];
  models?: { name: string; repo_id?: string }[];
}

interface ModelSettings {
  ss_steps: number;
  ss_cfg_strength: number;
  slat_steps: number;
  slat_cfg_strength: number;
  seed?: number;
  randomize_seed?: boolean;
  simplify: number;
  texture_size: number;
  enable_lighting_optimizer?: boolean;
  // Hunyuan3D-2 specific settings
  octree_resolution?: number;
  num_inference_steps?: number;
  guidance_scale?: number;
  enable_texture?: boolean;
  face_count?: number;
  // Advanced Hunyuan3D-2 settings
  mesh_simplify_ratio?: number;
  num_chunks?: number;
}

interface QualityPreset {
  name: string;
  description: string;
  estimatedTime: string;
  icon: string;
  settings: Partial<ModelSettings>;
}

// Quality presets for Hunyuan3D-2
const HUNYUAN_QUALITY_PRESETS: QualityPreset[] = [
  {
    name: "Low Quality: Fast",
    description: "Quick generation with basic quality",
    estimatedTime: "1-2 minutes",
    icon: "⚡",
    settings: {
      octree_resolution: 128,
      num_inference_steps: 15,
      guidance_scale: 5.0,
      enable_texture: false,
      face_count: 15000,
      mesh_simplify_ratio: 0.25,
      num_chunks: 15,
      texture_size: 512
    }
  },
  {
    name: "Medium Quality: Average Wait",
    description: "Balanced quality and speed",
    estimatedTime: "3-5 minutes",
    icon: "⚖️",
    settings: {
      octree_resolution: 256,
      num_inference_steps: 25,
      guidance_scale: 5.5,
      enable_texture: true,
      face_count: 30000,
      mesh_simplify_ratio: 0.25,
      num_chunks: 30,
      texture_size: 1024
    }
  },
  {
    name: "High Quality: Extended Wait",
    description: "High quality with detailed textures (no timeout)",
    estimatedTime: "10-20 minutes",
    icon: "💎",
    settings: {
      octree_resolution: 384,
      num_inference_steps: 35,
      guidance_scale: 6.0,
      enable_texture: true,
      face_count: 50000,
      mesh_simplify_ratio: 0.20,
      num_chunks: 50,
      texture_size: 1536
    }
  },
  {
    name: "Ultra Quality: Insane Wait",
    description: "Maximum quality for professional use (no timeout)",
    estimatedTime: "20-45 minutes",
    icon: "🚀",
    settings: {
      octree_resolution: 512,
      num_inference_steps: 50,
      guidance_scale: 7.0,
      enable_texture: true,
      face_count: 70000,
      mesh_simplify_ratio: 0.15,
      num_chunks: 70,
      texture_size: 2048
    }
  }
];

// Quality presets for Trellis
const TRELLIS_QUALITY_PRESETS: QualityPreset[] = [
  {
    name: "Low Quality: Fast",
    description: "Quick generation with basic quality",
    estimatedTime: "30-60 seconds",
    icon: "⚡",
    settings: {
      ss_steps: 8,
      ss_cfg_strength: 5.0,
      slat_steps: 8,
      slat_cfg_strength: 2.0,
      simplify: 0.98,
      texture_size: 512,
      enable_lighting_optimizer: false
    }
  },
  {
    name: "Medium Quality: Average Wait",
    description: "Balanced quality and speed",
    estimatedTime: "1-2 minutes",
    icon: "⚖️",
    settings: {
      ss_steps: 12,
      ss_cfg_strength: 7.5,
      slat_steps: 12,
      slat_cfg_strength: 3.0,
      simplify: 0.95,
      texture_size: 1024,
      enable_lighting_optimizer: true
    }
  },
  {
    name: "High Quality: Extended Wait",
    description: "High quality with detailed processing",
    estimatedTime: "2-4 minutes",
    icon: "💎",
    settings: {
      ss_steps: 20,
      ss_cfg_strength: 10.0,
      slat_steps: 20,
      slat_cfg_strength: 4.0,
      simplify: 0.90,
      texture_size: 2048,
      enable_lighting_optimizer: true
    }
  },
  {
    name: "Ultra Quality: Insane Wait",
    description: "Maximum quality for professional use",
    estimatedTime: "4-8 minutes",
    icon: "🚀",
    settings: {
      ss_steps: 30,
      ss_cfg_strength: 12.0,
      slat_steps: 30,
      slat_cfg_strength: 5.0,
      simplify: 0.85,
      texture_size: 2048,
      enable_lighting_optimizer: true
    }
  }
];

export const Settings: React.FC<SettingsProps> = ({
  onSettingsChange,
  isDarkMode,
  initialSettings,
  selectedPipeline = 'Microsoft_TRELLIS',
  onPipelineChange,
  availablePipelines = [],
  selectedModel,
  onModelChange,
}) => {
  const [isExpanded, setIsExpanded] = React.useState(false);
  const [isAdvancedExpanded, setIsAdvancedExpanded] = React.useState(false);
  const [selectedPreset, setSelectedPreset] = React.useState<string>('Medium Quality: Average Wait');
  const [settings, setSettings] = React.useState<ModelSettings>(initialSettings || {
    ss_steps: 12,
    ss_cfg_strength: 7.5,
    slat_steps: 12,
    slat_cfg_strength: 3.0,
    randomize_seed: true,
    seed: Math.floor(Math.random() * 1000000),
    simplify: 0.95,
    texture_size: 1024,
    enable_lighting_optimizer: true,
    // Hunyuan3D-2 balanced defaults (research-based stable settings)
    octree_resolution: 256,
    num_inference_steps: 25,
    guidance_scale: 5.5,
    enable_texture: true,
    face_count: 30000,
    // Advanced settings with balanced defaults
    mesh_simplify_ratio: 0.15,
    num_chunks: 30
  });

  // Calculate complexity score for Hunyuan3D-2 settings
  const getComplexityWarning = () => {
    if (!selectedPipeline.toLowerCase().includes('hunyaun')) return null;

    const steps = settings.num_inference_steps || 25;
    const resolution = settings.octree_resolution || 256;
    const chunks = settings.num_chunks || 30;
    const complexityScore = (steps * resolution * chunks) / 1000000;

    // Check if this is a high-quality preset with no timeout
    const isHighQualityPreset = (
      (steps >= 35 && resolution >= 384) || // High Quality preset
      (steps >= 40 && chunks >= 70) || // Ultra Quality preset
      complexityScore > 12 // Very high complexity
    );

    if (complexityScore > 15 && settings.enable_texture) {
      return {
        level: 'high',
        message: isHighQualityPreset
          ? 'Very high complexity - no timeout, generation will complete eventually'
          : 'Very high complexity - may cause timeouts or crashes during texture generation',
        estimatedTime: isHighQualityPreset ? '20-45 minutes' : '10-15 minutes'
      };
    } else if (complexityScore > 8 && settings.enable_texture) {
      return {
        level: 'medium',
        message: isHighQualityPreset
          ? 'High complexity - no timeout, please be patient'
          : 'High complexity - generation may take longer than usual',
        estimatedTime: isHighQualityPreset ? '10-20 minutes' : '5-10 minutes'
      };
    } else if (complexityScore > 5) {
      return {
        level: 'low',
        message: 'Moderate complexity - good balance of quality and speed',
        estimatedTime: '2-5 minutes'
      };
    }
    return null;
  };

  const complexityWarning = getComplexityWarning();

  // Trigger settings change when settings update
  React.useEffect(() => {
    onSettingsChange(settings);
  }, [settings, onSettingsChange]);

  // Get the appropriate preset array based on selected pipeline
  const getQualityPresets = () => {
    return selectedPipeline.toLowerCase().includes('hunyaun')
      ? HUNYUAN_QUALITY_PRESETS
      : TRELLIS_QUALITY_PRESETS;
  };

  // Apply preset settings
  const applyPreset = (presetName: string) => {
    const presets = getQualityPresets();
    const preset = presets.find((p: QualityPreset) => p.name === presetName);
    if (preset) {
      setSelectedPreset(presetName);
      setSettings(prevSettings => ({
        ...prevSettings,
        ...preset.settings
      }));
    }
  };

  // Check if current settings match a preset
  const getCurrentPreset = () => {
    const presets = getQualityPresets();
    for (const preset of presets) {
      const matches = Object.entries(preset.settings).every(([key, value]) => {
        return settings[key as keyof ModelSettings] === value;
      });
      if (matches) return preset.name;
    }
    return 'Custom';
  };

  const currentPreset = getCurrentPreset();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const target = e.target as HTMLInputElement;
    const { name, value, type } = target;
    const checked = target.checked;

    let newValue: any;
    if (type === 'checkbox') {
      newValue = checked;
    } else if (name === 'texture_size') {
      // Convert texture size to number
      newValue = parseInt(value);
    } else if (name === 'seed') {
      // Convert seed to number
      newValue = parseInt(value);
    } else {
      // Default to float for numeric inputs
      newValue = parseFloat(value);
    }

    const updatedSettings = {
      ...settings,
      [name]: newValue,
    };

    // If randomize_seed is turned off, ensure we have a seed value
    if (name === 'randomize_seed' && !newValue && !updatedSettings.seed) {
      updatedSettings.seed = Math.floor(Math.random() * 1000000);
    }

    // If randomize_seed is turned on, we can remove the seed value
    if (name === 'randomize_seed' && newValue) {
      updatedSettings.seed = undefined;
    }

    setSettings(updatedSettings);
    onSettingsChange(updatedSettings);
  };

  return (
    <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 shadow-lg`}>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`w-full flex items-center justify-between gap-2 mb-4 text-left hover:${
          isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
        } rounded-lg p-2 -m-2 transition-colors`}
      >
        <div className="flex items-center gap-2">
          <SettingsIcon className={`w-5 h-5 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`} />
          <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Generation Settings
          </h3>
        </div>
        {isExpanded ? (
          <ChevronUp className={`w-5 h-5 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`} />
        ) : (
          <ChevronDown className={`w-5 h-5 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`} />
        )}
      </button>

      {!isExpanded && (
        <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
          Steps: {settings.ss_steps}/{settings.slat_steps} • Guidance: {settings.ss_cfg_strength}/{settings.slat_cfg_strength} • Texture: {settings.texture_size}px
        </div>
      )}

      {isExpanded && (
        <div className="space-y-4 animate-in slide-in-from-top-2 duration-200">

        {/* Pipeline Selection */}
        {availablePipelines.length > 0 && onPipelineChange && (
          <div className="mb-4 border-b border-gray-200 dark:border-gray-700 pb-4">
            <label className={`flex items-center gap-2 text-sm font-medium mb-2 ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              <Cpu className="w-4 h-4" />
              3D Generation Pipeline
            </label>
            <select
              value={selectedPipeline}
              onChange={(e) => onPipelineChange(e.target.value)}
              className={`w-full px-3 py-2 rounded-lg ${
                isDarkMode ? 'bg-gray-700 text-white border-gray-600' : 'bg-gray-100 border-gray-300'
              } border focus:outline-none focus:ring-2 focus:ring-blue-500`}
            >
              {availablePipelines.map((pipeline) => (
                <option
                  key={pipeline.id}
                  value={pipeline.id}
                  disabled={!pipeline.available}
                >
                  {pipeline.name} {!pipeline.available ? '(Not Available)' : ''}
                </option>
              ))}
            </select>
            {availablePipelines.find(p => p.id === selectedPipeline) && (
              <div className={`mt-2 text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                {availablePipelines.find(p => p.id === selectedPipeline)?.description}
                <div className="flex flex-wrap gap-1 mt-1">
                  {availablePipelines.find(p => p.id === selectedPipeline)?.features.map((feature, index) => (
                    <span
                      key={index}
                      className={`px-2 py-1 rounded text-xs ${
                        isDarkMode ? 'bg-gray-600 text-gray-300' : 'bg-gray-200 text-gray-600'
                      }`}
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Model selection if pipeline provides models */}
        {availablePipelines.find(p => p.id === selectedPipeline)?.models?.length && onModelChange && (
          <div className="mt-4">
            <label className={`flex items-center gap-2 text-sm font-medium mb-2 ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              Model
            </label>
            <select
              value={selectedModel}
              onChange={(e) => onModelChange(e.target.value)}
              className={`w-full px-3 py-2 rounded-lg ${
                isDarkMode ? 'bg-gray-700 text-white border-gray-600' : 'bg-gray-100 border-gray-300'
              } border focus:outline-none focus:ring-2 focus:ring-blue-500`}
            >
              {availablePipelines.find(p => p.id === selectedPipeline)!.models!.map(m => (
                <option key={m.name} value={m.name}>{m.name}</option>
              ))}
            </select>
          </div>
        )}

        <div className="mb-4">
          <label className={`flex items-center gap-2 text-sm font-medium ${
            isDarkMode ? 'text-gray-300' : 'text-gray-700'
          }`}>
            <input
              type="checkbox"
              name="randomize_seed"
              checked={settings.randomize_seed}
              onChange={handleChange}
              className="mr-1"
            />
            Randomize Seed
          </label>
          {!settings.randomize_seed && (
            <input
              type="number"
              name="seed"
              value={settings.seed || 0}
              onChange={handleChange}
              className={`mt-2 w-full px-3 py-2 rounded-lg ${isDarkMode ? 'bg-gray-700 text-white' : 'bg-gray-100'}`}
            />
          )}
        </div>

        {/* Pipeline-specific settings */}
        {selectedPipeline.toLowerCase().includes('hunyaun') ? (
          // Hunyuan3D-2 specific settings
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <h4 className={`text-sm font-medium mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Hunyuan3D-2 Generation Settings
            </h4>

            {/* Quality Presets */}
            <div className="mb-4">
              <label className={`flex items-center gap-2 text-sm font-medium mb-3 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                <Zap className="w-4 h-4" />
                Quality Presets
              </label>
              <div className="grid grid-cols-1 gap-2">
                {getQualityPresets().map((preset: QualityPreset) => (
                  <button
                    key={preset.name}
                    onClick={() => applyPreset(preset.name)}
                    className={`p-3 rounded-lg border text-left transition-all ${
                      currentPreset === preset.name
                        ? isDarkMode
                          ? 'bg-blue-900/30 border-blue-600 text-blue-300'
                          : 'bg-blue-50 border-blue-300 text-blue-700'
                        : isDarkMode
                          ? 'bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700'
                          : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{preset.icon}</span>
                        <div>
                          <div className="font-medium text-sm">{preset.name}</div>
                          <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            {preset.description}
                          </div>
                        </div>
                      </div>
                      <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {preset.estimatedTime}
                      </div>
                    </div>
                  </button>
                ))}
                {currentPreset === 'Custom' && (
                  <div className={`p-3 rounded-lg border ${
                    isDarkMode
                      ? 'bg-purple-900/20 border-purple-600/30 text-purple-300'
                      : 'bg-purple-50 border-purple-300 text-purple-700'
                  }`}>
                    <div className="flex items-center gap-2">
                      <span className="text-lg">🎛️</span>
                      <div>
                        <div className="font-medium text-sm">Custom Settings</div>
                        <div className={`text-xs ${isDarkMode ? 'text-purple-400' : 'text-purple-600'}`}>
                          You've modified the settings manually
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="mb-4">
              <label className={`flex items-center gap-2 text-sm font-medium ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                <input
                  type="checkbox"
                  name="enable_texture"
                  checked={settings.enable_texture}
                  onChange={handleChange}
                  className="mr-1"
                />
                Enable Texture Generation
              </label>
              <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                Apply textures to the generated 3D model (slower but higher quality)
              </p>
            </div>

            {/* Complexity Warning */}
            {complexityWarning && (
              <div className={`mt-4 p-3 rounded-lg border ${
                complexityWarning.level === 'high'
                  ? isDarkMode ? 'bg-red-900/20 border-red-800/30' : 'bg-red-50 border-red-200'
                  : complexityWarning.level === 'medium'
                  ? isDarkMode ? 'bg-yellow-900/20 border-yellow-800/30' : 'bg-yellow-50 border-yellow-200'
                  : isDarkMode ? 'bg-blue-900/20 border-blue-800/30' : 'bg-blue-50 border-blue-200'
              }`}>
                <div className="flex items-start gap-2">
                  <span className="text-lg">
                    {complexityWarning.level === 'high' ? '⚠️' : complexityWarning.level === 'medium' ? '⏱️' : '💡'}
                  </span>
                  <div>
                    <p className={`text-sm font-medium ${
                      complexityWarning.level === 'high'
                        ? isDarkMode ? 'text-red-300' : 'text-red-700'
                        : complexityWarning.level === 'medium'
                        ? isDarkMode ? 'text-yellow-300' : 'text-yellow-700'
                        : isDarkMode ? 'text-blue-300' : 'text-blue-700'
                    }`}>
                      {complexityWarning.message}
                    </p>
                    <p className={`text-xs mt-1 ${
                      complexityWarning.level === 'high'
                        ? isDarkMode ? 'text-red-400' : 'text-red-600'
                        : complexityWarning.level === 'medium'
                        ? isDarkMode ? 'text-yellow-400' : 'text-yellow-600'
                        : isDarkMode ? 'text-blue-400' : 'text-blue-600'
                    }`}>
                      Estimated generation time: {complexityWarning.estimatedTime}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Advanced Settings Dropdown */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
              <button
                onClick={() => setIsAdvancedExpanded(!isAdvancedExpanded)}
                className={`flex items-center justify-between w-full text-sm font-medium ${
                  isDarkMode ? 'text-gray-300 hover:text-white' : 'text-gray-700 hover:text-gray-900'
                } transition-colors`}
              >
                <div className="flex items-center gap-2">
                  <Cpu className="w-4 h-4" />
                  Advanced Settings
                </div>
                {isAdvancedExpanded ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )}
              </button>
              <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                Fine-tune generation parameters for expert users
              </p>

              {isAdvancedExpanded && (
                <div className="mt-4 space-y-3">
                  {/* Core Generation Settings */}
                  <div className="mb-3">
                    <label className={`flex items-center gap-2 text-sm font-medium ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      <Sliders className="w-4 h-4" />
                      Octree Resolution
                    </label>
                    <input
                      type="range"
                      name="octree_resolution"
                      min="128"
                      max="512"
                      step="64"
                      value={settings.octree_resolution}
                      onChange={handleChange}
                      className={`w-full h-2 ${
                        isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                      } rounded-lg appearance-none cursor-pointer`}
                    />
                    <div className="flex justify-between text-xs mt-1">
                      <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.octree_resolution}</span>
                      <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Higher = more detail</span>
                    </div>
                  </div>

                  <div className="mb-3">
                    <label className={`flex items-center gap-2 text-sm font-medium ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      <Sliders className="w-4 h-4" />
                      Inference Steps
                    </label>
                    <input
                      type="range"
                      name="num_inference_steps"
                      min="10"
                      max="50"
                      step="5"
                      value={settings.num_inference_steps}
                      onChange={handleChange}
                      className={`w-full h-2 ${
                        isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                      } rounded-lg appearance-none cursor-pointer`}
                    />
                    <div className="flex justify-between text-xs mt-1">
                      <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.num_inference_steps}</span>
                      <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Higher = better quality</span>
                    </div>
                  </div>

                  <div className="mb-3">
                    <label className={`flex items-center gap-2 text-sm font-medium ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      <Sliders className="w-4 h-4" />
                      Guidance Scale
                    </label>
                    <input
                      type="range"
                      name="guidance_scale"
                      min="1.0"
                      max="15.0"
                      step="0.5"
                      value={settings.guidance_scale}
                      onChange={handleChange}
                      className={`w-full h-2 ${
                        isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                      } rounded-lg appearance-none cursor-pointer`}
                    />
                    <div className="flex justify-between text-xs mt-1">
                      <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.guidance_scale}</span>
                      <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Higher = more adherence</span>
                    </div>
                  </div>

                  {settings.enable_texture && (
                    <div className="mb-3">
                      <label className={`flex items-center gap-2 text-sm font-medium ${
                        isDarkMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        <Sliders className="w-4 h-4" />
                        Max Face Count
                      </label>
                      <input
                        type="range"
                        name="face_count"
                        min="10000"
                        max="100000"
                        step="10000"
                        value={settings.face_count}
                        onChange={handleChange}
                        className={`w-full h-2 ${
                          isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                        } rounded-lg appearance-none cursor-pointer`}
                      />
                      <div className="flex justify-between text-xs mt-1">
                        <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.face_count}</span>
                        <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Higher = more detail</span>
                      </div>
                    </div>
                  )}

                  {/* Mesh Processing Settings */}
                  <div className="mb-3">
                    <label className={`flex items-center gap-2 text-sm font-medium ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      <Sliders className="w-4 h-4" />
                      Mesh Simplification Ratio
                    </label>
                    <input
                      type="range"
                      name="mesh_simplify_ratio"
                      min="0.05"
                      max="0.5"
                      step="0.05"
                      value={settings.mesh_simplify_ratio}
                      onChange={handleChange}
                      className={`w-full h-2 ${
                        isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                      } rounded-lg appearance-none cursor-pointer`}
                    />
                    <div className="flex justify-between text-xs mt-1">
                      <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.mesh_simplify_ratio}</span>
                      <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Lower = more detail</span>
                    </div>
                    <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      Controls how much the mesh geometry is simplified (0.15 recommended)
                    </p>
                  </div>

                  <div className="mb-3">
                    <label className={`flex items-center gap-2 text-sm font-medium ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      <Sliders className="w-4 h-4" />
                      Processing Chunks
                    </label>
                    <input
                      type="range"
                      name="num_chunks"
                      min="10"
                      max="100"
                      step="10"
                      value={settings.num_chunks}
                      onChange={handleChange}
                      className={`w-full h-2 ${
                        isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                      } rounded-lg appearance-none cursor-pointer`}
                    />
                    <div className="flex justify-between text-xs mt-1">
                      <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.num_chunks}k faces</span>
                      <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Higher = more detail</span>
                    </div>
                    <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      Number of processing chunks (affects final mesh density, 30k recommended)
                    </p>
                  </div>

                  <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-blue-900/20 border border-blue-800/30' : 'bg-blue-50 border border-blue-200'}`}>
                    <p className={`text-xs ${isDarkMode ? 'text-blue-300' : 'text-blue-700'}`}>
                      <strong>💡 Tip:</strong> Current settings are balanced for stability and quality.
                      Higher values may cause crashes during texture generation.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          // Trellis specific settings
          <>
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <h4 className={`text-sm font-medium mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Trellis Generation Settings
            </h4>

            {/* Quality Presets for Trellis */}
            <div className="mb-4">
              <label className={`flex items-center gap-2 text-sm font-medium mb-3 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                <Zap className="w-4 h-4" />
                Quality Presets
              </label>
              <div className="grid grid-cols-1 gap-2">
                {getQualityPresets().map((preset: QualityPreset) => (
                  <button
                    key={preset.name}
                    onClick={() => applyPreset(preset.name)}
                    className={`p-3 rounded-lg border text-left transition-all ${
                      currentPreset === preset.name
                        ? isDarkMode
                          ? 'bg-blue-900/30 border-blue-600 text-blue-300'
                          : 'bg-blue-50 border-blue-300 text-blue-700'
                        : isDarkMode
                          ? 'bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700'
                          : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{preset.icon}</span>
                        <div>
                          <div className="font-medium text-sm">{preset.name}</div>
                          <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            {preset.description}
                          </div>
                        </div>
                      </div>
                      <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {preset.estimatedTime}
                      </div>
                    </div>
                  </button>
                ))}
                {currentPreset === 'Custom' && (
                  <div className={`p-3 rounded-lg border ${
                    isDarkMode
                      ? 'bg-purple-900/20 border-purple-600/30 text-purple-300'
                      : 'bg-purple-50 border-purple-300 text-purple-700'
                  }`}>
                    <div className="flex items-center gap-2">
                      <span className="text-lg">🎛️</span>
                      <div>
                        <div className="font-medium text-sm">Custom Settings</div>
                        <div className={`text-xs ${isDarkMode ? 'text-purple-400' : 'text-purple-600'}`}>
                          You've modified the settings manually
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Advanced Settings Dropdown for Trellis */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
              <button
                onClick={() => setIsAdvancedExpanded(!isAdvancedExpanded)}
                className={`flex items-center justify-between w-full text-sm font-medium ${
                  isDarkMode ? 'text-gray-300 hover:text-white' : 'text-gray-700 hover:text-gray-900'
                } transition-colors`}
              >
                <div className="flex items-center gap-2">
                  <Cpu className="w-4 h-4" />
                  Advanced Settings
                </div>
                {isAdvancedExpanded ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )}
              </button>
              <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                Fine-tune Trellis generation parameters for expert users
              </p>

              {isAdvancedExpanded && (
                <div className="mt-4 space-y-4">
                  {/* Stage 1: Sparse Structure Generation */}
                  <div>
                    <h4 className={`text-sm font-medium mb-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Stage 1: Sparse Structure Generation
                    </h4>

                    <div className="mb-3">
                      <label className={`flex items-center gap-2 text-sm font-medium ${
                        isDarkMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        <Sliders className="w-4 h-4" />
                        Guidance Strength
                      </label>
                      <input
                        type="range"
                        name="ss_cfg_strength"
                        min="0"
                        max="10"
                        step="0.1"
                        value={settings.ss_cfg_strength}
                        onChange={handleChange}
                        className={`w-full h-2 ${
                          isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                        } rounded-lg appearance-none cursor-pointer`}
                      />
                      <div className="flex justify-between text-xs mt-1">
                        <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.ss_cfg_strength}</span>
                      </div>
                    </div>

                    <div className="mb-3">
                      <label className={`flex items-center gap-2 text-sm font-medium ${
                        isDarkMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        <Sliders className="w-4 h-4" />
                        Sampling Steps
                      </label>
                      <input
                        type="range"
                        name="ss_steps"
                        min="1"
                        max="50"
                        step="1"
                        value={settings.ss_steps}
                        onChange={handleChange}
                        className={`w-full h-2 ${
                          isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                        } rounded-lg appearance-none cursor-pointer`}
                      />
                      <div className="flex justify-between text-xs mt-1">
                        <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.ss_steps}</span>
                      </div>
                    </div>
                  </div>

                  {/* Stage 2: Structured Latent Generation */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <h4 className={`text-sm font-medium mb-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Stage 2: Structured Latent Generation
                    </h4>

                    <div className="mb-3">
                      <label className={`flex items-center gap-2 text-sm font-medium ${
                        isDarkMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        <Sliders className="w-4 h-4" />
                        Guidance Strength
                      </label>
                      <input
                        type="range"
                        name="slat_cfg_strength"
                        min="0"
                        max="10"
                        step="0.1"
                        value={settings.slat_cfg_strength}
                        onChange={handleChange}
                        className={`w-full h-2 ${
                          isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                        } rounded-lg appearance-none cursor-pointer`}
                      />
                      <div className="flex justify-between text-xs mt-1">
                        <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.slat_cfg_strength}</span>
                      </div>
                    </div>

                    <div className="mb-3">
                      <label className={`flex items-center gap-2 text-sm font-medium ${
                        isDarkMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        <Sliders className="w-4 h-4" />
                        Sampling Steps
                      </label>
                      <input
                        type="range"
                        name="slat_steps"
                        min="1"
                        max="50"
                        step="1"
                        value={settings.slat_steps}
                        onChange={handleChange}
                        className={`w-full h-2 ${
                          isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                        } rounded-lg appearance-none cursor-pointer`}
                      />
                      <div className="flex justify-between text-xs mt-1">
                        <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.slat_steps}</span>
                      </div>
                    </div>
                  </div>

                  {/* Output Settings */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <h4 className={`text-sm font-medium mb-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Output Settings
                    </h4>

                    <div className="mb-4">
                      <label className={`flex items-center gap-2 text-sm font-medium ${
                        isDarkMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        <input
                          type="checkbox"
                          name="enable_lighting_optimizer"
                          checked={settings.enable_lighting_optimizer}
                          onChange={handleChange}
                          className="mr-1"
                        />
                        Enable Lighting Optimizer
                      </label>
                      <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        Reduces dark areas and shadows in the 3D model for better lighting
                      </p>
                    </div>

                    <div className="mb-3">
                      <label className={`flex items-center gap-2 text-sm font-medium ${
                        isDarkMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        <Sliders className="w-4 h-4" />
                        Mesh Simplification
                      </label>
                      <input
                        type="range"
                        name="simplify"
                        min="0.9"
                        max="0.98"
                        step="0.01"
                        value={settings.simplify}
                        onChange={handleChange}
                        className={`w-full h-2 ${
                          isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                        } rounded-lg appearance-none cursor-pointer`}
                      />
                      <div className="flex justify-between text-xs mt-1">
                        <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{settings.simplify}</span>
                      </div>
                    </div>

                    <div className="mb-3">
                      <label className={`flex items-center gap-2 text-sm font-medium ${
                        isDarkMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        <Sliders className="w-4 h-4" />
                        Texture Size
                      </label>
                      <select
                        name="texture_size"
                        value={settings.texture_size}
                        onChange={handleChange}
                        className={`w-full px-3 py-2 rounded-lg ${isDarkMode ? 'bg-gray-700 text-white' : 'bg-gray-100'}`}
                      >
                        <option value="512">512 x 512</option>
                        <option value="1024">1024 x 1024</option>
                        <option value="2048">2048 x 2048</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          </>
        )}
        </div>
      )}
    </div>
  );
};